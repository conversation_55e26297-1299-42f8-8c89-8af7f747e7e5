import ForgotPasswordForm from '@/components/forms/ForgotPasswordForm';
import Link from 'next/link';

export default function ForgotPasswordPage() {
  return (
    <div>
      <div className="mb-6 text-center">
        <h1 className="font-nunito mb-2 text-2xl font-bold text-gray-800 dark:text-gray-100">
          Forgot Password
        </h1>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Enter your email address and we&apos;ll send you a link to reset your
          password
        </p>
      </div>
      <ForgotPasswordForm />

      <div className="mt-6 space-y-3 text-center">
        <Link
          href="/sign-in"
          className="text-sm text-gray-500 transition-colors hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
        >
          <span className="cursor-pointer font-medium text-blue-600 dark:text-blue-400">
            ← Back to sign in
          </span>
        </Link>
      </div>
    </div>
  );
}
