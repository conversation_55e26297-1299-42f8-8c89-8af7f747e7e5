import Link from 'next/link';
import Image from 'next/image';

export default function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="font-urbanist relative min-h-screen overflow-hidden bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* 🌀 Animated Background Blobs */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 h-64 w-64 animate-pulse rounded-full bg-blue-200/30 blur-3xl dark:bg-blue-500/20" />
        <div className="absolute right-1/4 bottom-1/4 h-64 w-64 animate-pulse rounded-full bg-purple-200/30 blur-3xl delay-1000 dark:bg-purple-500/20" />
        <div className="absolute top-3/4 left-1/2 h-48 w-48 animate-pulse rounded-full bg-pink-200/30 blur-3xl delay-500 dark:bg-pink-500/20" />
      </div>

      {/* 🔗 Top Logo Link */}
      <Link
        href="/"
        aria-label="Home"
        className="absolute top-4 left-4 z-10 flex items-center space-x-2"
      >
        <Image
          src="/logo/white-logo.png"
          alt="Sudha Software Solution Logo"
          width={300}
          height={100}
          className="h-8 w-auto dark:hidden"
        />
        <Image
          src="/logo/dark-logo.png"
          alt="Sudha Software Solution Logo"
          width={300}
          height={100}
          className="hidden h-8 w-auto dark:block"
        />
      </Link>

      {/* 🧊 Auth Form Wrapper */}
      <main className="relative z-10 flex min-h-screen items-center justify-center p-4">
        <div className="w-full max-w-md">
          <div className="relative">
            {/* Gradient Shadow */}
            <div className="absolute -inset-2 rounded-3xl bg-gradient-to-r from-blue-200 via-purple-200 to-pink-200 opacity-20 blur dark:from-blue-500/30 dark:via-purple-500/30 dark:to-pink-500/30" />

            {/* Glass Card */}
            <div className="relative rounded-3xl border border-white/50 bg-white/80 p-6 shadow-2xl backdrop-blur-xl dark:border-gray-700/50 dark:bg-gray-800/80">
              {children}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
