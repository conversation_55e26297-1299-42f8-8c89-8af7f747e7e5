// import SocialLoginButtons from '@/components/forms/SocialLoginButtons';
import SignInForm from '@/components/forms/SignInForm';
import SocialLoginButtons from '@/components/forms/SocialLoginButtons';
import Link from 'next/link';

export default function SignInPage() {
  return (
    <>
      <div className="mb-6 text-center">
        <h1 className="font-nunito mb-2 text-2xl font-bold text-gray-800 dark:text-gray-100">
          Welcome to{' '}
          <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
            Sudha Software Solutions
          </span>
        </h1>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Sign in to your account
        </p>
      </div>

      <SocialLoginButtons />

      <SignInForm />

      <div className="mt-6 space-y-3 text-center">
        <Link
          href="/sign-up"
          className="text-sm text-gray-500 transition-colors hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
        >
          Don&apos;t have an account?{' '}
          <span className="cursor-pointer font-medium text-blue-600 dark:text-blue-400">
            Sign up
          </span>
        </Link>
      </div>
    </>
  );
}
