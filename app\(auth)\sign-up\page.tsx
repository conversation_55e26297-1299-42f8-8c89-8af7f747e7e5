// import SocialLoginButtons from '@/components/forms/SocialLoginButtons';
import SignUpForm from '@/components/forms/SignUpForm';
import Link from 'next/link';

export default function SignUpPage() {
  // const handleSocialLogin = (provider: 'google' | 'github' | 'facebook') => {
  //   // Implement your social login logic here
  //   console.log(`Social login with ${provider}`);
  // };

  return (
    <>
      <div className="mb-6 text-center">
        <h1 className="font-nunito mb-2 text-2xl font-bold text-gray-800 dark:text-gray-100">
          Join{' '}
          <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
            Sudha Software Solutions
          </span>
        </h1>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Create your account to get started
        </p>
      </div>

      {/* <SocialLoginButtons
        onSocialLogin={handleSocialLogin}
        isLoading={false}
        isSignUp={true}
      /> */}

      <SignUpForm />
      <div className="mt-4 text-center">
        <p className="text-xs text-gray-500 dark:text-gray-400">
          By creating an account, you agree to our{' '}
          <Link
            href="#"
            className="text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300"
          >
            Terms
          </Link>{' '}
          and{' '}
          <Link
            href="#"
            className="text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300"
          >
            Privacy Policy
          </Link>
        </p>
      </div>
      <div className="mt-6 space-y-3 text-center">
        <Link
          href="/sign-in"
          className="text-sm text-gray-500 transition-colors hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
        >
          Already have an account?{' '}
          <span className="cursor-pointer font-medium text-blue-600 dark:text-blue-400">
            Sign in
          </span>
        </Link>
      </div>
    </>
  );
}
