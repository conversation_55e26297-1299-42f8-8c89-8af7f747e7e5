'use client';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import React from 'react';

import { useGetMySkillsQuery } from '@/store/features/userApi';
import { Atom, History, Pencil } from 'lucide-react';
import EditSkillForm from '@/components/forms/EditSkillForm';
import AddSkillForm from '@/components/forms/AddSkillForm';
import Image from 'next/image';
import { Badge } from '@/components/ui/badge';

const SkillPage = () => {
  const { data, isLoading, isError } = useGetMySkillsQuery();
  console.log(data, isLoading, isError);
  if (isLoading) {
    return <div>Loading...</div>;
  }
  return (
    <div className="flex flex-col gap-5">
      <Card className="pb-0">
        <CardHeader className="flex flex-row flex-wrap justify-between border-b">
          <div className="">
            <CardTitle className="flex text-xl">
              {' '}
              <Image
                src="/images/icons/skill.png"
                alt="Skill"
                width="50"
                height="50"
                className="mr-2 inline-block h-6 w-6"
              />
              <span>Skill</span>
            </CardTitle>
            <CardDescription>
              Your personal information is the story of who you are.
            </CardDescription>
          </div>

          <Dialog>
            <DialogTrigger asChild>
              <Button variant="outline" className="w-fit">
                Add Skill
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl">
              <DialogHeader>
                <DialogTitle>Add Skill </DialogTitle>
                <DialogDescription>
                  Your personal information is the story of who you are.
                </DialogDescription>

                <AddSkillForm />
              </DialogHeader>
            </DialogContent>
          </Dialog>
        </CardHeader>
        <CardContent className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {data?.data.map(skill => (
            <Card
              key={skill._id}
              className="flex flex-col rounded-2xl border bg-white shadow-lg dark:bg-gray-800"
            >
              <CardHeader className="flex flex-row flex-wrap items-center justify-between border-b pb-0">
                <CardTitle className="flex items-center">
                  <Image
                    src="/images/icons/skill.png"
                    alt="Skill"
                    width="50"
                    height="50"
                    className="mr-2 inline-block h-6 w-6"
                  />
                  <span className="text-xl">{skill.name}</span>
                </CardTitle>

                <Dialog>
                  <DialogTrigger asChild>
                    <Button variant="outline" size="icon">
                      <Pencil />
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-4xl">
                    <DialogHeader>
                      <DialogTitle>Edit Skill </DialogTitle>
                      <DialogDescription>
                        Your personal information is the story of who you are.
                      </DialogDescription>

                      <EditSkillForm skill={skill} />
                    </DialogHeader>
                  </DialogContent>
                </Dialog>
              </CardHeader>
              <CardContent>
                <Badge variant="secondary" className="capitalize">
                  {' '}
                  {skill.level}
                </Badge>

                <div className="mt-5 space-y-2 text-sm text-gray-600 dark:text-gray-400">
                  <p>🕒 {skill.monthsOfExperience} months of experience</p>

                  <p>
                    <History />
                    {new Date(skill.updatedAt).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'short',
                      day: 'numeric',
                    })}
                  </p>
                </div>
              </CardContent>
            </Card>
          ))}
        </CardContent>
      </Card>
    </div>
  );
};

export default SkillPage;
