'use client';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { useAppSelector } from '@/store';
import { Mail, Phone } from 'lucide-react';
import Link from 'next/link';
import React from 'react';
import { format } from 'date-fns';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import EditPersonalDetailForm from '@/components/forms/EditPersonalDetailForm';
import { InfoField } from '@/components/ui/info-field';
import { useGetUserByIdQuery } from '@/store/features/userApi';

const ViewProfilePage = () => {
  const user = useAppSelector(state => state.auth?.user);

  const { data, isLoading, isError } = useGetUserByIdQuery(user?._id || '', {
    skip: !user?._id,
  });
  console.log(data, isLoading, isError);
  if (isLoading) {
    return <div>Loading...</div>;
  }
  return (
    <div className="flex flex-col gap-5">
      <Card className="bg-gray-50">
        <div className="flex flex-row items-center gap-3 px-4 text-center">
          <Avatar className="h-24 w-24 rounded-full border-4 border-white bg-red-50">
            <AvatarImage src="https://github.com/shadcn.png" alt="@shadcn" />
            <AvatarFallback>CN</AvatarFallback>
          </Avatar>{' '}
          <div className="flex flex-1 flex-col space-y-2.5 text-start">
            <h2 className="font-primary text-3xl font-bold">
              {data?.data.firstName} {data?.data.lastName}
            </h2>
            <div className="flex items-center gap-5">
              <div className="flex items-center gap-2 text-sm">
                {' '}
                <Mail className="size-4" />
                {data?.data?.email}
              </div>
              <div className="flex items-center gap-2 text-sm">
                {' '}
                <Phone className="size-3.5" />
                {data?.data?.phone}
              </div>
            </div>
          </div>
          <Link
            href={'/dashboard/account/edit-profile'}
            className='font-primary hover:shadow-xl" items-center justify-center rounded-md bg-gradient-to-r from-blue-600 to-purple-600 px-3 py-2 text-xs font-semibold text-white shadow-lg transition-all duration-300 hover:-translate-y-1 hover:scale-105 hover:from-blue-700 hover:to-purple-700'
          >
            Edit Profile
          </Link>
        </div>
      </Card>

      <Card className="pb-0">
        <CardHeader className="flex flex-row flex-wrap justify-between border-b">
          <div className="">
            <CardTitle>Personal Information</CardTitle>
            <CardDescription>
              Your personal information is the story of who you are.
            </CardDescription>
          </div>

          <Dialog>
            <DialogTrigger asChild>
              <Button variant="outline" className="w-fit">
                Edit Personal Info
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl">
              <DialogHeader>
                <DialogTitle>Edit Personal Detail </DialogTitle>
                <DialogDescription>
                  Your personal information is the story of who you are.
                </DialogDescription>

                <EditPersonalDetailForm />
              </DialogHeader>
            </DialogContent>
          </Dialog>
        </CardHeader>
        <CardContent className="grid grid-cols-1 gap-2 gap-x-10 pb-4 md:grid-cols-2 md:gap-3.5 lg:grid-cols-3">
          <InfoField title="Email" value={data?.data?.email} />
          <InfoField title="Phone" value={data?.data?.phone} />
          <InfoField
            title="Date of Birth"
            value={
              data?.data?.dob
                ? format(new Date(data?.data?.dob), 'MMM d, yyyy')
                : 'N/A'
            }
          />
          <InfoField
            title="Status"
            value={data?.data?.status}
            className="capitalize"
          />
          <InfoField title="Father's Name" value={data?.data?.fatherName} />
          <InfoField title="Mother's Name" value={data?.data?.motherName} />
          <InfoField
            title="Marital Status"
            className="capitalize"
            value={data?.data?.maritalStatus}
          />
        </CardContent>
      </Card>

      <Card className="">
        <CardHeader className="border-b">
          <CardTitle>Addresses</CardTitle>
          <CardDescription>
            Your address is not just a location; it&apos;s the gateway to
            cherished memories.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <InfoField
            title="Permanent Address"
            value={'Domchanch, Koderma, Jharkhand, India - 825418'}
          />
          <InfoField
            title="Present Address"
            value={'Domchanch, Koderma, Jharkhand, India - 825418'}
          />
        </CardContent>
      </Card>
    </div>
  );
};

export default ViewProfilePage;
