"use client";

import React, { useState } from 'react';
import { useGetMyInternshipApplicationsQuery } from '@/store/internshipApplication-feature/applicationApi';
import { IInternshipApplication } from '@/types/IInternshipApplication';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Eye, Calendar, User, Building2, MapPin, UserCheck, Briefcase } from 'lucide-react';
import { useAppSelector } from '@/store';
import { format } from 'date-fns';
import { useRouter } from 'next/navigation';

const InternshipApplicationsPage = () => {
  const router = useRouter();
  const [currentPage, setCurrentPage] = useState(1);
  const limit = 10;

  const currentUser = useAppSelector(state => state.auth.user);

  const {
    data,
    isLoading,
    isError,
    error
  } = useGetMyInternshipApplicationsQuery({
    page: currentPage,
    limit,
  });

  const applications = data?.data?.applications || [];
  console.log("Applications" , applications);
  
  const totalPages = data?.data?.totalPages || 1;
  const totalCount = data?.data?.totalApplications || 0;

  const getStatusColor = (status: string) => {
    const colors = {
      'draft': 'bg-gray-100 text-gray-800',
      'applied': 'bg-blue-100 text-blue-800',
      'under-review': 'bg-yellow-100 text-yellow-800',
      'shortlisted': 'bg-purple-100 text-purple-800',
      'interview': 'bg-orange-100 text-orange-800',
      'offered': 'bg-green-100 text-green-800',
      'hired': 'bg-emerald-100 text-emerald-800',
      'rejected': 'bg-red-100 text-red-800',
      'withdrawn': 'bg-gray-100 text-gray-800',
    };
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'MMM dd, yyyy');
  };

  const getApplicantName = (applicant: any) => {
    if (typeof applicant === 'object') {
      if (applicant?.firstName && applicant?.lastName) {
        return `${applicant.firstName} ${applicant.lastName}`;
      }
      if (applicant?.firstName) return applicant.firstName;
      if (applicant?.lastName) return applicant.lastName;
      if (applicant?.email) return applicant.email;
    }
    return 'Unknown Applicant';
  };

  const getInternshipTitle = (internship: any) => {
    if (typeof internship === 'object' && internship?.title) {
      return internship.title;
    }
    return 'Unknown Internship';
  };

  const getCompanyName = (internship: any) => {
    if (typeof internship === 'object' && internship?.company) {
      if (typeof internship.company === 'object' && internship.company?.name) {
        return internship.company.name;
      }
      if (typeof internship.company === 'string') {
        return internship.company;
      }
    }
    return 'Unknown Company';
  };

  const getLocation = (internship: any) => {
    if (typeof internship === 'object' && internship?.location) {
      return `${internship.location.city}, ${internship.location.state}`;
    }
    return 'Unknown Location';
  };

  const getUserRole = (application: IInternshipApplication) => {
    if (!currentUser?._id) return 'unknown';

    // Check if current user is the applicant
    const applicantId = typeof application.applicant === 'object'
      ? application.applicant._id
      : application.applicant;

    if (applicantId === currentUser._id) {
      return 'applicant';
    }

    // If not applicant, assume they're viewing as employer
    return 'employer';
  };

  if (isLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2">Loading applications...</span>
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="container mx-auto p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h3 className="text-red-800 font-medium">Error loading applications</h3>
          <p className="text-red-600 text-sm mt-1">
            {(error as any)?.data?.message || 'Failed to load applications. Please try again.'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">My Internship Applications</h1>
        <p className="text-gray-600">View and manage your internship applications and applications to your posted internships</p>
      </div>



      {/* Results Summary */}
      <div className="mb-4">
        <p className="text-sm text-gray-600">
          Showing {applications.length} of {totalCount} applications
        </p>
      </div>

      {/* Applications List */}
      <div className="space-y-4">
        {applications.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <div className="text-gray-400 mb-4">
                <User className="h-12 w-12 mx-auto" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No applications found</h3>
              <p className="text-gray-600">No internship applications match your current filters.</p>
            </CardContent>
          </Card>
        ) : (
          applications.map((application: IInternshipApplication) => (
            <Card key={application._id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                  {/* Main Info */}
                  <div className="flex-1">
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-1">
                          {getInternshipTitle(application.Internship)}
                        </h3>
                        <div className="flex items-center text-sm text-gray-600 space-x-4">
                          <div className="flex items-center">
                            <Building2 className="h-4 w-4 mr-1" />
                            {getCompanyName(application.Internship)}
                          </div>
                          <div className="flex items-center">
                            <MapPin className="h-4 w-4 mr-1" />
                            {getLocation(application.Internship)}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge
                          variant="outline"
                          className={getUserRole(application) === 'applicant' ? 'bg-blue-50 text-blue-700 border-blue-200' : 'bg-green-50 text-green-700 border-green-200'}
                        >
                          {getUserRole(application) === 'applicant' ? (
                            <>
                              <UserCheck className="h-3 w-3 mr-1" />
                              My Application
                            </>
                          ) : (
                            <>
                              <Briefcase className="h-3 w-3 mr-1" />
                              As Employer
                            </>
                          )}
                        </Badge>
                        <Badge className={getStatusColor(application.status)}>
                          {application.status.replace('-', ' ').toUpperCase()}
                        </Badge>
                      </div>
                    </div>

                    <div className="flex items-center text-sm text-gray-600 space-x-4">
                      <div className="flex items-center">
                        <User className="h-4 w-4 mr-1" />
                        <span className="font-medium">{getApplicantName(application.applicant)}</span>
                      </div>
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-1" />
                        Applied on {formatDate(application.createdAt)}
                      </div>
                    </div>

                    {application.coverLetter && (
                      <div className="mt-3">
                        <p className="text-sm text-gray-700 line-clamp-2">
                          <span className="font-medium">Cover Letter:</span> {application.coverLetter}
                        </p>
                      </div>
                    )}
                  </div>

                  {/* Actions */}
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => router.push(`/dashboard/applications/internship-applications/${application._id}`)}
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      View Details
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="mt-6 flex items-center justify-center space-x-2">
          <Button
            variant="outline"
            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
          >
            Previous
          </Button>

          <div className="flex items-center space-x-1">
            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
              <Button
                key={page}
                variant={currentPage === page ? "default" : "outline"}
                size="sm"
                onClick={() => setCurrentPage(page)}
                className="w-10"
              >
                {page}
              </Button>
            ))}
          </div>

          <Button
            variant="outline"
            onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
            disabled={currentPage === totalPages}
          >
            Next
          </Button>
        </div>
      )}
    </div>
  );
};

export default InternshipApplicationsPage;