"use client";

import React from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { useGetMyJobApplicationsQuery } from '@/store/jobApplication-feature/jobApplicationApi';
import { useGetUserByIdQuery } from '@/store/features/userApi';

// Extended interface to handle the actual data structure
interface ExtendedJobApplication {
  _id: string;
  job: any; // More flexible to handle the full job object
  applicant: any; // More flexible to handle the full applicant object
  coverLetter: string;
  answers: Array<{
    questionId: number;
    answer: string | string[];
  }>;
  status: string;
  reviewLogs: any[];
  interviews: any[];
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
}
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import {
  ArrowLeft,
  Calendar,
  User,
  Building2,
  MapPin,
  DollarSign,
  Clock,
  FileText,
  Mail,
  Phone,
  Briefcase,
  GraduationCap,
  Award,
  Globe,
  Star
} from 'lucide-react';
import { format } from 'date-fns';
import { useAppSelector } from '@/store';

const JobApplicationDetailsPage = () => {
  const params = useParams();
  const router = useRouter();
  const applicationId = params.id as string;
  const currentUser = useAppSelector(state => state.auth.user);
  console.log("Current user:", currentUser);
  console.log("Is user authenticated:", !!currentUser);

  // Since the API now expects jobId, we need to get all applications first
  // and then find the specific application by applicationId
  const {
    data: applicationData,
    isLoading: applicationLoading,
    isError: applicationError
  } = useGetMyJobApplicationsQuery();

  console.log("API Loading:", applicationLoading);
  console.log("API Error:", applicationError);

  // Find the specific application by ID from the user's applications
  console.log("Full applicationData:", applicationData);
  console.log("applicationData.data:", applicationData?.data);
  console.log("jobApplications:", applicationData?.data?.jobApplications);

  const application = applicationData?.data?.jobApplications?.find(
    (app) => app._id === applicationId
  ) as ExtendedJobApplication | undefined;
  console.log("Looking for applicationId:", applicationId);
  console.log("Found application:", application);
  console.log("Job company:", typeof application?.job === 'object' ? (application.job as any).company : 'Job is string');
  console.log("Job location:", typeof application?.job === 'object' ? (application.job as any).location : 'Job is string');


  // Extract the actual application data from the API response
  const finalApplication = application

  console.log("Final application - for job:", finalApplication);
  

  // Get applicant details if current user is not the applicant
  const applicantId = typeof finalApplication?.applicant === 'object'
    ? finalApplication.applicant._id
    : finalApplication?.applicant;

  console.log("applicantId", applicantId);

  const shouldFetchApplicantDetails = applicantId && applicantId !== currentUser?._id;

  const {
    data: applicantData,
    isLoading: applicantLoading,
    error: applicantError
  } = useGetUserByIdQuery(applicantId || '', {
    skip: !shouldFetchApplicantDetails || !applicantId
  });

  console.log('User query error:', applicantError);
  console.log('User query loading:', applicantLoading);

  // Get applicant details - prioritize fetched data, then current user, then create basic info from application
  const applicantDetails = shouldFetchApplicantDetails
    ? applicantData?.data
    : currentUser
    ? currentUser
    : (typeof finalApplication?.applicant === 'object' ? finalApplication.applicant : null);

  // Debug logs to check the structure
  console.log('shouldFetchApplicantDetails:', shouldFetchApplicantDetails);
  console.log('applicantData:', applicantData);
  console.log('currentUser:', currentUser);
  console.log('Final applicantDetails:', applicantDetails);

  const getStatusColor = (status: string) => {
    const colors = {
      'draft': 'bg-gray-100 text-gray-800',
      'applied': 'bg-blue-100 text-blue-800',
      'under-review': 'bg-yellow-100 text-yellow-800',
      'shortlisted': 'bg-purple-100 text-purple-800',
      'interview': 'bg-orange-100 text-orange-800',
      'offered': 'bg-green-100 text-green-800',
      'hired': 'bg-emerald-100 text-emerald-800',
      'rejected': 'bg-red-100 text-red-800',
      'withdrawn': 'bg-gray-100 text-gray-800',
    };
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM dd, yyyy');
    } catch {
      return 'Invalid date';
    }
  };

  const formatDateRange = (startDate: string, endDate?: string) => {
    try {
      const start = startDate ? format(new Date(startDate), 'MMM yyyy') : 'Unknown';
      const end = endDate ? format(new Date(endDate), 'MMM yyyy') : 'Present';
      return `${start} - ${end}`;
    } catch {
      return 'Date range not available';
    }
  };

  const getJobTitle = (job: any) => {
    return typeof job === 'object' ? job.title : 'Job Title Not Available';
  };

  const getCompanyName = (job: any) => {
    if (typeof job === 'object' && job.company) {
      // If company is an object, get the name property
      if (typeof job.company === 'object' && job.company.name) {
        return job.company.name;
      }
      // If company is a string, return it directly
      if (typeof job.company === 'string') {
        return job.company;
      }
    }
    return 'Company Not Available';
  };

  const getLocation = (job: any) => {
    if (typeof job === 'object' && job.location) {
      // If location is an object, try to build a location string
      if (typeof job.location === 'object') {
        const parts = [];
        if (job.location.city) parts.push(job.location.city);
        if (job.location.state) parts.push(job.location.state);
        if (job.location.country) parts.push(job.location.country);
        return parts.length > 0 ? parts.join(', ') : 'Location Available';
      }
      // If location is a string, return it directly
      if (typeof job.location === 'string') {
        return job.location;
      }
    }
    return 'Location Not Available';
  };

  const getUserRole = () => {
    if (!currentUser?._id || !finalApplication) return 'unknown';

    const applicantId = typeof finalApplication.applicant === 'object'
      ? finalApplication.applicant._id
      : finalApplication.applicant;

    return applicantId === currentUser._id ? 'applicant' : 'employer';
  };

  if (applicationLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2">Loading application details...</span>
        </div>
      </div>
    );
  }

  if (applicationError) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Error Loading Application</h1>
          <p className="text-gray-600 mb-4">There was an error loading the application details. Please try again.</p>
          <Button onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Go Back
          </Button>
        </div>
      </div>
    );
  }

  if (!applicationLoading && !finalApplication) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Application Not Found</h1>
          <p className="text-gray-600 mb-4">
            The application you're looking for doesn't exist or you don't have permission to view it.
          </p>
          <div className="bg-gray-100 p-4 rounded-lg mb-4">
            <p className="text-sm text-gray-600">
              <strong>Application ID:</strong> {applicationId}
            </p>
            <p className="text-sm text-gray-600">
              <strong>Available Applications:</strong> {applicationData?.data?.jobApplications?.length || 0}
            </p>
          </div>
          <Button onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Go Back
          </Button>
        </div>
      </div>
    );
  }

  // Early return if application is not loaded yet
  if (!finalApplication) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2">Loading application details...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      {/* Header */}
      <div className="mb-6">
        <Button 
          variant="ghost" 
          onClick={() => router.back()}
          className="mb-4"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Applications
        </Button>
        
        <div className="flex items-start justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Application Details
            </h1>
            <div className="flex items-center space-x-4">
              <Badge className={getStatusColor(finalApplication.status)}>
                {/* {finalApplication.status.replace('-', ' ').toUpperCase()} */}
                {finalApplication.status}
              </Badge>
              <Badge variant="outline">
                {getUserRole() === 'applicant' ? 'Your Application' : 'Received Application'}
              </Badge>
            </div>
          </div>
          <div className="text-right text-sm text-gray-600">
            <p>Applied on {formatDate(finalApplication.createdAt)}</p>
            {finalApplication.updatedAt !== finalApplication.createdAt && (
              <p>Updated on {formatDate(finalApplication.updatedAt)}</p>
            )}
          </div>
        </div>
      </div>

      <div className="space-y-6">
        {/* Job Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Briefcase className="h-5 w-5 mr-2" />
              Job Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                {getJobTitle(finalApplication.job)}
              </h3>
              <div className="flex items-center space-x-4 text-sm text-gray-600">
                <span className="flex items-center">
                  <Building2 className="h-4 w-4 mr-1" />
                  {getCompanyName(finalApplication.job)}
                </span>
                <span className="flex items-center">
                  <MapPin className="h-4 w-4 mr-1" />
                  {getLocation(finalApplication.job)}
                </span>
              </div>
            </div>

            {typeof finalApplication.job === 'object' && (
              <>
                {/* Job Description */}
                {finalApplication.job.description && (
                  <div className="pt-4">
                    <h4 className="font-semibold text-gray-900 mb-2">Job Description</h4>
                    <p className="text-gray-700 leading-relaxed">{finalApplication.job.description}</p>
                  </div>
                )}

                {/* Job Details Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4">
                  {finalApplication.job.salaryRange && (
                    <div className="flex items-center">
                      <DollarSign className="h-4 w-4 mr-2 text-gray-500" />
                      <div>
                        <p className="text-sm text-gray-600">Salary Range</p>
                        <p className="font-medium">{finalApplication.job.salaryRange}</p>
                      </div>
                    </div>
                  )}
                  {finalApplication.job.workingHours && (
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 mr-2 text-gray-500" />
                      <div>
                        <p className="text-sm text-gray-600">Working Hours</p>
                        <p className="font-medium">{finalApplication.job.workingHours}</p>
                      </div>
                    </div>
                  )}
                  {finalApplication.job.workPlaceType && (
                    <div className="flex items-center">
                      <Briefcase className="h-4 w-4 mr-2 text-gray-500" />
                      <div>
                        <p className="text-sm text-gray-600">Work Type</p>
                        <p className="font-medium capitalize">{finalApplication.job.workPlaceType}</p>
                      </div>
                    </div>
                  )}
                  {finalApplication.job.mode && (
                    <div className="flex items-center">
                      <Globe className="h-4 w-4 mr-2 text-gray-500" />
                      <div>
                        <p className="text-sm text-gray-600">Work Mode</p>
                        <p className="font-medium capitalize">{finalApplication.job.mode}</p>
                      </div>
                    </div>
                  )}
                  {finalApplication.job.openings && (
                    <div className="flex items-center">
                      <User className="h-4 w-4 mr-2 text-gray-500" />
                      <div>
                        <p className="text-sm text-gray-600">Openings</p>
                        <p className="font-medium">{finalApplication.job.openings} position{finalApplication.job.openings > 1 ? 's' : ''}</p>
                      </div>
                    </div>
                  )}
                  {finalApplication.job.applicationDeadline && (
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                      <div>
                        <p className="text-sm text-gray-600">Application Deadline</p>
                        <p className="font-medium">{formatDate(finalApplication.job.applicationDeadline)}</p>
                      </div>
                    </div>
                  )}
                </div>

                {/* Working Days */}
                {finalApplication.job.workingDays && finalApplication.job.workingDays.length > 0 && (
                  <div className="pt-4">
                    <h4 className="font-semibold text-gray-900 mb-2">Working Days</h4>
                    <div className="flex flex-wrap gap-2">
                      {finalApplication.job.workingDays.map((day: string, index: number) => (
                        <Badge key={index} variant="outline">{day}</Badge>
                      ))}
                    </div>
                  </div>
                )}

                {/* Skills Required */}
                {finalApplication.job.skills && finalApplication.job.skills.length > 0 && (
                  <div className="pt-4">
                    <h4 className="font-semibold text-gray-900 mb-2">Skills Required</h4>
                    <div className="space-y-2">
                      {finalApplication.job.skills.map((skill: any, index: number) => (
                        <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                          <span className="font-medium">{skill.skill}</span>
                          <Badge variant={skill.level === 'expert' ? 'default' : skill.level === 'advanced' ? 'secondary' : 'outline'}>
                            {skill.level}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Responsibilities */}
                {finalApplication.job.responsibilities && finalApplication.job.responsibilities.length > 0 && (
                  <div className="pt-4">
                    <h4 className="font-semibold text-gray-900 mb-2">Key Responsibilities</h4>
                    <ul className="list-disc list-inside space-y-1 text-gray-700">
                      {finalApplication.job.responsibilities.map((responsibility: any, index: number) => (
                        <li key={index}>{responsibility.title}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Qualifications */}
                {finalApplication.job.qualification && finalApplication.job.qualification.length > 0 && (
                  <div className="pt-4">
                    <h4 className="font-semibold text-gray-900 mb-2">Qualifications</h4>
                    <ul className="list-disc list-inside space-y-1 text-gray-700">
                      {finalApplication.job.qualification.map((qual: string, index: number) => (
                        <li key={index}>{qual}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Eligibility Criteria */}
                {finalApplication.job.eligibilityCriteria && finalApplication.job.eligibilityCriteria.length > 0 && (
                  <div className="pt-4">
                    <h4 className="font-semibold text-gray-900 mb-2">Eligibility Criteria</h4>
                    <ul className="list-disc list-inside space-y-1 text-gray-700">
                      {finalApplication.job.eligibilityCriteria.map((criteria: string, index: number) => (
                        <li key={index}>{criteria}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Perks */}
                {finalApplication.job.perks && finalApplication.job.perks.length > 0 && (
                  <div className="pt-4">
                    <h4 className="font-semibold text-gray-900 mb-2">Perks & Benefits</h4>
                    <div className="flex flex-wrap gap-2">
                      {finalApplication.job.perks.map((perk: any, index: number) => (
                        <Badge key={index} variant="outline">{perk.title}</Badge>
                      ))}
                    </div>
                  </div>
                )}
              </>
            )}
          </CardContent>
        </Card>

        {/* Company Information */}
        {typeof finalApplication.job === 'object' && finalApplication.job.company && typeof finalApplication.job.company === 'object' && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Building2 className="h-5 w-5 mr-2" />
                Company Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {finalApplication.job.company.name}
                </h3>
                {finalApplication.job.company.description && (
                  <p className="text-gray-700 leading-relaxed mb-4">
                    {finalApplication.job.company.description}
                  </p>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {finalApplication.job.company.industry && (
                  <div className="flex items-center">
                    <Briefcase className="h-4 w-4 mr-2 text-gray-500" />
                    <div>
                      <p className="text-sm text-gray-600">Industry</p>
                      <p className="font-medium">{finalApplication.job.company.industry}</p>
                    </div>
                  </div>
                )}
                {finalApplication.job.company.companySize && (
                  <div className="flex items-center">
                    <User className="h-4 w-4 mr-2 text-gray-500" />
                    <div>
                      <p className="text-sm text-gray-600">Company Size</p>
                      <p className="font-medium">{finalApplication.job.company.companySize} employees</p>
                    </div>
                  </div>
                )}
                {finalApplication.job.company.founded && (
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                    <div>
                      <p className="text-sm text-gray-600">Founded</p>
                      <p className="font-medium">{formatDate(finalApplication.job.company.founded)}</p>
                    </div>
                  </div>
                )}
                {finalApplication.job.company.country && (
                  <div className="flex items-center">
                    <MapPin className="h-4 w-4 mr-2 text-gray-500" />
                    <div>
                      <p className="text-sm text-gray-600">Country</p>
                      <p className="font-medium">{finalApplication.job.company.country}</p>
                    </div>
                  </div>
                )}
                {finalApplication.job.company.sector && (
                  <div className="flex items-center">
                    <Building2 className="h-4 w-4 mr-2 text-gray-500" />
                    <div>
                      <p className="text-sm text-gray-600">Sector</p>
                      <p className="font-medium">{finalApplication.job.company.sector}</p>
                    </div>
                  </div>
                )}
                {finalApplication.job.company.organization && (
                  <div className="flex items-center">
                    <Building2 className="h-4 w-4 mr-2 text-gray-500" />
                    <div>
                      <p className="text-sm text-gray-600">Organization</p>
                      <p className="font-medium">{finalApplication.job.company.organization}</p>
                    </div>
                  </div>
                )}
              </div>

              {/* Company Registration Details */}
              {(finalApplication.job.company.cin || finalApplication.job.company.gst || finalApplication.job.company.pan) && (
                <div className="pt-4">
                  <h4 className="font-semibold text-gray-900 mb-3">Registration Details</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {finalApplication.job.company.cin && (
                      <div className="bg-gray-50 p-3 rounded">
                        <p className="text-sm text-gray-600">CIN</p>
                        <p className="font-mono text-xs">{finalApplication.job.company.cin}</p>
                      </div>
                    )}
                    {finalApplication.job.company.gst && (
                      <div className="bg-gray-50 p-3 rounded">
                        <p className="text-sm text-gray-600">GST</p>
                        <p className="font-mono text-xs">{finalApplication.job.company.gst}</p>
                      </div>
                    )}
                    {finalApplication.job.company.pan && (
                      <div className="bg-gray-50 p-3 rounded">
                        <p className="text-sm text-gray-600">PAN</p>
                        <p className="font-mono text-xs">{finalApplication.job.company.pan}</p>
                      </div>
                    )}
                    {finalApplication.job.company.tan && (
                      <div className="bg-gray-50 p-3 rounded">
                        <p className="text-sm text-gray-600">TAN</p>
                        <p className="font-mono text-xs">{finalApplication.job.company.tan}</p>
                      </div>
                    )}
                  </div>
                  {finalApplication.job.company.isStartupIndiaRegistered && (
                    <div className="mt-3">
                      <Badge variant="default" className="bg-green-100 text-green-800">
                        Startup India Registered
                      </Badge>
                    </div>
                  )}
                </div>
              )}

              {/* Websites & Social Media */}
              {((finalApplication.job.company.websites && finalApplication.job.company.websites.length > 0) ||
                (finalApplication.job.company.socialMedia && finalApplication.job.company.socialMedia.length > 0)) && (
                <div className="pt-4">
                  <h4 className="font-semibold text-gray-900 mb-3">Online Presence</h4>
                  <div className="space-y-3">
                    {finalApplication.job.company.websites && finalApplication.job.company.websites.map((website: any, index: number) => (
                      <div key={index} className="flex items-center">
                        <Globe className="h-4 w-4 mr-2 text-gray-500" />
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-sm text-gray-600">{website.label} - {website.purpose}</p>
                              <a
                                href={website.website}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-blue-600 hover:text-blue-800 text-sm"
                              >
                                {website.website}
                              </a>
                            </div>
                            {website.isVerified && (
                              <Badge variant="default" className="text-xs">Verified</Badge>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                    {finalApplication.job.company.socialMedia && finalApplication.job.company.socialMedia.map((social: any, index: number) => (
                      <div key={index} className="flex items-center">
                        <Globe className="h-4 w-4 mr-2 text-gray-500" />
                        <div>
                          <p className="text-sm text-gray-600">{social.platform}</p>
                          <a
                            href={social.value}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:text-blue-800 text-sm"
                          >
                            {social.value}
                          </a>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Additional Information */}
              {finalApplication.job.company.moreInformation && finalApplication.job.company.moreInformation.length > 0 && (
                <div className="pt-4">
                  <h4 className="font-semibold text-gray-900 mb-3">Additional Information</h4>
                  <div className="space-y-2">
                    {finalApplication.job.company.moreInformation.map((info: any, index: number) => (
                      <div key={index} className="bg-blue-50 p-3 rounded-lg">
                        <p className="font-medium text-blue-900">{info.title}</p>
                        <p className="text-blue-800 text-sm mt-1">{info.value}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Company Categories */}
              {finalApplication.job.company.category && finalApplication.job.company.category.length > 0 && (
                <div className="pt-4">
                  <h4 className="font-semibold text-gray-900 mb-2">Categories</h4>
                  <div className="flex flex-wrap gap-2">
                    {finalApplication.job.company.category.map((cat: string, index: number) => (
                      <Badge key={index} variant="outline">{cat}</Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* Contact Information */}
              {((finalApplication.job.company.emails && finalApplication.job.company.emails.length > 0) ||
               (finalApplication.job.company.phones && finalApplication.job.company.phones.length > 0)) && (
                <div className="pt-4">
                  <h4 className="font-semibold text-gray-900 mb-2">Contact Information</h4>
                  <div className="space-y-2">
                    {finalApplication.job.company.emails && finalApplication.job.company.emails.map((emailObj: any, index: number) => (
                      <div key={index} className="flex items-center">
                        <Mail className="h-4 w-4 mr-2 text-gray-500" />
                        <div>
                          <p className="text-sm text-gray-600">{emailObj.label}</p>
                          <p className="font-medium">{emailObj.email}</p>
                        </div>
                      </div>
                    ))}
                    {finalApplication.job.company.phones && finalApplication.job.company.phones.map((phoneObj: any, index: number) => (
                      <div key={index} className="flex items-center">
                        <Phone className="h-4 w-4 mr-2 text-gray-500" />
                        <div>
                          <p className="text-sm text-gray-600">{phoneObj.label}</p>
                          <p className="font-medium">{phoneObj.phone}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Selection Process */}
        {typeof finalApplication.job === 'object' && finalApplication.job.selectionProcess && finalApplication.job.selectionProcess.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Award className="h-5 w-5 mr-2" />
                Selection Process
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {finalApplication.job.selectionProcess.map((stage: any, index: number) => (
                  <div key={index} className="flex items-start space-x-4">
                    <div className="flex-shrink-0 w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium">
                      {stage.sortOrder || index + 1}
                    </div>
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-900">{stage.stage}</h4>
                      <p className="text-gray-600 text-sm mt-1">{stage.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Interview Rounds */}
        {typeof finalApplication.job === 'object' && finalApplication.job.interviews && finalApplication.job.interviews.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <User className="h-5 w-5 mr-2" />
                Interview Rounds
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {finalApplication.job.interviews.map((interview: any, index: number) => (
                  <div key={index} className="flex items-start space-x-4">
                    <div className="flex-shrink-0 w-8 h-8 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-sm font-medium">
                      {interview.sortOrder || index + 1}
                    </div>
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-900">{interview.roundTitle}</h4>
                      <p className="text-gray-600 text-sm mt-1">{interview.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Cover Letter */}
        {finalApplication.coverLetter && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <FileText className="h-5 w-5 mr-2" />
                Cover Letter
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="prose max-w-none">
                <p className="text-gray-700 whitespace-pre-wrap leading-relaxed">
                  {finalApplication.coverLetter}
                </p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Questions and Answers */}
        {finalApplication.answers && finalApplication.answers.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <FileText className="h-5 w-5 mr-2" />
                Application Questions & Answers
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {finalApplication.answers.map((answer: any, index: number) => {
                  // Find the corresponding question from job data
                  const question = typeof finalApplication.job === 'object' && finalApplication.job.questions
                    ? finalApplication.job.questions.find((q: any) => q.questionId === answer.questionId)
                    : null;

                  return (
                    <div key={index}>
                      <div className="mb-2">
                        <p className="font-medium text-gray-900">
                          {question ? question.question : `Question ${answer.questionId}`}
                        </p>
                        {question && question.hint && (
                          <p className="text-sm text-gray-500 mt-1">{question.hint}</p>
                        )}
                        {question && question.isRequired && (
                          <Badge variant="outline" className="mt-1">Required</Badge>
                        )}
                      </div>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <p className="text-gray-700">
                          {Array.isArray(answer.answer)
                            ? answer.answer.join(', ')
                            : typeof answer.answer === 'string'
                            ? answer.answer
                            : typeof answer.answer === 'object'
                            ? JSON.stringify(answer.answer)
                            : String(answer.answer)
                          }
                        </p>
                      </div>
                      {index < finalApplication.answers.length - 1 && <Separator className="mt-6" />}
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Applicant Details */}
       
        {  

        applicantDetails && typeof applicantDetails === 'object' && (
          (applicantDetails.firstName && applicantDetails.email) ||
          (applicantDetails as any).name
        ) && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <User className="h-5 w-5 mr-2" />
                Applicant Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              {applicantLoading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                  <span className="ml-2">Loading applicant details...</span>
                </div>
              ) : (
                <div className="space-y-6">
                  {/* Basic Information */}
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-3">Personal Details</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="flex items-center">
                        <User className="h-4 w-4 mr-2 text-gray-500" />
                        <div>
                          <p className="text-sm text-gray-600">Full Name</p>
                          <p className="font-medium">
                            {typeof applicantDetails.firstName === 'string' ? applicantDetails.firstName : ''} {typeof applicantDetails.lastName === 'string' ? applicantDetails.lastName : ''}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center">
                        <Mail className="h-4 w-4 mr-2 text-gray-500" />
                        <div>
                          <p className="text-sm text-gray-600">Email</p>
                          <p className="font-medium">
                            {typeof applicantDetails.email === 'string' ? applicantDetails.email : 'Email not available'}
                          </p>
                        </div>
                      </div>
                      {applicantDetails && typeof applicantDetails === 'object' && 'phone' in applicantDetails && (applicantDetails as any).phone && (
                        <div className="flex items-center">
                          <Phone className="h-4 w-4 mr-2 text-gray-500" />
                          <div>
                            <p className="text-sm text-gray-600">Phone</p>
                            <p className="font-medium">
                              {typeof (applicantDetails as any).phone === 'string'
                                ? (applicantDetails as any).phone
                                : 'Phone information available'
                              }
                            </p>
                          </div>
                        </div>
                      )}
                      {applicantDetails && typeof applicantDetails === 'object' && 'fatherName' in applicantDetails && (applicantDetails as any).fatherName && (
                        <div className="flex items-center">
                          <User className="h-4 w-4 mr-2 text-gray-500" />
                          <div>
                            <p className="text-sm text-gray-600">Father's Name</p>
                            <p className="font-medium">{(applicantDetails as any).fatherName}</p>
                          </div>
                        </div>
                      )}
                      {applicantDetails && typeof applicantDetails === 'object' && 'motherName' in applicantDetails && (applicantDetails as any).motherName && (
                        <div className="flex items-center">
                          <User className="h-4 w-4 mr-2 text-gray-500" />
                          <div>
                            <p className="text-sm text-gray-600">Mother's Name</p>
                            <p className="font-medium">{(applicantDetails as any).motherName}</p>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Additional Details */}
                  {applicantDetails && typeof applicantDetails === 'object' && ('dob' in applicantDetails || 'gender' in applicantDetails || 'maritalStatus' in applicantDetails) && (
                    <>
                      <Separator />
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-3">Additional Information</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {'dob' in applicantDetails && (applicantDetails as any).dob && (
                            <div className="flex items-center">
                              <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                              <div>
                                <p className="text-sm text-gray-600">Date of Birth</p>
                                <p className="font-medium">
                                  {(applicantDetails as any).dob ? formatDate((applicantDetails as any).dob) : 'Date not available'}
                                </p>
                              </div>
                            </div>
                          )}
                          {'gender' in applicantDetails && (applicantDetails as any).gender && (
                            <div className="flex items-center">
                              <User className="h-4 w-4 mr-2 text-gray-500" />
                              <div>
                                <p className="text-sm text-gray-600">Gender</p>
                                <p className="font-medium">
                                  {typeof (applicantDetails as any).gender === 'string'
                                    ? (applicantDetails as any).gender
                                    : 'Gender information available'
                                  }
                                </p>
                              </div>
                            </div>
                          )}
                          {'maritalStatus' in applicantDetails && (applicantDetails as any).maritalStatus && (
                            <div className="flex items-center">
                              <User className="h-4 w-4 mr-2 text-gray-500" />
                              <div>
                                <p className="text-sm text-gray-600">Marital Status</p>
                                <p className="font-medium capitalize">
                                  {typeof (applicantDetails as any).maritalStatus === 'string'
                                    ? (applicantDetails as any).maritalStatus
                                    : 'Marital status information available'
                                  }
                                </p>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </>
                  )}

                  {/* Professional Information */}
                  {applicantDetails && typeof applicantDetails === 'object' && (
                    ('skills' in applicantDetails && (applicantDetails as any).skills && (applicantDetails as any).skills.length > 0) ||
                    ('qualifications' in applicantDetails && (applicantDetails as any).qualifications && (applicantDetails as any).qualifications.length > 0) ||
                    ('experiences' in applicantDetails && (applicantDetails as any).experiences && (applicantDetails as any).experiences.length > 0) ||
                    ('certificates' in applicantDetails && (applicantDetails as any).certificates && (applicantDetails as any).certificates.length > 0)
                  ) && (
                    <>
                      <Separator />
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-3">Professional Information</h4>

                        {/* Skills */}
                        {'skills' in applicantDetails && (applicantDetails as any).skills && (applicantDetails as any).skills.length > 0 && (
                          <div className="mb-6">
                            <h5 className="font-medium text-gray-800 mb-3">Skills & Expertise</h5>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                              {(applicantDetails as any).skills.map((skill: any, index: number) => (
                                <div key={skill._id || index} className="bg-gray-50 p-3 rounded-lg">
                                  <div className="flex items-center justify-between mb-2">
                                    <h6 className="font-medium text-gray-900 capitalize">{skill.name}</h6>
                                    <Badge variant={
                                      skill.level === 'expert' ? 'default' :
                                      skill.level === 'advanced' ? 'secondary' :
                                      skill.level === 'intermediate' ? 'outline' : 'outline'
                                    }>
                                      {skill.level}
                                    </Badge>
                                  </div>
                                  <div className="flex items-center text-sm text-gray-600">
                                    <Star className="h-3 w-3 mr-1" />
                                    <span>{skill.monthsOfExperience} months experience</span>
                                  </div>
                                </div>
                              ))}
                            </div>
                            <p className="text-xs text-gray-500 mt-2">
                              Total Skills: {(applicantDetails as any).skills.length}
                            </p>
                          </div>
                        )}

                        {/* Qualifications */}
                        {'qualifications' in applicantDetails && (applicantDetails as any).qualifications && (applicantDetails as any).qualifications.length > 0 && (
                          <div className="mb-4">
                            <h5 className="font-medium text-gray-800 mb-2">Qualifications</h5>
                            <div className="flex flex-wrap gap-2">
                              {(applicantDetails as any).qualifications.map((qualId: string, index: number) => (
                                <Badge key={index} variant="outline">
                                  <GraduationCap className="h-3 w-3 mr-1" />
                                  Qualification ID: {qualId}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Work Experience */}
                        {'experiences' in applicantDetails && (applicantDetails as any).experiences && (applicantDetails as any).experiences.length > 0 && (
                          <div className="mb-6">
                            <h5 className="font-medium text-gray-800 mb-3">Work Experience</h5>
                            <div className="space-y-4">
                              {(applicantDetails as any).experiences.map((exp: any, index: number) => (
                                <div key={exp._id || index} className="bg-gray-50 p-4 rounded-lg">
                                  <div className="flex items-start justify-between mb-2">
                                    <div>
                                      <h6 className="font-semibold text-gray-900">{exp.designation}</h6>
                                      <p className="text-blue-600 font-medium">{exp.company}</p>
                                    </div>
                                    <Badge variant="outline" className="capitalize">
                                      {exp.employmentType}
                                    </Badge>
                                  </div>
                                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mb-3 text-sm text-gray-600">
                                    <div className="flex items-center">
                                      <Calendar className="h-3 w-3 mr-1" />
                                      <span>{formatDateRange(exp.startDate, exp.endDate)}</span>
                                    </div>
                                    <div className="flex items-center">
                                      <MapPin className="h-3 w-3 mr-1" />
                                      <span>{exp.location} ({exp.locationType})</span>
                                    </div>
                                  </div>
                                  {exp.description && (
                                    <p className="text-gray-700 text-sm">{exp.description}</p>
                                  )}
                                </div>
                              ))}
                            </div>
                            <p className="text-xs text-gray-500 mt-2">
                              Total Experience: {(applicantDetails as any).experiences.length} positions
                            </p>
                          </div>
                        )}

                        {/* Certificates */}
                        {'certificates' in applicantDetails && (applicantDetails as any).certificates && (applicantDetails as any).certificates.length > 0 && (
                          <div className="mb-6">
                            <h5 className="font-medium text-gray-800 mb-3">Certificates & Achievements</h5>
                            <div className="space-y-3">
                              {(applicantDetails as any).certificates.map((cert: any, index: number) => (
                                <div key={cert._id || index} className="bg-gray-50 p-4 rounded-lg">
                                  <div className="flex items-start">
                                    <Award className="h-5 w-5 mr-3 text-blue-600 mt-1" />
                                    <div className="flex-1">
                                      <div className="flex items-start justify-between mb-2">
                                        <div>
                                          <h6 className="font-semibold text-gray-900">{cert.certificateName}</h6>
                                          <p className="text-blue-600 font-medium">{cert.instituteName}</p>
                                        </div>
                                        <Badge variant="outline" className="capitalize">
                                          {cert.certificateType}
                                        </Badge>
                                      </div>

                                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mb-3 text-sm text-gray-600">
                                        <div className="flex items-center">
                                          <Calendar className="h-3 w-3 mr-1" />
                                          <span>Issued: {formatDate(cert.issueDate)}</span>
                                        </div>
                                        {cert.expiryDate && (
                                          <div className="flex items-center">
                                            <Calendar className="h-3 w-3 mr-1" />
                                            <span>Expires: {formatDate(cert.expiryDate)}</span>
                                          </div>
                                        )}
                                      </div>

                                      {cert.description && (
                                        <p className="text-gray-700 text-sm mb-3">{cert.description}</p>
                                      )}

                                      {cert.verificationUrl && (
                                        <div className="flex items-center">
                                          <Globe className="h-3 w-3 mr-1 text-green-600" />
                                          <a
                                            href={cert.verificationUrl}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="text-green-600 hover:text-green-800 text-sm"
                                          >
                                            Verify Certificate
                                          </a>
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                            <p className="text-xs text-gray-500 mt-2">
                              Total Certificates: {(applicantDetails as any).certificates.length}
                            </p>
                          </div>
                        )}
                      </div>
                    </>
                  )}

                  {/* Important Links */}
                  {'importantLinks' in applicantDetails && (applicantDetails as any).importantLinks && (applicantDetails as any).importantLinks.length > 0 && (
                    <>
                      <Separator />
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-3">Important Links</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                          {(applicantDetails as any).importantLinks.map((link: any, index: number) => (
                            <div key={link._id || index} className="bg-gray-50 p-3 rounded-lg">
                              <div className="flex items-center">
                                <Globe className="h-4 w-4 mr-2 text-gray-500" />
                                <div className="flex-1">
                                  <p className="font-medium text-gray-900">{link.title}</p>
                                  <a
                                    href={link.url}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="text-blue-600 hover:text-blue-800 text-sm break-all"
                                  >
                                    {link.url}
                                  </a>
                                  <div className="flex items-center mt-1 gap-2">
                                    <Badge variant="outline" className="text-xs">
                                      {link.type}
                                    </Badge>
                                    {link.isVerified && (
                                      <Badge variant="default" className="text-xs">
                                        Verified
                                      </Badge>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                        <p className="text-xs text-gray-500 mt-2">
                          Total Links: {(applicantDetails as any).importantLinks.length}
                        </p>
                      </div>
                    </>
                  )}

                  {/* User Type Information */}
                  {applicantDetails && typeof applicantDetails === 'object' && 'userType' in applicantDetails && (applicantDetails as any).userType && (
                    <>
                      <Separator />
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-3">Account Information</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="flex items-center">
                            <User className="h-4 w-4 mr-2 text-gray-500" />
                            <div>
                              <p className="text-sm text-gray-600">User Type</p>
                              <p className="font-medium capitalize">
                                {typeof (applicantDetails as any).userType === 'string'
                                  ? (applicantDetails as any).userType
                                  : 'User type information available'
                                }
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Application Status & Timeline */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Clock className="h-5 w-5 mr-2" />
              Application Status & Timeline
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-gray-900">Current Status</p>
                  <Badge className={getStatusColor(finalApplication.status)}>
                    {finalApplication.status.charAt(0).toUpperCase() + finalApplication.status.slice(1)}
                  </Badge>
                </div>
                <div className="text-right">
                  <p className="text-sm text-gray-600">Last Updated</p>
                  <p className="font-medium">{formatDate(finalApplication.updatedAt)}</p>
                </div>
              </div>

              {/* Review Logs */}
              {finalApplication.reviewLogs && finalApplication.reviewLogs.length > 0 && (
                <div className="pt-4">
                  <h4 className="font-semibold text-gray-900 mb-3">Review History</h4>
                  <div className="space-y-3">
                    {finalApplication.reviewLogs.map((log: any, index: number) => (
                      <div key={index} className="bg-gray-50 p-3 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <Badge variant="outline">{log.status || 'Status Update'}</Badge>
                          <span className="text-sm text-gray-500">
                            {log.date ? formatDate(log.date) : 'Date not available'}
                          </span>
                        </div>
                        {log.comment && (
                          <p className="text-gray-700 text-sm">{log.comment}</p>
                        )}
                        {log.reviewer && (
                          <p className="text-gray-600 text-xs mt-1">Reviewed by: {log.reviewer}</p>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Interviews */}
              {finalApplication.interviews && finalApplication.interviews.length > 0 && (
                <div className="pt-4">
                  <h4 className="font-semibold text-gray-900 mb-3">Scheduled Interviews</h4>
                  <div className="space-y-3">
                    {finalApplication.interviews.map((interview: any, index: number) => (
                      <div key={index} className="bg-blue-50 p-3 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <h5 className="font-medium text-gray-900">
                            {interview.title || `Interview ${index + 1}`}
                          </h5>
                          {interview.status && (
                            <Badge variant={interview.status === 'completed' ? 'default' : 'outline'}>
                              {interview.status}
                            </Badge>
                          )}
                        </div>
                        {interview.date && (
                          <p className="text-sm text-gray-600">
                            <Calendar className="h-4 w-4 inline mr-1" />
                            {formatDate(interview.date)}
                          </p>
                        )}
                        {interview.time && (
                          <p className="text-sm text-gray-600">
                            <Clock className="h-4 w-4 inline mr-1" />
                            {interview.time}
                          </p>
                        )}
                        {interview.location && (
                          <p className="text-sm text-gray-600">
                            <MapPin className="h-4 w-4 inline mr-1" />
                            {interview.location}
                          </p>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Application Metadata */}
              <div className="pt-4 border-t">
                <h4 className="font-semibold text-gray-900 mb-3">Application Details</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-gray-600">Application ID</p>
                    <p className="font-mono text-xs bg-gray-100 p-1 rounded">{finalApplication._id}</p>
                  </div>
                  <div>
                    <p className="text-gray-600">Job ID</p>
                    <p className="font-mono text-xs bg-gray-100 p-1 rounded">
                      {typeof finalApplication.job === 'object' ? finalApplication.job._id : finalApplication.job}
                    </p>
                  </div>
                  <div>
                    <p className="text-gray-600">Submitted On</p>
                    <p className="font-medium">{formatDate(finalApplication.createdAt)}</p>
                  </div>
                  <div>
                    <p className="text-gray-600">Is Deleted</p>
                    <p className="font-medium">{finalApplication.isDeleted ? 'Yes' : 'No'}</p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default JobApplicationDetailsPage;
