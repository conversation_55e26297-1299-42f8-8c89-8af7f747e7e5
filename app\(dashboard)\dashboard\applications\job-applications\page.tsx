"use client";

import React, { useState } from 'react';
import { useGetMyJobApplicationsQuery } from '@/store/jobApplication-feature/jobApplicationApi';
import { IJobApplication } from '@/types/IJobApplication';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Eye, Calendar, User, Building2, MapPin, UserCheck, Briefcase } from 'lucide-react';
import { useAppSelector } from '@/store';
import { format } from 'date-fns';
import { useRouter } from 'next/navigation';

const JobApplicationsPage = () => {
  const router = useRouter();
  const currentUser = useAppSelector(state => state.auth.user);

  const {
    data,
    isLoading,
    isError,
    error
  } = useGetMyJobApplicationsQuery();

  const applications = data?.data?.jobApplications || [];

  const getStatusColor = (status: string) => {
    const colors = {
      'draft': 'bg-gray-100 text-gray-800',
      'applied': 'bg-blue-100 text-blue-800',
      'under-review': 'bg-yellow-100 text-yellow-800',
      'shortlisted': 'bg-purple-100 text-purple-800',
      'interview': 'bg-orange-100 text-orange-800',
      'offered': 'bg-green-100 text-green-800',
      'hired': 'bg-emerald-100 text-emerald-800',
      'rejected': 'bg-red-100 text-red-800',
      'withdrawn': 'bg-gray-100 text-gray-800',
    };
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  

  const getJobTitle = (job: any) => {
    if (typeof job === 'object' && job?.title) {
      return job.title;
    }
    return 'Unknown Job';
  };

  const getCompanyName = (job: any) => {
    if (typeof job === 'object' && job?.company) {
      if (typeof job.company === 'object' && job.company?.name) {
        return job.company.name;
      }
      if (typeof job.company === 'string') {
        return job.company;
      }
    }
    return 'Unknown Company';
  };

  const getLocation = (job: any) => {
    if (typeof job === 'object' && job?.location) {
      return `${job.location.city}, ${job.location.state}`;
    }
    return 'Unknown Location';
  };

  const getUserRole = (application: IJobApplication) => {
    if (!currentUser?._id) return 'unknown';

    // Check if current user is the applicant
    const applicantId = typeof application.applicant === 'object'
      ? application.applicant._id
      : application.applicant;

    if (applicantId === currentUser._id) {
      return 'applicant';
    }

    // If not applicant, assume they're viewing as employer
    return 'employer';
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Error Loading Applications</h2>
          <p className="text-gray-600">
            {error && 'data' in error ? (error.data as any)?.message : 'Something went wrong'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">My Job Applications</h1>
        <p className="text-gray-600">View and manage your job applications and applications to your posted jobs</p>
      </div>

      {/* Results Summary */}
      <div className="mb-6">
        <p className="text-sm text-gray-600">
          Showing {applications.length} application{applications.length !== 1 ? 's' : ''}
        </p>
      </div>

      {/* Applications List */}
      <div className="space-y-4">
        {applications.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <div className="text-gray-400 mb-4">
                <Briefcase className="h-12 w-12 mx-auto" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Applications Found</h3>
              <p className="text-gray-600">You haven't applied to any jobs yet.</p>
            </CardContent>
          </Card>
        ) : (
          applications.map((application: IJobApplication) => (
            <Card key={application._id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    {/* Job Info */}
                    <div className="mb-4">
                      <div className="flex items-center space-x-2 mb-2">
                        <Building2 className="h-4 w-4 text-gray-500" />
                        <h3 className="text-lg font-semibold text-gray-900">
                          {getJobTitle(application.job)}
                        </h3>
                      </div>
                      <div className="flex items-center space-x-4 text-sm text-gray-600">
                        <span className="flex items-center">
                          <Building2 className="h-3 w-3 mr-1" />
                          {getCompanyName(application.job)}
                        </span>
                        <span className="flex items-center">
                          <MapPin className="h-3 w-3 mr-1" />
                          {getLocation(application.job)}
                        </span>
                      </div>
                    </div>

                   
                    {/* Cover Letter Preview */}
                    {application.coverLetter && (
                      <div className="mb-4">
                        <p className="text-sm text-gray-600 line-clamp-2">
                          {application.coverLetter.substring(0, 150)}
                          {application.coverLetter.length > 150 ? '...' : ''}
                        </p>
                      </div>
                    )}
                  </div>

                  {/* Right Side - Status and Actions */}
                  <div className="flex flex-col items-end space-y-3">
                    {/* Role and Status Badges */}
                    <div className="flex items-center space-x-2">
                      <Badge
                        variant="outline"
                        className={getUserRole(application) === 'applicant' ? 'bg-blue-50 text-blue-700 border-blue-200' : 'bg-green-50 text-green-700 border-green-200'}
                      >
                        {getUserRole(application) === 'applicant' ? (
                          <>
                            <UserCheck className="h-3 w-3 mr-1" />
                            My Application
                          </>
                        ) : (
                          <>
                            <Briefcase className="h-3 w-3 mr-1" />
                            As Employer
                          </>
                        )}
                      </Badge>
                      <Badge className={getStatusColor(application.status)}>
                        {application.status.replace('-', ' ').toUpperCase()}
                      </Badge>
                    </div>

                    {/* View Details Button */}
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex items-center space-x-1"
                      onClick={() => router.push(`/dashboard/applications/job-applications/${application._id}`)}
                    >
                      <Eye className="h-3 w-3" />
                      <span>View Details</span>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
};

export default JobApplicationsPage;
