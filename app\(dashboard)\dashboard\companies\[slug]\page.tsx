"use client"

import { useParams } from 'next/navigation'; 

import { 
  Building2, 
  Mail, 
  Phone, 
  Globe, 
  MapPin, 
  Users, 
  Calendar, 
  ExternalLink,
  Linkedin,
  Twitter,
  CheckCircle,
  Clock,
} from 'lucide-react';
import { useGetCompanyBySlugQuery } from '@/store/company-features/companyApi';



export default function CompanyDetailsPage() {
    const params = useParams();
  const slug = params.slug as string;

    const { data, isLoading, isError } = useGetCompanyBySlugQuery(slug);
    if (isLoading) {
      return <div>Loading...</div>;
    }
    if (isError) {
      return <div>Error...</div>;
    }
    


  // Helper function to format dates
  const formatDate = (dateString : Date) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Helper function to calculate years since foundation
  const getYearsSinceFoundation = (foundedDate : Date) => {
    if (!foundedDate) return 0;
    const founded = new Date(foundedDate);
    const now = new Date();
    return now.getFullYear() - founded.getFullYear();
  };
  
  // Helper function to get social media icons
  const getSocialIcon = (platform : string) => {
    switch (platform.toLowerCase()) {
      case 'linkedin':
        return <Linkedin className="w-4 h-4 text-gray-500 dark:text-gray-400" />;
      case 'twitter':
        return <Twitter className="w-4 h-4 text-gray-500 dark:text-gray-400" />;
      default:
        return <Globe className="w-4 h-4 text-gray-500 dark:text-gray-400" />;
    }
  };

  return (
    <div className="min-h-screen ">
      <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        {/* Header */}
        <div className="bg-white dark:bg-[#171717] rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-8 mb-8">
          <div className="flex flex-col sm:flex-row items-start justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-16 h-16 bg-blue-50 dark:bg-gray-700 rounded-lg flex items-center justify-center border border-blue-100 dark:border-blue-900">
                <Building2 className="w-8 h-8 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <h1 className="text-2xl font-semibold text-gray-900 dark:text-white mb-1">
                  {data?.data?.name}
                </h1>
                <p className="text-gray-600 dark:text-gray-400 mb-1">{data?.data?.organization}</p>
                <p className="text-sm text-gray-500 dark:text-gray-500">@{data?.data?.slug}</p>
              </div>
            </div>
            <div className="text-right mt-4 sm:mt-0">
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                data?.data?.isDeleted 
                  ? 'bg-red-100 dark:bg-red-900/50 text-red-800 dark:text-red-300' 
                  : 'bg-green-100 dark:bg-green-900/50 text-green-800 dark:text-green-300'
              }`}>
                {data?.data?.isDeleted ? 'Inactive' : 'Active'}
              </span>
            </div>
          </div>
          <p className="text-gray-700 dark:text-gray-300 mt-4 leading-relaxed">
            {data?.data?.description}
          </p>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          
          {/* Left Column */}
          <div className="lg:col-span-2 space-y-8">
            
            {/* Company Information */}
            <div className="bg-white dark:bg-[#171717] rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">Company Information</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-8">
                <div>
                  <label className="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Industry</label>
                  <p className="text-gray-900 dark:text-gray-200">{data?.data?.industry}</p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Sector</label>
                  <p className="text-gray-900 dark:text-gray-200">{data?.data?.sector}</p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Location</label>
                  <div className="flex items-center text-gray-900 dark:text-gray-200">
                    <MapPin className="w-4 h-4 mr-2 text-gray-400 dark:text-gray-500" />
                    {data?.data?.country}
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Team Size</label>
                  <div className="flex items-center text-gray-900 dark:text-gray-200">
                    <Users className="w-4 h-4 mr-2 text-gray-400 dark:text-gray-500" />
                    {data?.data?.companySize}
                  </div>
                </div>
                
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Founded</label>
                  <div className="flex items-center text-gray-900 dark:text-gray-200">
                    <Calendar className="w-4 h-4 mr-2 text-gray-400 dark:text-gray-500" />
                    {data?.data?.founded ? formatDate(data?.data?.founded) : 'N/A'}
                  </div>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mt-1 ml-6">
                    {data?.data?.founded && getYearsSinceFoundation(data?.data?.founded)} years in business
                  </p>
                </div>
              </div>
            </div>

            {/* Contact Information */}
            <div className="bg-white dark:bg-[#171717] rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">Contact Information</h2>
              
              <div className="space-y-8">
                {/* Emails */}
                <div>
                  <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-4 flex items-center">
                    <Mail className="w-4 h-4 mr-2" />
                    Email Addresses
                  </h3>
                  <div className="space-y-3">
                    {data?.data?.emails?.map((email, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                        <div className="flex items-center space-x-3">
                          <span className="text-gray-900 dark:text-gray-200">{email.email}</span>
                          {email.isVerified && (
                            <CheckCircle className="w-4 h-4 text-green-500 dark:text-green-400" />
                          )}
                          {email.isDefault && (
                            <span className="px-2 py-0.5 bg-blue-100 text-blue-800 dark:bg-blue-900/70 dark:text-blue-300 text-xs rounded-full">
                              Primary
                            </span>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Phones */}
                <div>
                  <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-4 flex items-center">
                    <Phone className="w-4 h-4 mr-2" />
                    Phone Numbers
                  </h3>
                  <div className="space-y-3">
                    {data?.data?.phones?.map((phone, index ) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                        <div className="flex items-center space-x-3">
                          <span className="text-gray-900 dark:text-gray-200">{phone.phone}</span>
                          {phone.isVerified && (
                            <CheckCircle className="w-4 h-4 text-green-500 dark:text-green-400"     />                    
                         )}
                          {phone.isDefault && (
                            <span className="px-2 py-0.5 bg-blue-100 text-blue-800 dark:bg-blue-900/70 dark:text-blue-300 text-xs rounded-full">
                              Primary
                            </span>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Websites */}
                <div>
                  <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-4 flex items-center">
                    <Globe className="w-4 h-4 mr-2" />
                    Websites
                  </h3>
                  <div className="space-y-3">
                    {data?.data?.websites?.map((website, index ) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                        <div className="flex items-center space-x-3">
                          <a
                            href={website.website}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 flex items-center"
                          >
                            {website.website}
                            <ExternalLink className="w-3 h-3 ml-2" />
                          </a>
                          {website.isVerified && (
                             <CheckCircle className="w-4 h-4 text-green-500 dark:text-green-400"  />                        )}
                          {website.isDefault && (
                            <span className="px-2 py-0.5 bg-blue-100 text-blue-800 dark:bg-blue-900/70 dark:text-blue-300 text-xs rounded-full">
                              Primary
                            </span>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Column */}
          <div className="space-y-8">
            
            {/* Social Media */}
            <div className="bg-white dark:bg-[#171717] rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">Social Media</h2>
              <div className="space-y-4">
                {data?.data?.socialMedia?.map((social , index) => (
                  <a
                    key={index}
                    href={social.value}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center justify-between p-3 bg-white dark:bg-gray-800 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                  >
                    <div className="flex items-center space-x-3">
                      {getSocialIcon(social.platform)}
                      <span className="text-gray-900 dark:text-gray-200 capitalize">{social.platform}</span>
                    </div>
                    <ExternalLink className="w-4 h-4 text-gray-400 dark:text-gray-500" />
                  </a>
                ))}
              </div>
            </div>

            {/* Quick Stats */}
            <div className="bg-white dark:bg-[#171717] rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">Summary</h2>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600 dark:text-gray-400">Total Contacts</span>
                  <span className="font-medium text-gray-900 dark:text-gray-200">
                    {/* {data?.data?.emails?.length + data?.data?.phones?.length} */}
                    {
                       ( data?.data?.phones? data?.data?.phones.length : 0) + (data?.data?.emails? data?.data?.emails.length : 0)
                    }
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600 dark:text-gray-400">Websites</span>
                  <span className="font-medium text-gray-900 dark:text-gray-200">{data?.data?.websites.length}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600 dark:text-gray-400">Social Platforms</span>
                  <span className="font-medium text-gray-900 dark:text-gray-200">{data?.data?.socialMedia.length}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600 dark:text-gray-400">Years Active</span>
                  <span className="font-medium text-gray-900 dark:text-gray-200">
                    {data?.data?.founded && getYearsSinceFoundation(data?.data?.founded)}
                  </span>
                </div>
              </div>
            </div>

            {/* System Information */}
            <div className="bg-white dark:bg-[#171717] rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">System Information</h2>
              <div className="space-y-4">
                <div>
                  <div className="flex items-center text-gray-600 dark:text-gray-400 mb-1">
                    <Clock className="w-4 h-4 mr-2" />
                    <span className="text-sm">Created</span>
                  </div>
                  <p className="text-gray-900 dark:text-gray-200 ml-6">{data?.data?.createdAt &&formatDate(data?.data?.createdAt)}</p>
                </div>
                <div>
                  <div className="flex items-center text-gray-600 dark:text-gray-400 mb-1">
                    <Clock className="w-4 h-4 mr-2" />
                    <span className="text-sm">Last Updated</span>
                  </div>
                  <p className="text-gray-900 dark:text-gray-200 ml-6">{data?.data?.updatedAt && formatDate(data?.data?.updatedAt)}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}


