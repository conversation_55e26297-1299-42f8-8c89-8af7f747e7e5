"use client"

import { Heading } from '@/components/sections/Heading';
import CompanyCard from '@/components/dashboard-components/CompanyCard';
import { useGetAllCompaniesQuery } from '@/store/company-features/companyApi';


export default function Company() {
  const { data, isLoading, isError } = useGetAllCompaniesQuery();
  console.log("Full data structure:", data);
console.log("data.data:", data?.data);
  if (isLoading) {
    return <div>Loading...</div>;
  }
  if (isError) {
    return <div>Error...</div>;
  }
  const companies = data?.data.companies || [];
  

  return (
    <main>
      
        <div className='min-h-screen '>
          <Heading className='mb-10'>Our Companies</Heading>
          <div className="">
            {companies?.map((company : any, index:number) => (              
              <CompanyCard key={company.slug} company={company} />              
            ))
            }
          </div>
        </div>

     
    </main>
  );
}