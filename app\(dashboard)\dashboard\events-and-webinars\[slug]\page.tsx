'use client';
import React, { useEffect, useState, useMemo } from 'react';
import { useParams } from 'next/navigation';
import axios from 'axios';
import { motion } from 'framer-motion';
import {
  Calendar,
  Clock,
  MapPin,
  Users,
  Tag,
  Award,
  Globe,
  Building,
  User,
  DollarSign,
  Video,
  FileText,
  MapPinIcon,
  ArrowLeft,
  Share2,
  Heart,
  ExternalLink,
  CheckCircle,
  AlertCircle,
  Info,
  Star,
  Timer,
  Shield,
  Download,
  BookOpen,
  Zap,
  Target,
  Phone,
  Mail,
  Link as LinkIcon,
  Copy,
  Twitter,
  Facebook,
  Linkedin,
  PlayCircle,
  Archive,
  History,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { DialogHeader } from '@/components/ui/dialog';
import EventRegisterationForm from "@/components/forms/RegisterForm";
import { IEvent } from '@/types/IEvents';
import { Card, CardHeader } from '@/components/ui/card';
import { useAppSelector } from '@/store';
import { useGetEventBySlugQuery } from '@/store/features/events-features/eventsApi';

type EventStatus = 'upcoming' | 'ongoing' | 'past';

const formatDateTime = (dateString: string, timezone?: string) => {
  const date = new Date(dateString);
  const options: Intl.DateTimeFormatOptions = {
    timeZone: timezone || 'UTC',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    weekday: 'long',
  };
  return date.toLocaleString('en-US', options);
};

const formatDuration = (minutes: number) => {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  if (hours > 0) {
    return `${hours}h ${mins}m`;
  }
  return `${mins}m`;
};

const getEventStatus = (startTime: string, endTime?: string): EventStatus => {
  const now = new Date();
  const eventStart = new Date(startTime);
  const eventEnd = endTime ? new Date(endTime) : new Date(eventStart.getTime() + 2 * 60 * 60 * 1000);
  
  if (now < eventStart) return 'upcoming';
  if (now >= eventStart && now <= eventEnd) return 'ongoing';
  return 'past';
};

const getTimeDisplay = (startTime: string, status: EventStatus, endTime?: string) => {
  const now = new Date();
  const eventStart = new Date(startTime);
  const eventEnd = endTime ? new Date(endTime) : new Date(eventStart.getTime() + 2 * 60 * 60 * 1000);
  
  if (status === 'upcoming') {
    const diff = eventStart.getTime() - now.getTime();
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    
    if (days > 0) return `Starts in ${days}d ${hours}h`;
    if (hours > 0) return `Starts in ${hours}h ${minutes}m`;
    return `Starts in ${minutes}m`;
  }
  
  if (status === 'ongoing') {
    const diff = eventEnd.getTime() - now.getTime();
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    
    if (hours > 0) return `Ends in ${hours}h ${minutes}m`;
    return `Ends in ${minutes}m`;
  }
  
  const diff = now.getTime() - eventEnd.getTime();
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  
  if (days > 0) return `Ended ${days}d ${hours}h ago`;
  if (hours > 0) return `Ended ${hours}h ago`;
  return `Ended recently`;
};

const defaultEventImage = '/images/annie-spratt-QckxruozjRg-unsplash.jpg';

export default function EventDetails() {
  const params = useParams();
  const slug = params?.slug as string;
  const [isRegistered, setIsRegistered] = useState(false);
  const [showShareModal, setShowShareModal] = useState(false);
  const [copySuccess, setCopySuccess] = useState(false);
  const user = useAppSelector(state => state.auth?.user);
  const [event, setEvent] = useState<IEvent | undefined>();

  const {
    data,
    isLoading: loading,
    error: error,
  } = useGetEventBySlugQuery(slug, {
    skip: !slug,
  });



  useEffect(() => {
    setEvent(data?.data);
    if (event && user) {
      const registered = event.registeredUsers?.some(
        (element: { _id: string }) => element._id === user._id
      );
      setIsRegistered(registered);
    } else {
      setIsRegistered(false);
    }
  }, [event, user, data]);
  
  const getHostName = (event: IEvent) => {
    if (event.hostName) return event.hostName;
    if (event.hostedByUsers?.[0]) {
      const user = event.hostedByUsers[0].user;
      return `${user.firstName} ${user.lastName}`;
    }
    if (event.hostedByCompanies?.[0]) {
      return event.hostedByCompanies[0].user.name;
    }
    return 'Unknown Host';
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (err) {
      console.error('Failed to copy text');
    }
  };

  const shareUrl = typeof window !== 'undefined' ? window.location.href : '';

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="h-12 w-12 animate-spin rounded-full border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error || !event) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen text-center">
        <AlertCircle className="h-16 w-16 text-red-500 mb-4" />
        <div className="mb-4 text-red-500">
          'Event not found'
        </div>
        <button
          onClick={() => window.history.back()}
          className="flex items-center gap-2 rounded-lg bg-blue-600 px-4 py-2 text-white hover:bg-blue-700"
        >
          <ArrowLeft className="h-4 w-4" />
          Go Back
        </button>
      </div>
    );
  }

  const eventStatus = getEventStatus(event.startTime, event.endTime);
  const timeDisplay = getTimeDisplay(event.startTime, eventStatus, event.endTime);
  const spotsLeft = event.capacity ? event.capacity - event.registeredUsers.length : null;
  const isRegistrationClosed = event.registrationDeadline ? new Date(event.registrationDeadline) < new Date() : false;


  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="relative">
        <div className="h-64 md:h-96 overflow-hidden">
          <img
            src={event.bannerImage?.url || defaultEventImage}
            alt={event.title}
            className="w-full h-full object-cover rounded-xl"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent rounded-xl" />
        </div>
        
        <div className="absolute top-4 left-4 z-10">
          <button
            onClick={() => window.history.back()}
            className="flex items-center gap-2 rounded-lg bg-black/50 backdrop-blur-sm px-4 py-2 text-white hover:bg-black/60 transition-colors"
          >
            <ArrowLeft className="h-4 w-4" />
            Back
          </button>
        </div>

        <div className="absolute top-4 right-4 z-10 flex gap-2">
          <button
            onClick={() => setShowShareModal(true)}
            className="flex items-center gap-2 rounded-lg bg-black/50 backdrop-blur-sm px-4 py-2 text-white hover:bg-black/60 transition-colors"
          >
            <Share2 className="h-4 w-4" />
            Share
          </button>
        </div>

        <div className="absolute top-4 left-1/2 transform -translate-x-1/2 z-10 flex gap-2">
          {event.isFeatured && (
            <div className="flex items-center gap-2 rounded-full bg-yellow-400 px-4 py-2 text-sm font-semibold text-yellow-800">
              <Star className="h-4 w-4" />
              Featured
            </div>
          )}
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2 space-y-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg"
            >
              <div className="flex flex-wrap items-center gap-3 mb-4">
                <span className="rounded-full bg-blue-100 px-3 py-1 text-sm font-medium text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                  {event.category}
                </span>
                <span className="rounded-full bg-gray-100 px-3 py-1 text-sm font-medium text-gray-800 dark:bg-gray-800 dark:text-gray-200">
                  {event.level}
                </span>
                <span className="rounded-full bg-purple-100 px-3 py-1 text-sm font-medium text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                  {event.language}
                </span>
                {event.isPaid ? (
                  <span className="rounded-full bg-green-100 px-3 py-1 text-sm font-medium text-green-800 dark:bg-green-900 dark:text-green-200">
                    {event.currency} {event.price}
                  </span>
                ) : (
                  <span className="rounded-full bg-blue-100 px-3 py-1 text-sm font-medium text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                    Free
                  </span>
                )}
              </div>

              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                {event.title}
              </h1>

              <div className="prose max-w-none text-gray-600 dark:text-gray-300 mb-6">
                <p className="text-lg leading-relaxed">{event.description}</p>
              </div>

              <div className="flex flex-wrap gap-4 mb-6">
                {event.certificateOffered && (
                  <div className="flex items-center gap-2 text-sm text-green-700 dark:text-green-300">
                    <Award className="h-4 w-4" />
                    Certificate Included
                  </div>
                )}
                {event.recordingAccess && (
                  <div className="flex items-center gap-2 text-sm text-purple-700 dark:text-purple-300">
                    <Video className="h-4 w-4" />
                    Recording Access
                  </div>
                )}
                {event.resourcesProvided && (
                  <div className="flex items-center gap-2 text-sm text-blue-700 dark:text-blue-300">
                    <FileText className="h-4 w-4" />
                    Resources Provided
                  </div>
                )}
              </div>

              {event.tags && event.tags.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {event.tags.map((tag, idx) => (
                    <span
                      key={idx}
                      className="inline-flex items-center gap-1 rounded-full bg-gray-100 px-3 py-1 text-sm text-gray-600 dark:bg-gray-700 dark:text-gray-400"
                    >
                      <Tag className="h-3 w-3" />
                      {tag}
                    </span>
                  ))}
                </div>
              )}
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg"
            >
              <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
                <Info className="h-5 w-5" />
                Event Details
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <Calendar className="h-5 w-5 text-blue-600 mt-1" />
                    <div>
                      <p className="font-medium text-gray-900 dark:text-white">Date & Time</p>
                      <p className="text-gray-600 dark:text-gray-400">
                        {formatDateTime(event.startTime, event.timezone)}
                      </p>
                      {event.endTime && (
                        <p className="text-sm text-gray-500 dark:text-gray-500">
                          Ends: {formatDateTime(event.endTime, event.timezone)}
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <Clock className="h-5 w-5 text-blue-600 mt-1" />
                    <div>
                      <p className="font-medium text-gray-900 dark:text-white">Duration</p>
                      <p className="text-gray-600 dark:text-gray-400">
                        {event.durationMinutes ? formatDuration(event.durationMinutes) : 'TBD'}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    {event.mode === 'Online' ? (
                      <Globe className="h-5 w-5 text-blue-600 mt-1" />
                    ) : (
                      <MapPinIcon className="h-5 w-5 text-blue-600 mt-1" />
                    )}
                    <div>
                      <p className="font-medium text-gray-900 dark:text-white">
                        {event.mode === 'Online' ? 'Platform' : 'Location'}
                      </p>
                      <p className="text-gray-600 dark:text-gray-400">
                        {event.mode === 'Online' ? event.platform : event.location}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <Users className="h-5 w-5 text-blue-600 mt-1" />
                    <div>
                      <p className="font-medium text-gray-900 dark:text-white">Attendees</p>
                      <p className="text-gray-600 dark:text-gray-400">
                        {event.registeredUsers.length} {eventStatus === 'past' ? 'attended' : 'registered'}
                        {event.capacity && ` of ${event.capacity}`}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <User className="h-5 w-5 text-blue-600 mt-1" />
                    <div>
                      <p className="font-medium text-gray-900 dark:text-white">Host</p>
                      <p className="text-gray-600 dark:text-gray-400">
                        {getHostName(event)}
                      </p>
                      {event.hostDesignation && (
                        <p className="text-sm text-gray-500 dark:text-gray-500">
                          {event.hostDesignation}
                        </p>
                      )}
                    </div>
                  </div>

                  {event.timezone && (
                    <div className="flex items-start gap-3">
                      <Globe className="h-5 w-5 text-blue-600 mt-1" />
                      <div>
                        <p className="font-medium text-gray-900 dark:text-white">Timezone</p>
                        <p className="text-gray-600 dark:text-gray-400">{event.timezone}</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </motion.div>
          </div>

          <div className="space-y-6">
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg sticky top-4"
            >
              <div className="text-center mb-6">
                <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                  {event.isPaid ? `${event.currency} ${event.price}` : 'Free'}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  {timeDisplay}
                </div>
              </div>

              {eventStatus === 'upcoming' && spotsLeft !== null && spotsLeft > 0 && spotsLeft <= 10 && (
                <div className="mb-4 p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                  <div className="flex items-center gap-2 text-orange-800 dark:text-orange-200">
                    <AlertCircle className="h-4 w-4" />
                    <span className="text-sm font-medium">
                      Only {spotsLeft} spots left!
                    </span>
                  </div>
                </div>
              )}

              {eventStatus === 'upcoming' && event.registrationDeadline && (!isRegistered)  && (
                <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <div className="text-sm text-blue-800 dark:text-blue-200">
                    <strong>Registration Deadline:</strong><br />
                    {formatDateTime(event.registrationDeadline, event.timezone)}
                  </div>
                </div>
              )}

              <div className="space-y-3">
                {eventStatus === 'upcoming' && !isRegistrationClosed && (
                  isRegistered ? 
                  <></>
                  :
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button
                        className={`w-full py-3 px-4 rounded-lg font-medium transition-colors ${
                          'bg-blue-600 text-white hover:bg-blue-700'
                        }`}
                      >
                        'Register Now'
                      </Button>
                    </DialogTrigger>
                    
                    <DialogContent className="max-w-4xl">
                      <Card>
                        <CardHeader>
                          <DialogHeader>
                            <DialogTitle> Register </DialogTitle>
                            <EventRegisterationForm eventId={event._id} questions={event.questions} setIsRegistered={setIsRegistered} />
                          </DialogHeader>
                        </CardHeader>
                      </Card>
                    </DialogContent>
                  </Dialog>
                )}

                {eventStatus === 'ongoing' && (
                  <button className="w-full py-3 px-4 rounded-lg bg-green-600 text-white hover:bg-green-700 font-medium transition-colors">
                    <PlayCircle className="inline h-4 w-4 mr-2" />
                    Join Live Event
                  </button>
                )}

                {eventStatus === 'past' && (
                  <div className="w-full py-3 px-4 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 text-center font-medium">
                    <Archive className="inline h-4 w-4 mr-2" />
                    Event Completed
                  </div>
                )}

                {eventStatus === 'upcoming' && isRegistrationClosed && (
                  <div className="w-full py-3 px-4 rounded-lg bg-red-100 dark:bg-red-900/20 text-red-600 dark:text-red-400 text-center font-medium">
                    Registration Closed
                  </div>
                )}

                {event.meetingLink && (isRegistered || eventStatus === 'ongoing') && (
                  <button className="w-full py-3 px-4 rounded-lg border border-blue-600 text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors">
                    <ExternalLink className="inline h-4 w-4 mr-2" />
                    {eventStatus === 'ongoing' ? 'Join Meeting' : 'Meeting Link'}
                  </button>
                )}

                {eventStatus === 'past' && event.recordingAccess && (
                  <button className="w-full py-3 px-4 rounded-lg border border-purple-600 text-purple-600 hover:bg-purple-50 dark:hover:bg-purple-900/20 transition-colors">
                    <Video className="inline h-4 w-4 mr-2" />
                    View Recording
                  </button>
                )}
              </div>

              <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                <h3 className="font-medium text-gray-900 dark:text-white mb-3">
                  {eventStatus === 'past' ? 'What was included:' : 'What\'s included:'}
                </h3>
                <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                  {event.certificateOffered && (
                    <li className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      Certificate of completion
                    </li>
                  )}
                  {event.recordingAccess && (
                    <li className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      {eventStatus === 'past' ? 'Recording available' : 'Access to recording'}
                    </li>
                  )}
                  {event.resourcesProvided && (
                    <li className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      Learning resources
                    </li>
                  )}
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    {eventStatus === 'past' ? 'Q&A session included' : 'Live Q&A session'}
                  </li>
                </ul>
              </div>
            </motion.div>
          </div>
        </div>
      </div>

      {showShareModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white dark:bg-gray-800 rounded-xl p-6 max-w-md w-full"
          >
            <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">
              Share Event
            </h3>
            
            <div className="space-y-4">
              <div className="flex items-center gap-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <input
                  type="text"
                  value={shareUrl}
                  readOnly
                  className="flex-1 bg-transparent text-sm text-gray-600 dark:text-gray-400"
                />
                <button
                  onClick={() => copyToClipboard(shareUrl)}
                  className="p-2 text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded"
                >
                  {copySuccess ? <CheckCircle className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                </button>
              </div>
            </div>

            <button
              onClick={() => setShowShareModal(false)}
              className="w-full mt-4 py-2 px-4 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600"
            >
              Close
            </button>
          </motion.div>
        </div>
      )}
    </div>
  );
}