'use client';
import React from 'react';
import { useGetRegisteredEventsQuery } from '@/store/features/events-features/eventsApi';
import EventsComponent from '@/components/custom/dashboardEvents';

const UpcomingEvents = () => {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="mb-4 text-3xl font-bold text-gray-900 dark:text-white">
          Past Registered Events
        </h1>
      </div>

      <EventsComponent type="past" registered={true}/>
    </div>
  );
};

export default UpcomingEvents;
