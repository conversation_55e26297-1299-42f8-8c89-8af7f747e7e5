"use client"

import React from 'react';
import { MapPin, Calendar, Clock, Users, DollarSign, Globe, Building, CheckCircle, Star, Award, BookOpen } from 'lucide-react';
import { useParams } from 'next/navigation';

import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';

import InternshipApplicationForm from '@/components/forms/InternshipApplicationForm';
import { useState } from 'react';
import { useGetInternshipBySlugQuery } from '@/store/internship-features/internshipApi';

const InternshipDisplayPage = () => {
    const params = useParams();
    const slug = params.slug as string;
    const [isApplicationSubmitted, setIsApplicationSubmitted] = useState(false);
        
            const { data, isLoading, isError } = useGetInternshipBySlugQuery(slug);
                console.log(data, isLoading, isError);
            if (isLoading) {
              return <div>Loading...</div>;
            }
            if (isError) {
              return <div>Error...</div>;
            }
            // const data?.data? = data?.data || {};


  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getSkillLevelColor = (level: string) => {
    switch (level) {
      case 'expert': return 'bg-green-100 text-green-800';
      case 'advanced': return 'bg-blue-100 text-blue-800';
      case 'intermediate': return 'bg-yellow-100 text-yellow-800';
      case 'beginner': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft': return 'bg-yellow-100 text-yellow-800';
      case 'published': return 'bg-green-100 text-green-800';
      case 'closed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header Section */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-6 border-l-4 border-blue-500">
          <div className="flex justify-between items-start mb-4">
            <div>
              <div className="flex items-center mb-2">
                <BookOpen className="h-6 w-6 text-blue-600 mr-2" />
                <span className="text-sm font-medium text-blue-600 bg-blue-50 px-2 py-1 rounded">INTERNSHIP</span>
              </div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">{data?.data?.title}</h1>
              <div className="flex items-center space-x-4 text-gray-600">
                <div className="flex items-center">
                  <Building className="h-5 w-5 mr-2" />
                  <span className="font-medium">{data?.data?.company.name}</span>
                </div>
                <div className="flex items-center">
                  <MapPin className="h-5 w-5 mr-2" />
                  <span>{data?.data?.location.city}, {data?.data?.location.state}, {data?.data?.location.country}</span>
                </div>
              </div>
            </div>
            <div className="text-right">
              <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${data?.data?.status && getStatusColor(data?.data?.status)}`}>
                {data?.data?.status && (data?.data?.status.charAt(0).toUpperCase() + data?.data?.status.slice(1))}
              </span>
            </div>
          </div>

          {/* Key Info Bar */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-4 pt-4 border-t">
            <div className="flex items-center">
              <DollarSign className="h-5 w-5 text-green-600 mr-2" />
              <div>
                <p className="text-sm text-gray-600">Stipend</p>
                <p className="font-medium">{data?.data?.stipend}</p>
              </div>
            </div>
            <div className="flex items-center">
              <Clock className="h-5 w-5 text-blue-600 mr-2" />
              <div>
                <p className="text-sm text-gray-600">Duration</p>
                <p className="font-medium">{data?.data?.duration.min}-{data?.data?.duration.max} {data?.data?.duration.frequency}</p>
              </div>
            </div>
            <div className="flex items-center">
              <Globe className="h-5 w-5 text-purple-600 mr-2" />
              <div>
                <p className="text-sm text-gray-600">Mode</p>
                <p className="font-medium capitalize">{data?.data?.mode}</p>
              </div>
            </div>
            <div className="flex items-center">
              <Users className="h-5 w-5 text-orange-600 mr-2" />
              <div>
                <p className="text-sm text-gray-600">Openings</p>
                <p className="font-medium">{data?.data?.openings}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column */}
          <div className="lg:col-span-2 space-y-6">
            {/* About This Internship */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold mb-4 flex items-center">
                <BookOpen className="h-5 w-5 text-blue-600 mr-2" />
                About This Internship
              </h2>
              <p className="text-gray-700 leading-relaxed">{data?.data?.description}</p>
            </div>

            {/* Responsibilities */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold mb-4">Key Responsibilities</h2>
              <ul className="space-y-3">
                {data?.data?.responsibilities.map((resp  : any, index : number) => (
                  <li key={index} className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-700">{resp.title}</span>
                  </li>
                ))}
              </ul>
            </div>

            {/* Skills & Learning */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold mb-4">Skills You'll Learn</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {data?.data?.skills.map((skill:any, index:number) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 transition-colors">
                    <span className="font-medium">{skill.skill}</span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getSkillLevelColor(skill.level)}`}>
                      {skill.level}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {/* Selection Process */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold mb-4">Selection Process</h2>
              <div className="space-y-4">
                {data?.data?.selectionProcess.map((stage: any, index: number) => (
                  <div key={index} className="relative">
                    <div className="border-l-4 border-blue-500 pl-4">
                      <div className="flex items-center">
                        <div className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-medium mr-3">
                          {stage.sortOrder}
                        </div>
                        <h3 className="font-semibold text-gray-900">{stage.stage}</h3>
                      </div>
                      <p className="text-gray-600 mt-2 ml-9">{stage.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Interview Rounds */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold mb-4">Interview Rounds</h2>
              <div className="space-y-4">
                {data?.data?.interviews.map((interview: any, index: number) => (
                  <div key={index} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                    <h3 className="font-semibold text-gray-900 mb-2">{interview.roundTitle}</h3>
                    <p className="text-gray-600 text-sm">{interview.description}</p>
                  </div>
                ))}
              </div>
            </div>

            {/* Application Questions Preview */}
            {data?.data?.questions && data?.data?.questions.length > 0 && (
              <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-xl font-semibold mb-4">Application Questions</h2>
                <p className="text-gray-600 text-sm mb-4">
                  You will be asked to answer the following questions during the application process:
                </p>
                <div className="space-y-3">
                  {data?.data?.questions.map((question: any, index: number) => (
                    <div key={question.questionId || index} className="border-l-4 border-blue-500 pl-4 py-2">
                      <p className="font-medium text-gray-900">
                        {index + 1}. {question.question}
                      </p>
                      {question.hint && (
                        <p className="text-sm text-gray-500 mt-1">
                          Hint: {question.hint}
                        </p>
                      )}
                      <span className={`inline-block mt-2 px-2 py-1 text-xs rounded-full ${
                        question.isRequired
                          ? 'bg-red-100 text-red-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {question.isRequired ? 'Required' : 'Optional'}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Right Column */}
          <div className="space-y-6">
            {/* Quick Actions */}
            <div className="bg-white rounded-lg shadow-md p-6">
              {isApplicationSubmitted ? (
                <button className="w-full bg-green-600 text-white py-3 px-4 rounded-lg font-medium mb-3" disabled>
                  Application Submitted ✓
                </button>
              ) : (
                <Dialog>
                  <DialogTrigger asChild>
                    <button className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 transition duration-200 mb-3">
                      Apply Now
                    </button>
                  </DialogTrigger>

                  <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto overflow-x-hidden p-0">
                    <div className="p-6">
                      <DialogHeader>
                        <DialogTitle>Apply for {data?.data?.title}</DialogTitle>
                      </DialogHeader>
                      <div className="mt-6">
                        <InternshipApplicationForm
                          internshipId={data?.data?._id || ''}
                          questions={data?.data?.questions || []}
                          onSuccess={() => setIsApplicationSubmitted(true)}
                        />
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>
              )}
              <button className="w-full border border-gray-300 text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-50 transition duration-200">
                Save Internship
              </button>
            </div>

            {/* Internship Details */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold mb-4">Internship Details</h3>
              <div className="space-y-3">
                <div>
                  <p className="text-sm text-gray-600">Application Deadline</p>
                  <p className="font-medium">{data?.data?.applicationDeadline && formatDate(data?.data?.applicationDeadline)}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Duration</p>
                  <p className="font-medium">{data?.data?.duration.min}-{data?.data?.duration.max} {data?.data?.duration.frequency}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Project Budget</p>
                  <p className="font-medium">₹{data?.data?.customFields.project_budget.toLocaleString()}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Working Hours</p>
                  <p className="font-medium">{data?.data?.workingHours}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Working Days</p>
                  <p className="font-medium">{data?.data?.workingDays.join(', ')}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Languages</p>
                  <p className="font-medium">{data?.data?.preferredLanguages.join(', ')}</p>
                </div>
              </div>
            </div>

            {/* Perks & Benefits */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold mb-4 flex items-center">
                <Award className="h-5 w-5 text-yellow-500 mr-2" />
                Perks & Benefits
              </h3>
              <div className="space-y-3">
                {data?.data?.perks.map((perk: any, index: number) => (
                  <div key={index} className="flex items-center p-2 bg-yellow-50 rounded-lg">
                    <Star className="h-4 w-4 text-yellow-500 mr-2" />
                    <span className="text-gray-700">{perk.title}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Tags */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold mb-4">Tags</h3>
              <div className="flex flex-wrap gap-2">
                {data?.data?.tags.map((tag: any, index: number) => (
                  <span key={index} className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                    {tag}
                  </span>
                ))}
              </div>
            </div>

            {/* Eligibility */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold mb-4">Eligibility Criteria</h3>
              <ul className="space-y-3">
                {data?.data?.eligibilityCriteria.map((criteria : any, index : number) => (
                  <li key={index} className="flex items-start">
                    <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-700 text-sm">{criteria}</span>
                  </li>
                ))}
              </ul>
            </div>

            {/* Qualifications */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold mb-4">Preferred Qualifications</h3>
              <ul className="space-y-2">
                {data?.data?.qualification.map((qual : any, index : number) => (
                  <li key={index} className="flex items-start">
                    <CheckCircle className="h-4 w-4 text-blue-500 mr-2 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-700 text-sm">{qual}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>

        {/* Footer Info */}
        <div className="bg-white rounded-lg shadow-md p-6 mt-6">
          <div className="flex justify-between items-center text-sm text-gray-600">
           {data?.data?.postedAt &&<span>Posted on { formatDate(data?.data?.postedAt)}</span>}
            {data?.data?.updatedAt && <span>Last updated { formatDate(data?.data?.updatedAt)}</span>}
          </div>
        </div>
      </div>
    </div>
  );
};

export default InternshipDisplayPage;