"use client"
import Heading from '@/components/sections/Heading';
import React from 'react';
import { useGetAllInternshipsQuery } from '@/store/internship-features/internshipApi'; 
import InternshipCard from '@/components/dashboard-components/InternshipCard';
import { IInternship } from '@/types/IInternship';

const JobsPage = () => {
  const { data, isLoading, isError } = useGetAllInternshipsQuery();
  console.log(data, isLoading, isError);
  if (isLoading) {
    return <div>Loading...</div>;
  }
  if (isError) {
    return <div>Error...</div>;
  }
  const internship = data?.data.internships || [];



  return<main>
        
          <div className='min-h-screen '>
            <Heading className='mb-10 md:pl-20'>Explore Internships</Heading>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 max-w-6xl mx-auto gap-5">
              {internship?.map(( internships: IInternship, index:number) => (              
                <InternshipCard key={index} internship={internships} />            
              ))
              }
              
            </div>
          </div>
  
       
      </main>
};

export default JobsPage;
