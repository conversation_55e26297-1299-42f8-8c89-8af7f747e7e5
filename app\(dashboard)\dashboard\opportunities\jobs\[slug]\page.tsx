"use client"

import React, { useState } from 'react';
import { MapPin, Calendar, Clock, Users, DollarSign, Globe, Building, CheckCircle, Star, Briefcase } from 'lucide-react';
import { useParams } from 'next/navigation';
import { useGetJobBySlugQuery } from '@/store/job-features/jobApi';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import JobApplicationForm from '@/components/forms/JobApplicationForm';
import { IJob } from '@/types/IJob';



const JobDisplayPage = () => {
    const [isApplicationSubmitted, setIsApplicationSubmitted] = useState(false);

    const params = useParams();
    const slug = params.slug as string;

    const { data, isLoading, isError } = useGetJobBySlugQuery(slug);
    console.log("Job API Response:", data, "Loading:", isLoading, "Error:", isError);

    if (isLoading) {
      return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading job details...</p>
          </div>
        </div>
      );
    }

    if (isError) {
      console.log("Error fetching job data", isError);
      return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-red-600 mb-4">Error Loading Job</h1>
            <p className="text-gray-600 mb-4">There was an error loading the job details. Please try again.</p>
            <button
              onClick={() => window.location.reload()}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
            >
              Retry
            </button>
          </div>
        </div>
      );
    }

    const jobData = data?.data as IJob | undefined;
    console.log("Job Data:", jobData);

    // Check if job data is empty or missing required fields
    if (!jobData || !jobData.title) {
      return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Job Not Found</h1>
            <p className="text-gray-600 mb-4">The job you're looking for doesn't exist or has been removed.</p>
            <button
              onClick={() => window.history.back()}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
            >
              Go Back
            </button>
          </div>
        </div>
      );
    }

  const formatDate = (dateString: string | Date) => {
    const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getSkillLevelColor = (level?: string) => {
    switch (level) {
      case 'expert': return 'bg-green-100 text-green-800';
      case 'advanced': return 'bg-blue-100 text-blue-800';
      case 'intermediate': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status : string) => {
    switch (status) {
      case 'draft': return 'bg-yellow-100 text-yellow-800';
      case 'published': return 'bg-green-100 text-green-800';
      case 'closed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header Section */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <div className="flex justify-between items-start mb-4">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">{jobData.title}</h1>
              <div className="flex items-center space-x-4 text-gray-600">
                <div className="flex items-center">
                  <Building className="h-5 w-5 mr-2" />
                  <span className="font-medium">{jobData.company?.name || 'Company Name'}</span>
                </div>
                <div className="flex items-center">
                  <MapPin className="h-5 w-5 mr-2" />
                  <span>
                    {[jobData.location?.city, jobData.location?.state, jobData.location?.country]
                      .filter(Boolean)
                      .join(', ') || 'Location not specified'}
                  </span>
                </div>
              </div>
            </div>
            <div className="text-right">
              <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(jobData.status)}`}>
                {jobData.status ? jobData.status.charAt(0).toUpperCase() + jobData.status.slice(1) : 'Draft'}
              </span>
            </div>
          </div>

          {/* Key Info Bar */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-4 pt-4 border-t">
            <div className="flex items-center">
              <DollarSign className="h-5 w-5 text-green-600 mr-2" />
              <div>
                <p className="text-sm text-gray-600">Salary Range</p>
                <p className="font-medium">{jobData.salaryRange || 'Not specified'}</p>
              </div>
            </div>
            <div className="flex items-center">
              <Clock className="h-5 w-5 text-blue-600 mr-2" />
              <div>
                <p className="text-sm text-gray-600">Work Type</p>
                <p className="font-medium capitalize">{jobData.workPlaceType || 'Not specified'}</p>
              </div>
            </div>
            <div className="flex items-center">
              <Globe className="h-5 w-5 text-purple-600 mr-2" />
              <div>
                <p className="text-sm text-gray-600">Mode</p>
                <p className="font-medium capitalize">{jobData.mode || 'Not specified'}</p>
              </div>
            </div>
            <div className="flex items-center">
              <Users className="h-5 w-5 text-orange-600 mr-2" />
              <div>
                <p className="text-sm text-gray-600">Openings</p>
                <p className="font-medium">{jobData.openings || 'Not specified'}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column */}
          <div className="lg:col-span-2 space-y-6">
            {/* Job Description */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold mb-4">Job Description</h2>
              <p className="text-gray-700 leading-relaxed">{jobData.description || 'No description available.'}</p>
            </div>

            {/* Responsibilities */}
            {jobData.responsibilities && jobData.responsibilities.length > 0 && (
              <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-xl font-semibold mb-4">Key Responsibilities</h2>
                <ul className="space-y-3">
                  {jobData.responsibilities.map((resp, index) => (
                    <li key={index} className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700">{resp.title}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Skills Required */}
            {jobData.skills && jobData.skills.length > 0 && (
              <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-xl font-semibold mb-4">Required Skills</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {jobData.skills.map((skill, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <span className="font-medium">{skill.skill}</span>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getSkillLevelColor(skill.level)}`}>
                        {skill.level || 'Not specified'}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Selection Process */}
            {jobData.selectionProcess && jobData.selectionProcess.length > 0 && (
              <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-xl font-semibold mb-4">Selection Process</h2>
                <div className="space-y-4">
                  {jobData.selectionProcess.map((stage, index) => (
                    <div key={index} className="border-l-4 border-blue-500 pl-4">
                      <h3 className="font-semibold text-gray-900">{stage.stage}</h3>
                      <p className="text-gray-600 mt-1">{stage.description}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Right Column */}
          <div className="space-y-6">
            {/* Quick Actions */}
            <div className="bg-white rounded-lg shadow-md p-6">
              {!isApplicationSubmitted ? (
                <Dialog>
                  <DialogTrigger asChild>
                    <button className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 transition duration-200 mb-3 flex items-center justify-center space-x-2">
                      <Briefcase className="h-4 w-4" />
                      <span>Apply Now</span>
                    </button>
                  </DialogTrigger>

                  <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto overflow-x-hidden p-0">
                    <div className="p-6">
                      <DialogHeader>
                        <DialogTitle>Apply for {jobData.title}</DialogTitle>
                      </DialogHeader>
                      <div className="mt-6">
                        <JobApplicationForm
                          jobId={jobData._id || ''}
                          questions={(jobData.questions || []) as any}
                          onSuccess={() => setIsApplicationSubmitted(true)}
                        />
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>
              ) : (
                <button className="w-full bg-green-600 text-white py-3 px-4 rounded-lg font-medium mb-3 flex items-center justify-center space-x-2" disabled>
                  <CheckCircle className="h-4 w-4" />
                  <span>Application Submitted</span>
                </button>
              )}
              <button className="w-full border border-gray-300 text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-50 transition duration-200">
                Save Job
              </button>
            </div>

            {/* Job Details */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold mb-4">Job Details</h3>
              <div className="space-y-3">
                <div>
                  <p className="text-sm text-gray-600">Application Deadline</p>
                  <p className="font-medium">{formatDate(jobData.applicationDeadline)}</p>
                </div>
                {jobData.customFields?.expectedStartDate && (
                  <div>
                    <p className="text-sm text-gray-600">Expected Start Date</p>
                    <p className="font-medium">{jobData.customFields.expectedStartDate}</p>
                  </div>
                )}
                {jobData.customFields?.teamSize && (
                  <div>
                    <p className="text-sm text-gray-600">Team Size</p>
                    <p className="font-medium">{jobData.customFields.teamSize} members</p>
                  </div>
                )}
                {jobData.workingHours && (
                  <div>
                    <p className="text-sm text-gray-600">Working Hours</p>
                    <p className="font-medium">{jobData.workingHours}</p>
                  </div>
                )}
                {jobData.workingDays && jobData.workingDays.length > 0 && (
                  <div>
                    <p className="text-sm text-gray-600">Working Days</p>
                    <p className="font-medium">{jobData.workingDays.join(', ')}</p>
                  </div>
                )}
                {jobData.probation && (
                  <div>
                    <p className="text-sm text-gray-600">Probation Period</p>
                    <p className="font-medium">
                      {jobData.probation.min}-{jobData.probation.max} {jobData.probation.frequency}
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* Perks & Benefits */}
            {jobData.perks && jobData.perks.length > 0 && (
              <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-lg font-semibold mb-4">Perks & Benefits</h3>
                <div className="space-y-2">
                  {jobData.perks.map((perk, index) => (
                    <div key={index} className="flex items-center">
                      <Star className="h-4 w-4 text-yellow-500 mr-2" />
                      <span className="text-gray-700">{perk.title}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Tags */}
            {jobData.tags && jobData.tags.length > 0 && (
              <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-lg font-semibold mb-4">Tags</h3>
                <div className="flex flex-wrap gap-2">
                  {jobData.tags.map((tag, index) => (
                    <span key={index} className="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm">
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* Eligibility */}
            {jobData.eligibilityCriteria && jobData.eligibilityCriteria.length > 0 && (
              <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-lg font-semibold mb-4">Eligibility Criteria</h3>
                <ul className="space-y-2">
                  {jobData.eligibilityCriteria.map((criteria, index) => (
                    <li key={index} className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700 text-sm">{criteria}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </div>

        {/* Footer Info */}
        {(jobData.postedAt || (jobData as any).updatedAt) && (
          <div className="bg-white rounded-lg shadow-md p-6 mt-6">
            <div className="flex justify-between items-center text-sm text-gray-600">
              {jobData.postedAt && <span>Posted on {formatDate(jobData.postedAt)}</span>}
              {(jobData as any).updatedAt && <span>Last updated {formatDate((jobData as any).updatedAt)}</span>}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default JobDisplayPage;