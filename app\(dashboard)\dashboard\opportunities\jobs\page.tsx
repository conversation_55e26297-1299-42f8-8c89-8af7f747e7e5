"use client"
import Heading from '@/components/sections/Heading';
import React from 'react';
import { useGetAllJobsQuery } from '@/store/job-features/jobApi'; 
import JobCard from '@/components/dashboard-components/JobCard';
import { IJob } from '@/types/IJob';

const JobsPage = () => {
  const { data, isLoading, isError } = useGetAllJobsQuery();
  console.log(data, isLoading, isError);
  if (isLoading) {
    return <div>Loading...</div>;
  }
  if (isError) {
    return <div>Error...</div>;
  }
  const job = data?.data.jobs || [];
  console.log("Jobs", job);

  return<main>
        
          <div className='min-h-screen '>
            <Heading className='mb-10 md:pl-20'>Explore Jobs</Heading>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 max-w-6xl mx-auto gap-5">
              {job?.map((jobs : IJob, index:number) => (              
                <JobCard key={index} jobs={jobs} />            
              ))
              }
              
            </div>
          </div>
  
       
      </main>
};

export default JobsPage;
