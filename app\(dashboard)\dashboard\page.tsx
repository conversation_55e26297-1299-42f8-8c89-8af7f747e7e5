'use client';

import { useEffect } from 'react';
import { useAppSelector } from '@/store';
import { useRouter } from 'next/navigation';
import LottieLoader from '@/components/ui/LottieLoader';

export default function DashboardPage() {
  const isLogin = useAppSelector(state => state.auth.isLogin);
  const router = useRouter();

  useEffect(() => {
    if (!isLogin) {
      router.push('/sign-in');
    }
  }, [isLogin, router]);

  if (!isLogin) return null;

  return <div>Welcome to your dashboard <LottieLoader /></div>;
}
