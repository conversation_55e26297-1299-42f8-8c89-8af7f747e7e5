'use client';

import React from 'react';
import {<PERSON>, Bell, Clock, Zap, Shield, Smartphone} from 'lucide-react';

// WhatsApp Logo Component
const WhatsAppLogo = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 32 32" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
    <path d="M16 0c-8.837 0-16 7.163-16 16 0 2.825 0.737 5.607 2.137 8.048l-2.137 7.952 8.17-2.113c2.343 1.225 4.978 1.863 7.83 1.863 8.837 0 16-7.163 16-16s-7.163-16-16-16zM16 29.25c-2.444 0-4.831-0.669-6.919-1.931l-0.497-0.294-5.15 1.331 1.381-5.138-0.325-0.513c-1.394-2.206-2.115-4.744-2.115-7.331 0-7.306 5.944-13.25 13.25-13.25s13.25 5.944 13.25 13.25-5.944 13.25-13.25 13.25z"/>
    <path d="M23.024 19.649c-0.363 1.019-1.806 1.856-2.956 2.094-0.781 0.162-1.806 0.288-5.244-1.125-3.706-1.525-6.081-5.281-6.269-5.525-0.181-0.244-1.481-1.969-1.481-3.756s0.938-2.663 1.269-3.025c0.331-0.362 0.725-0.456 0.969-0.456s0.488 0.006 0.7 0.013c0.225 0.006 0.525-0.088 0.819 0.625 0.331 0.8 1.131 2.756 1.231 2.956 0.1 0.2 0.169 0.431 0.031 0.694-0.131 0.262-0.2 0.431-0.394 0.656-0.194 0.225-0.406 0.5-0.581 0.675-0.194 0.194-0.394 0.4-0.169 0.787 0.225 0.387 1 1.644 2.144 2.663 1.469 1.306 2.706 1.712 3.094 1.9 0.387 0.188 0.612 0.156 0.837-0.094 0.225-0.25 0.969-1.131 1.231-1.519 0.262-0.387 0.519-0.325 0.875-0.194 0.356 0.131 2.256 1.063 2.644 1.256 0.387 0.194 0.644 0.287 0.744 0.45 0.1 0.162 0.1 0.938-0.262 1.831z"/>
  </svg>
);

interface ContactMethod {
  icon: React.ReactNode;
  title: string;
  description: string;
  link: string;
  linkText: string;
}

interface Feature {
  icon: React.ReactNode;
  title: string;
  description: string;
}

const WhatsAppConnectPage: React.FC = () => {
  const phoneNumber = '+91 6204351245'; // Replace with your actual WhatsApp number

  const features: Feature[] = [
    {
      icon: <Zap className="w-6 h-6" />,
      title: 'Instant Messaging',
      description: 'Get real-time responses to your queries and support requests through WhatsApp.'
    },
    {
      icon: <Shield className="w-6 h-6" />,
      title: 'Secure Communication',
      description: 'End-to-end encryption ensures your conversations remain private and secure.'
    },
    {
      icon: <Smartphone className="w-6 h-6" />,
      title: 'Mobile Friendly',
      description: 'Access our services directly from your mobile device with ease.'
    },
    {
      icon: <Clock className="w-6 h-6" />,
      title: '24/7 Availability',
      description: 'Connect with us anytime, anywhere. We\'re always ready to help.'
    }
  ];

  const contactMethods: ContactMethod[] = [
    {
      icon: <WhatsAppLogo className="w-8 h-8" />,
      title: 'Personal Chat',
      description: 'One-on-one conversation for personalized support and assistance.',
      link: `https://wa.me/${phoneNumber}`,
      linkText: 'Start Personal Chat'
    },
    {
      icon: <Users className="w-8 h-8" />,
      title: 'Group Support',
      description: 'Join our community group for updates and peer support.',
      link: 'https://chat.whatsapp.com/grouplink',
      linkText: 'Join Group'
    },
    {
      icon: <Bell className="w-8 h-8" />,
      title: 'Updates Channel',
      description: 'Subscribe to our broadcast list for important announcements.',
      link: 'https://whatsapp.com/channel/updates',
      linkText: 'Subscribe to Updates'
    }
  ];

  
  const handleQuickAction = (message: string) => {
    const encodedMessage = encodeURIComponent(message);
    window.open(`https://wa.me/${phoneNumber}?text=${encodedMessage}`, '_blank');
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300">      

      {/* Hero Section */}
      <section className="py-16 px-4 max-w-7xl mx-auto">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div>
            <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-6">
              Connect with us on{' '}
              <span className="text-green-600 dark:text-green-400">WhatsApp</span>
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">
              Get instant support, updates, and personalized assistance through WhatsApp. 
              Connect with our team for faster responses and seamless communication.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <a 
                href={`https://wa.me/${phoneNumber}`}
                target="_blank" 
                rel="noopener noreferrer"
                className="bg-green-600 dark:bg-green-500 text-white px-8 py-3 rounded-lg hover:bg-green-700 dark:hover:bg-green-600 transition-colors font-semibold text-center inline-flex items-center justify-center space-x-2"
              >
                <WhatsAppLogo className="w-5 h-5" />
                <span>Start Chat Now</span>
              </a>
              <a 
                href="#contact-methods" 
                className="bg-white dark:bg-gray-800 text-green-600 dark:text-green-400 px-8 py-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors font-semibold border border-green-200 dark:border-green-700 text-center"
              >
                View Options
              </a>
            </div>
          </div>
          <div className="flex justify-center">
            <div className="relative">
              <div className="w-80 h-80 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center shadow-2xl">
                <WhatsAppLogo className="w-32 h-32 text-white" />
              </div>
              <div className="absolute -top-4 -right-4 w-20 h-20 bg-white dark:bg-gray-800 rounded-full flex items-center justify-center shadow-lg border-4 border-green-500">
                <Smartphone className="w-8 h-8 text-green-600" />
              </div>
              <div className="absolute -bottom-4 -left-4 w-16 h-16 bg-white dark:bg-gray-800 rounded-full flex items-center justify-center shadow-lg border-4 border-green-500">
                <Zap className="w-6 h-6 text-green-600" />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 px-4 max-w-7xl mx-auto">
        <div className="text-center mb-12">
          <h3 className="text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Why Choose <span className="text-green-600 dark:text-green-400">WhatsApp</span>?
          </h3>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Experience the benefits of instant, secure, and convenient communication through WhatsApp.
          </p>
        </div>
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <div key={index} className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow border border-gray-100 dark:border-gray-700">
              <div className="bg-green-100 dark:bg-green-900/50 w-12 h-12 rounded-full flex items-center justify-center mb-4 border border-green-200 dark:border-green-700">
                <div className="text-green-600 dark:text-green-400">
                  {feature.icon}
                </div>
              </div>
              <h4 className="text-xl font-semibold mb-3 text-gray-900 dark:text-white">{feature.title}</h4>
              <p className="text-gray-600 dark:text-gray-300">{feature.description}</p>
            </div>
          ))}
        </div>
      </section>

      {/* Contact Methods Section */}
      <section id="contact-methods" className="py-16 px-4 max-w-7xl mx-auto">
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8 border border-gray-100 dark:border-gray-700">
          <div className="text-center mb-12">
            <h3 className="text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Choose Your <span className="text-green-600 dark:text-green-400">Connection</span> Method
            </h3>
            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Select the best way to connect with us based on your needs. Whether you prefer personal chat, 
              group discussions, or just want to stay updated.
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            {contactMethods.map((contact, index) => (
              <div key={index} className="text-center">
                <div className="bg-green-100 dark:bg-green-900/50 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 border border-green-200 dark:border-green-700">
                  <div className="text-green-600 dark:text-green-400">
                    {contact.icon}
                  </div>
                </div>
                <h4 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">{contact.title}</h4>
                <p className="text-gray-600 dark:text-gray-300 mb-4">{contact.description}</p>
                <a 
                  href={contact.link} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-green-600 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300 font-medium"
                >
                  {contact.linkText}
                </a>
              </div>
            ))}
          </div>
        </div>
      </section>

      
      {/* CTA Section */}
      <section className="py-16 px-4 max-w-7xl mx-auto">
        <div className="bg-gradient-to-r from-green-50 to-teal-50 dark:from-green-900/20 dark:to-teal-900/20 rounded-2xl p-12 text-center border border-green-100 dark:border-green-800">
          <h3 className="text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white mb-6">
            Ready to <span className="text-green-600 dark:text-green-400">Connect</span>?
          </h3>
          <p className="text-lg text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto">
            Join thousands of satisfied customers who choose WhatsApp for quick, reliable, 
            and secure communication. Get started today!
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a 
              href={`https://wa.me/${phoneNumber}`}
              target="_blank" 
              rel="noopener noreferrer"
              className="bg-green-600 dark:bg-green-500 text-white px-8 py-3 rounded-lg hover:bg-green-700 dark:hover:bg-green-600 transition-colors font-semibold inline-flex items-center justify-center space-x-2"
            >
              <WhatsAppLogo className="w-5 h-5" />
              <span>Start WhatsApp Chat</span>
            </a>
            <a 
              href="#quick-actions" 
              className="bg-white dark:bg-gray-800 text-green-600 dark:text-green-400 px-8 py-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors font-semibold border border-green-200 dark:border-green-700"
            >
              Browse Quick Actions
            </a>
          </div>
        </div>
      </section>

      {/* Business Hours Section */}
      <section className="py-12 px-4 max-w-7xl mx-auto">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-8 border border-gray-100 dark:border-gray-700">
          <div className="text-center">
            <h4 className="text-2xl font-semibold mb-6 text-gray-900 dark:text-white flex items-center justify-center space-x-2">
              <Clock className="w-6 h-6" />
              <span>Business Hours & Response Times</span>
            </h4>
            <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
              <div className="bg-gray-50 dark:bg-gray-700 p-6 rounded-lg">
                <h5 className="font-semibold text-gray-900 dark:text-white mb-3 flex items-center justify-center space-x-2">
                  <Clock className="w-5 h-5" />
                  <span>Business Hours</span>
                </h5>
                <div className="text-gray-600 dark:text-gray-300 space-y-1">
                  <p>Monday - Friday: 9:00 AM - 6:00 PM</p>
                  <p>Saturday: 10:00 AM - 4:00 PM</p>
                  <p>Sunday: Closed</p>
                </div>
              </div>
              <div className="bg-gray-50 dark:bg-gray-700 p-6 rounded-lg">
                <h5 className="font-semibold text-gray-900 dark:text-white mb-3 flex items-center justify-center space-x-2">
                  <Zap className="w-5 h-5" />
                  <span>Response Times</span>
                </h5>
                <div className="text-gray-600 dark:text-gray-300 space-y-1">
                  <p>Business Hours: Within 30 minutes</p>
                  <p>After Hours: Within 2-4 hours</p>
                  <p>Weekends: Within 24 hours</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      
    </div>
  );
};

export default WhatsAppConnectPage;