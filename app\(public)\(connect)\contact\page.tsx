'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import axios from 'axios';
import { z } from 'zod';
import ContactSection from '@/components/custom/contactForm';

const contactSchema = z.object({
  fullName: z.string().min(1, 'Full name is required'),
  email: z.string().email('Invalid email address'),
  phone: z.string().optional(),
  subject: z.string().min(1, 'Subject is required'),
  message: z.string().min(1, 'Message is required'),
});

const contactInfo = [
  {
    icon: '📧',
    title: 'Email Us',
    description:
      'Send us an email and we will get back to you within 24 hours.',
    contact: '<EMAIL>',
    action: 'mailto:<EMAIL>',
    bgColor:
      'from-orange-50 to-orange-100 dark:from-orange-900/30 dark:to-orange-800/30',
    iconBg: 'bg-orange-100 dark:bg-orange-900',
    hoverBg:
      'hover:from-orange-100 hover:to-orange-200 dark:hover:from-orange-800/40 dark:hover:to-orange-700/40',
  },
  {
    icon: '📞',
    title: 'Call Us',
    description: 'Speak directly with our team during business hours.',
    contact: '+91 6204351245',
    action: 'tel:+916204351245',
    bgColor:
      'from-green-50 to-green-100 dark:from-green-900/30 dark:to-green-800/30',
    iconBg: 'bg-green-100 dark:bg-green-900',
    hoverBg:
      'hover:from-green-100 hover:to-green-200 dark:hover:from-green-800/40 dark:hover:to-green-700/40',
  },
  {
    icon: '📍',
    title: 'Visit Us',
    description: 'Come visit our office for an in-person conversation.',
    contact:
      '01, Ground Floor, BOI Audit Office Building, Pragati Path, Makchund Toli, Ranchi - 834001',
    action: '#',
    bgColor:
      'from-purple-50 to-purple-100 dark:from-purple-900/30 dark:to-purple-800/30',
    iconBg: 'bg-purple-100 dark:bg-purple-900',
    hoverBg:
      'hover:from-purple-100 hover:to-purple-200 dark:hover:from-purple-800/40 dark:hover:to-purple-700/40',
  },
];

const ContactPage = () => {
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    phone: '',
    subject: '',
    message: '',
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<
    'idle' | 'success' | 'error'
  >('idle');
  const [formError, setFormError] = useState<string | null>(null);

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormError(null);

    // Validate with zod
    const result = contactSchema.safeParse(formData);
    if (!result.success) {
      setFormError(result.error.errors[0]?.message || 'Validation failed');
      setSubmitStatus('error');
      return;
    }

    setIsSubmitting(true);

    try {
      const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || '';
      const url = new URL('/api/v1/contacts', baseUrl);
      const response = await axios.post(url.toString(), formData);

      if (response.data?.success) {
        setSubmitStatus('success');
        setFormData({
          fullName: '',
          email: '',
          phone: '',
          subject: '',
          message: '',
        });
        setTimeout(() => setSubmitStatus('idle'), 3000);
      } else {
        console.log(response.data);
        setFormError(response.data?.message || 'Submission failed');
        setSubmitStatus('error');
      }
    } catch (error: any) {
      console.log(error.response.data);
      setFormError(error?.response?.data?.message || 'Submission failed');
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <main className="font-urbanist min-h-screen bg-gradient-to-br from-slate-50 to-gray-100 dark:from-gray-900 dark:to-slate-900">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 md:px-8 lg:px-16 xl:px-40">
          <div className="grid items-center gap-12 lg:grid-cols-2">
            <div className="space-y-8">
              <div className="space-y-6">
                <h1 className="font-nunito text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl lg:text-6xl dark:text-white">
                  Get in{' '}
                  <span className="bg-gradient-to-r from-blue-600 to-purple-800 bg-clip-text text-transparent">
                    Touch
                  </span>
                </h1>
                <p className="text-lg leading-relaxed text-gray-600 dark:text-gray-300">
                  We would love to hear from you! Whether you have questions
                  about current opportunities, need more information about our
                  company, or simply want to share your thoughts, our team is
                  here to help.
                </p>
              </div>
            </div>
            <div className="relative">
              <div className="absolute -inset-4 rounded-2xl bg-gradient-to-r from-blue-400 to-purple-400 opacity-20 blur-lg"></div>
              <Image
                src="https://images.unsplash.com/photo-1423666639041-f56000c27a9a?w=600&h=600&fit=crop"
                width={500}
                height={500}
                className="relative rounded-2xl shadow-xl"
                alt="Contact us"
                priority
              />
            </div>
          </div>
        </div>
      </section>

      {/* Contact Methods Section */}
      <section className="relative bg-white py-24 lg:py-32 dark:bg-gray-800">
        <div className="absolute inset-0 bg-gradient-to-b from-gray-50/50 to-transparent dark:from-gray-700/50"></div>
        <div className="relative mx-auto max-w-7xl px-4 sm:px-6 md:px-8 lg:px-16 xl:px-40">
          <div className="mb-20 text-center">
            <div className="mb-6 inline-flex items-center gap-2 rounded-full bg-gradient-to-r from-blue-100 to-purple-100 px-4 py-2 text-sm font-medium text-gray-700 dark:from-blue-900/50 dark:to-purple-900/50 dark:text-gray-300">
              <span className="text-lg">🌟</span>
              Multiple Contact Options
            </div>
            <h2 className="font-nunito mb-4 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl lg:text-5xl dark:text-white">
              Choose Your Preferred{' '}
              <span className="bg-gradient-to-r from-blue-600 to-purple-800 bg-clip-text text-transparent">
                Communication
              </span>
            </h2>
            <p className="mx-auto max-w-2xl text-lg text-gray-600 dark:text-gray-300">
              We offer multiple ways to get in touch. Select the method that
              works best for you and we'll respond promptly.
            </p>
          </div>
          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            {contactInfo.map((info, index) => (
              <div
                key={index}
                className={`group relative rounded-2xl bg-gradient-to-br ${info.bgColor} p-8 text-center transition-all duration-500 hover:scale-105 hover:shadow-xl ${info.hoverBg} border border-white/50 backdrop-blur-sm dark:border-gray-700/50`}
              >
                <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-white/20 to-transparent opacity-0 transition-opacity duration-500 group-hover:opacity-100 dark:from-white/5"></div>
                <div className="relative">
                  <div
                    className={`mx-auto mb-6 flex h-20 w-20 items-center justify-center rounded-2xl ${info.iconBg} shadow-lg transition-all duration-500 group-hover:scale-110 group-hover:shadow-xl`}
                  >
                    <span className="text-3xl">{info.icon}</span>
                  </div>
                  <h3 className="font-nunito mb-3 text-xl font-bold text-gray-900 transition-colors group-hover:text-gray-800 dark:text-white dark:group-hover:text-gray-200">
                    {info.title}
                  </h3>
                  <p className="mb-6 text-sm leading-relaxed text-gray-700 dark:text-gray-300">
                    {info.description}
                  </p>
                  <a
                    href={info.action}
                    className="inline-block max-w-full rounded-lg bg-white/50 px-3 py-2 text-sm font-semibold break-words hyphens-auto text-gray-800 transition-all duration-300 hover:bg-white/70 hover:text-gray-900 dark:bg-gray-800/50 dark:text-gray-200 dark:hover:bg-gray-700/70 dark:hover:text-white"
                    style={{ wordBreak: 'break-word' }}
                  >
                    {info.contact}
                  </a>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Form Section */}
      <ContactSection />
      {/* <section className="relative overflow-hidden bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30 py-24 lg:py-32 dark:from-gray-900 dark:via-blue-900/10 dark:to-purple-900/10">
        <div className="pointer-events-none absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(59,130,246,0.1),transparent_50%)] dark:bg-[radial-gradient(circle_at_30%_20%,rgba(59,130,246,0.05),transparent_50%)]"></div>
        <div className="pointer-events-none absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(147,51,234,0.1),transparent_50%)] dark:bg-[radial-gradient(circle_at_70%_80%,rgba(147,51,234,0.05),transparent_50%)]"></div>

        <div className="relative mx-auto max-w-7xl px-4 sm:px-6 md:px-8 lg:px-16 xl:px-40">
          {/* Section Header *}
          <div className="mb-16 text-center">
            <div className="mb-6 inline-flex items-center gap-2 rounded-full bg-gradient-to-r from-green-100 to-blue-100 px-4 py-2 text-sm font-medium text-gray-700 dark:from-green-900/50 dark:to-blue-900/50 dark:text-gray-300">
              <span className="text-lg">✉️</span>
              Direct Communication
            </div>
            <h2 className="font-nunito mb-4 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl lg:text-5xl dark:text-white">
              Get in{' '}
              <span className="bg-gradient-to-r from-blue-600 to-purple-800 bg-clip-text text-transparent">
                Touch
              </span>
            </h2>
            <p className="mx-auto max-w-2xl text-lg text-gray-600 dark:text-gray-300">
              Ready to start a conversation? Fill out the form below and we'll
              get back to you as soon as possible.
            </p>
          </div>

          <div className="grid items-start gap-16 lg:grid-cols-2">
            {/* Left Side - Image *}
            <div className="relative order-2 lg:order-1">
              <div className="absolute -inset-6 rounded-3xl bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 opacity-20 blur-2xl"></div>
              <div className="relative rounded-3xl bg-white p-3 shadow-2xl dark:bg-gray-800">
                <Image
                  src="https://images.unsplash.com/photo-1556761175-b413da4baf72?w=600&h=600&fit=crop"
                  width={500}
                  height={500}
                  className="rounded-2xl"
                  alt="Contact form"
                />
              </div>

              {/* Floating Elements *}
              <div className="absolute -top-4 -right-4 rounded-2xl bg-white p-4 shadow-xl dark:bg-gray-800">
                <div className="flex items-center gap-2">
                  <div className="h-3 w-3 rounded-full bg-green-500"></div>
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Online Now
                  </span>
                </div>
              </div>

              <div className="absolute -bottom-4 -left-4 rounded-2xl bg-white p-4 shadow-xl dark:bg-gray-800">
                <div className="flex items-center gap-2">
                  <span className="text-lg">⚡</span>
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Quick Response
                  </span>
                </div>
              </div>
            </div>

            {/* Right Side - Form Card *}
            <div className="order-1 lg:order-2">
              <div className="relative">
                {/* Card Background with Gradient Border *}
                <div className="absolute -inset-1 rounded-3xl bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 opacity-20 blur-lg"></div>
                <div className="relative rounded-3xl border border-white/50 bg-white/80 p-8 shadow-2xl backdrop-blur-xl lg:p-10 dark:border-gray-700/50 dark:bg-gray-800/80">
                  {/* Form Header *}
                  <div className="mb-6 text-center">
                    <div className="mb-3 inline-flex h-10 w-10 items-center justify-center rounded-lg bg-gray-100 dark:bg-gray-700">
                      <span className="text-lg text-gray-600 dark:text-gray-300">
                        📝
                      </span>
                    </div>
                    <h3 className="font-nunito mb-2 text-lg font-semibold text-gray-900 dark:text-white">
                      Reach Out Confidentially
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300">
                      Your privacy is our priority — your information is never
                      shared, and we'll never spam your inbox or phone.
                    </p>

                    {/* Privacy Badge *}
                    <div className="mt-3 mb-3 inline-flex items-center gap-2 rounded-lg border border-green-200 bg-green-50 px-3 py-2 dark:border-green-700 dark:bg-green-900/30">
                      <div className="flex-shrink-0">
                        <span className="text-sm text-green-600 dark:text-green-400">
                          🔒
                        </span>
                      </div>
                      <span className="text-xs font-medium text-green-800 dark:text-green-300">
                        Your data is safe with us
                      </span>
                    </div>
                  </div>

                  {/* Success/Error Message *}
                  {submitStatus === 'success' && (
                    <div className="mb-4 rounded-lg border border-green-200/50 bg-green-50 p-3 dark:border-green-700/50 dark:bg-green-900/30">
                      <div className="flex items-center gap-2">
                        <div className="flex h-5 w-5 flex-shrink-0 items-center justify-center rounded-full bg-green-100 dark:bg-green-800">
                          <span className="text-xs text-green-600 dark:text-green-300">
                            ✓
                          </span>
                        </div>
                        <p className="text-xs font-medium text-green-800 dark:text-green-300">
                          Message sent successfully!
                        </p>
                      </div>
                    </div>
                  )}
                  {formError && (
                    <div className="mb-4 rounded-lg border border-red-200/50 bg-red-50 p-3 dark:border-red-700/50 dark:bg-red-900/30">
                      <div className="flex items-center gap-2">
                        <div className="flex h-5 w-5 flex-shrink-0 items-center justify-center rounded-full bg-red-100 dark:bg-red-800">
                          <span className="text-xs text-red-600 dark:text-red-300">
                            !
                          </span>
                        </div>
                        <p className="text-xs font-medium text-red-800 dark:text-red-300">
                          {formError}
                        </p>
                      </div>
                    </div>
                  )}
                  {/* Contact Form *}
                  <form onSubmit={handleSubmit} className="space-y-3">
                    <div>
                      <label
                        htmlFor="fullName"
                        className="mb-1 block text-xs font-medium text-gray-700 dark:text-gray-300"
                      >
                        Full Name *
                      </label>
                      <input
                        type="text"
                        id="fullName"
                        name="fullName"
                        value={formData.fullName}
                        onChange={handleInputChange}
                        required
                        className="w-full rounded-lg border border-gray-200 bg-gray-50/30 px-3 py-2 text-sm text-gray-900 placeholder-gray-400 transition-all duration-300 focus:border-gray-400 focus:bg-white focus:ring-1 focus:ring-gray-200/50 focus:outline-none dark:border-gray-600 dark:bg-gray-700/30 dark:text-white dark:placeholder-gray-500 dark:focus:border-gray-500 dark:focus:bg-gray-700 dark:focus:ring-gray-600/50"
                        placeholder="Your full name"
                      />
                    </div>

                    <div>
                      <label
                        htmlFor="email"
                        className="mb-1 block text-xs font-medium text-gray-700 dark:text-gray-300"
                      >
                        Email Address *
                      </label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        required
                        className="w-full rounded-lg border border-gray-200 bg-gray-50/30 px-3 py-2 text-sm text-gray-900 placeholder-gray-400 transition-all duration-300 focus:border-gray-400 focus:bg-white focus:ring-1 focus:ring-gray-200/50 focus:outline-none dark:border-gray-600 dark:bg-gray-700/30 dark:text-white dark:placeholder-gray-500 dark:focus:border-gray-500 dark:focus:bg-gray-700 dark:focus:ring-gray-600/50"
                        placeholder="<EMAIL>"
                      />
                    </div>

                    <div>
                      <label
                        htmlFor="phone"
                        className="mb-1 block text-xs font-medium text-gray-700 dark:text-gray-300"
                      >
                        Phone Number
                      </label>
                      <input
                        type="tel"
                        id="phone"
                        name="phone"
                        value={formData.phone}
                        onChange={handleInputChange}
                        className="w-full rounded-lg border border-gray-200 bg-gray-50/30 px-3 py-2 text-sm text-gray-900 placeholder-gray-400 transition-all duration-300 focus:border-gray-400 focus:bg-white focus:ring-1 focus:ring-gray-200/50 focus:outline-none dark:border-gray-600 dark:bg-gray-700/30 dark:text-white dark:placeholder-gray-500 dark:focus:border-gray-500 dark:focus:bg-gray-700 dark:focus:ring-gray-600/50"
                        placeholder="Your phone number"
                      />
                    </div>

                    <div>
                      <label
                        htmlFor="subject"
                        className="mb-1 block text-xs font-medium text-gray-700 dark:text-gray-300"
                      >
                        Subject
                      </label>
                      <input
                        type="text"
                        id="subject"
                        name="subject"
                        value={formData.subject}
                        onChange={handleInputChange}
                        className="w-full rounded-lg border border-gray-200 bg-gray-50/30 px-3 py-2 text-sm text-gray-900 placeholder-gray-400 transition-all duration-300 focus:border-gray-400 focus:bg-white focus:ring-1 focus:ring-gray-200/50 focus:outline-none dark:border-gray-600 dark:bg-gray-700/30 dark:text-white dark:placeholder-gray-500 dark:focus:border-gray-500 dark:focus:bg-gray-700 dark:focus:ring-gray-600/50"
                        placeholder="Your Subject"
                      />
                    </div>

                    <div>
                      <label
                        htmlFor="message"
                        className="mb-1 block text-xs font-medium text-gray-700 dark:text-gray-300"
                      >
                        Message *
                      </label>
                      <textarea
                        id="message"
                        name="message"
                        value={formData.message}
                        onChange={handleInputChange}
                        required
                        rows={3}
                        className="w-full rounded-lg border border-gray-200 bg-gray-50/30 px-3 py-2 text-sm text-gray-900 placeholder-gray-400 transition-all duration-300 focus:border-gray-400 focus:bg-white focus:ring-1 focus:ring-gray-200/50 focus:outline-none dark:border-gray-600 dark:bg-gray-700/30 dark:text-white dark:placeholder-gray-500 dark:focus:border-gray-500 dark:focus:bg-gray-700 dark:focus:ring-gray-600/50"
                        placeholder="Your message..."
                      />
                    </div>

                    <button
                      type="submit"
                      disabled={isSubmitting}
                      className="w-full rounded-lg bg-gray-900 px-4 py-2.5 text-sm font-medium text-white shadow-sm transition-all duration-300 hover:bg-gray-800 hover:shadow-md focus:ring-1 focus:ring-gray-500/20 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50 dark:bg-gray-100 dark:text-gray-900 dark:hover:bg-gray-200 dark:focus:ring-gray-400/20"
                    >
                      {isSubmitting ? (
                        <span className="flex items-center justify-center gap-2">
                          <svg
                            className="h-3 w-3 animate-spin"
                            viewBox="0 0 24 24"
                          >
                            <circle
                              className="opacity-25"
                              cx="12"
                              cy="12"
                              r="10"
                              stroke="currentColor"
                              strokeWidth="4"
                              fill="none"
                            />
                            <path
                              className="opacity-75"
                              fill="currentColor"
                              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                            />
                          </svg>
                          Sending...
                        </span>
                      ) : (
                        'Send Message'
                      )}
                    </button>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section> */}

      {/* Call to Action Section */}
      <section className="py-20 lg:py-32">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 md:px-8 lg:px-16 xl:px-40">
          <div className="grid gap-8 md:grid-cols-2">
            <div className="rounded-2xl bg-gradient-to-br from-blue-50 to-indigo-100 p-8 shadow-lg dark:from-blue-900/30 dark:to-indigo-900/30">
              <h3 className="mb-4 text-2xl font-bold text-gray-900 dark:text-white">
                🚀 Ready to Start Your Journey?
              </h3>
              <div className="space-y-4 text-gray-700 dark:text-gray-300">
                <p>
                  Join our team of innovative professionals and be part of
                  exciting projects that shape the future of technology.
                </p>
                <p>
                  Explore our current job openings and find the perfect role
                  that matches your skills and ambitions.
                </p>
              </div>
            </div>
            <div className="rounded-2xl bg-gradient-to-br from-green-50 to-emerald-100 p-8 shadow-lg dark:from-green-900/30 dark:to-emerald-900/30">
              <h3 className="mb-4 text-2xl font-bold text-gray-900 dark:text-white">
                💼 Have a Project in Mind?
              </h3>
              <div className="space-y-4 text-gray-700 dark:text-gray-300">
                <p>
                  Let us help you bring your ideas to life with our
                  comprehensive IT solutions and expert development team.
                </p>
                <p>
                  From web applications to mobile apps, we have the expertise to
                  deliver exceptional results.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
};

export default ContactPage;
