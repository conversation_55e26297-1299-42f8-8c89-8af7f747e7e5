'use client';
import React, { useState } from 'react'; // useState is no longer strictly needed for form handling, but kept for client component

// Define an interface for the shape of each partnership type object
interface PartnershipType {
  icon: string;
  title: string;
  description: string;
  bgColor: string;
  iconBg: string;
  hoverBg: string;
}

const partnershipTypes: PartnershipType[] = [
  {
    icon: '🤝',
    title: 'Strategic Partnership',
    description: 'Long-term collaboration for mutual growth and innovation.',
    bgColor: 'from-blue-50 to-blue-100 dark:from-blue-900/30 dark:to-blue-800/30',
    iconBg: 'bg-blue-100 dark:bg-blue-900',
    hoverBg: 'hover:from-blue-100 hover:to-blue-200 dark:hover:from-blue-800/40 dark:hover:to-blue-700/40',
  },
  {
    icon: '💼',
    title: 'Business Alliance',
    description: 'Joint ventures and collaborative business opportunities.',
    bgColor: 'from-purple-50 to-purple-100 dark:from-purple-900/30 dark:to-purple-800/30',
    iconBg: 'bg-purple-100 dark:bg-purple-900',
    hoverBg: 'hover:from-purple-100 hover:to-purple-200 dark:hover:from-purple-800/40 dark:hover:to-purple-700/40',
  },
  {
    icon: '🔧',
    title: 'Technology Partner',
    description: 'Integration and technical collaboration opportunities.',
    bgColor: 'from-green-50 to-green-100 dark:from-green-900/30 dark:to-green-800/30',
    iconBg: 'bg-green-100 dark:bg-green-900',
    hoverBg: 'hover:from-green-100 hover:to-green-200 dark:hover:from-green-800/40 dark:hover:to-green-700/40',
  },
  {
    icon: '🌐',
    title: 'Reseller Program',
    description: 'Become an authorized reseller of our solutions.',
    bgColor: 'from-orange-50 to-orange-100 dark:from-orange-900/30 dark:to-orange-800/30',
    iconBg: 'bg-orange-100 dark:bg-orange-900',
    hoverBg: 'hover:from-orange-100 hover:to-orange-200 dark:hover:from-orange-800/40 dark:hover:to-orange-700/40',
  },
];

const PartnershipEnquiries = () => {
  // Removed formData, isSubmitting, and submitStatus state as the form is removed.
  // Removed handleInputChange and handleSubmit functions as they are no longer needed.

  return (
    <main className="font-urbanist min-h-screen bg-gradient-to-br from-slate-50 to-gray-100 dark:from-gray-900 dark:to-slate-900">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 md:px-8 lg:px-16 xl:px-40">
          <div className="grid items-center gap-12 lg:grid-cols-2">
            <div className="space-y-8">
              <div className="space-y-6">
                <h1 className="font-nunito text-4xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-5xl lg:text-6xl">
                  Partnership{' '}
                  <span className="bg-gradient-to-r from-blue-600 to-purple-800 bg-clip-text text-transparent">
                    Enquiries
                  </span>
                </h1>
                <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed">
                  Ready to grow together? Explore partnership opportunities with Sudha Software
                  Solutions. Whether you're looking for strategic alliances, technology integration,
                  or business collaboration, we're here to build meaningful partnerships that drive
                  mutual success.
                </p>
              </div>
            </div>
            <div className="relative">
              <div className="absolute -inset-4 rounded-2xl bg-gradient-to-r from-blue-400 to-purple-400 opacity-20 blur-lg"></div>
              <div className="relative rounded-2xl bg-white dark:bg-gray-800 p-8 shadow-xl">
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">50+</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">Active Partners</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">15+</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">Countries</div>
                  </div>
                </div>
                <div className="text-center">
                  <div className="inline-flex items-center gap-2 bg-gradient-to-r from-green-100 to-blue-100 dark:from-green-900/50 dark:to-blue-900/50 rounded-full px-4 py-2">
                    <span className="text-lg">🌟</span>
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Growing Network
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Partnership Types Section */}
      <section className="py-24 lg:py-32 bg-white dark:bg-gray-800 relative">
        <div className="absolute inset-0 bg-gradient-to-b from-gray-50/50 to-transparent dark:from-gray-700/50"></div>
        <div className="mx-auto max-w-7xl px-4 sm:px-6 md:px-8 lg:px-16 xl:px-40 relative">
          <div className="text-center mb-20">
            <div className="inline-flex items-center gap-2 rounded-full bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-900/50 dark:to-purple-900/50 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 mb-6">
              <span className="text-lg">🤝</span>
              Partnership Opportunities
            </div>
            <h2 className="font-nunito text-3xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl lg:text-5xl mb-4">
              Choose Your{' '}
              <span className="bg-gradient-to-r from-blue-600 to-purple-800 bg-clip-text text-transparent">
                Partnership Type
              </span>
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              We offer various partnership models designed to create value for both parties and
              foster long-term collaboration.
            </p>
          </div>
          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
            {partnershipTypes.map((type, index) => (
              <div
                key={index}
                className={`group relative rounded-2xl bg-gradient-to-br ${type.bgColor} p-6 text-center transition-all duration-500 hover:shadow-xl hover:scale-105 ${type.hoverBg} border border-white/50 dark:border-gray-700/50 backdrop-blur-sm`}
              >
                <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-white/20 to-transparent dark:from-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="relative">
                  <div
                    className={`mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-2xl ${type.iconBg} shadow-lg group-hover:shadow-xl transition-all duration-500 group-hover:scale-110`}
                  >
                    <span className="text-2xl">{type.icon}</span>
                  </div>
                  <h3 className="font-nunito mb-3 text-lg font-bold text-gray-900 dark:text-white group-hover:text-gray-800 dark:group-hover:text-gray-200 transition-colors">
                    {type.title}
                  </h3>
                  <p className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
                    {type.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Replaced Partnership Form Section with CTA */}
      <section className="py-24 lg:py-32 bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30 dark:from-gray-900 dark:via-blue-900/10 dark:to-purple-900/10 relative overflow-hidden">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(59,130,246,0.1),transparent_50%)] dark:bg-[radial-gradient(circle_at_30%_20%,rgba(59,130,246,0.05),transparent_50%)] pointer-events-none"></div>
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(147,51,234,0.1),transparent_50%)] dark:bg-[radial-gradient(circle_at_70%_80%,rgba(147,51,234,0.05),transparent_50%)] pointer-events-none"></div>

        <div className="mx-auto max-w-7xl px-4 sm:px-6 md:px-8 lg:px-16 xl:px-40 relative text-center">
          {/* Section Header */}
          <div className="mb-16">
            <div className="inline-flex items-center gap-2 rounded-full bg-gradient-to-r from-green-100 to-blue-100 dark:from-green-900/50 dark:to-blue-900/50 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 mb-6">
              <span className="text-lg">✨</span>
              Start Your Journey
            </div>
            <h2 className="font-nunito text-3xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl lg:text-5xl mb-4">
              Ready to{' '}
              <span className="bg-gradient-to-r from-blue-600 to-purple-800 bg-clip-text text-transparent">
                Collaborate?
              </span>
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              If you're eager to discuss a potential partnership and explore how we can achieve mutual success,
              reach out to our partnership team today.
            </p>
          </div>

          <div className="max-w-xl mx-auto">
            <div className="relative">
              {/* CTA Card Background with Gradient Border */}
              <div className="absolute -inset-1 rounded-3xl bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 opacity-20 blur-lg"></div>
              <div className="relative rounded-3xl bg-white/10 dark:bg-gray-800/80 backdrop-blur-xl border border-white/50 dark:border-gray-700/50 shadow-2xl p-8 lg:p-10 flex flex-col items-center justify-center">
                <div className="mb-6">
                  <div className="inline-flex items-center justify-center w-12 h-12 rounded-lg bg-gray-100 dark:bg-gray-700 mb-4">
                    <span className="text-xl text-gray-600 dark:text-gray-300">📧</span>
                  </div>
                  <h3 className="font-nunito text-xl font-semibold text-gray-900 dark:text-white mb-2">
                    Contact Our Partnership Team
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    Send us an email with your partnership proposal.
                  </p>
                </div>

                {/* The CTA Button */}
                <a
                  href="mailto:<EMAIL>"
                  className="inline-flex items-center justify-center px-8 py-4 rounded-lg bg-blue-500 dark:bg-blue-600 text-gray-100 hover:text-gray-700 dark:hover:text-gray-50 font-medium shadow-sm transition-all duration-300 hover:bg-blue-300 dark:hover:bg-blue-500 hover:shadow-md focus:outline-none focus:ring-1 focus:ring-gray-500/20 dark:focus:ring-gray-400/20"
                >
                  <span className="mr-2">✉️</span> Send Partnership Email
                </a>

                {/* Optional: Add a trust badge if desired, or remove if not applicable */}
                <div className="inline-flex items-center gap-2 bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-700 rounded-lg px-4 py-2 mt-6">
                  <span className="text-blue-600 dark:text-blue-400 text-sm">🌐</span>
                  <span className="text-xs font-medium text-blue-800 dark:text-blue-300">
                    Quick Response Guaranteed
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 lg:py-32">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 md:px-8 lg:px-16 xl:px-40">
          <div className="text-center mb-16">
            <h2 className="font-nunito text-3xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl mb-4">
              Why Partner With{' '}
              <span className="bg-gradient-to-r from-blue-600 to-purple-800 bg-clip-text text-transparent">
                Us?
              </span>
            </h2>
          </div>
          <div className="grid gap-8 md:grid-cols-2">
            <div className="rounded-2xl bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-blue-900/30 dark:to-indigo-900/30 p-8 shadow-lg">
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                🎯 Mutual Growth Opportunities
              </h3>
              <div className="space-y-4 text-gray-700 dark:text-gray-300">
                <p>
                  Access new markets, expand your customer base, and leverage our expertise to
                  accelerate your business growth.
                </p>
                <p>
                  Benefit from co-marketing opportunities, joint sales efforts, and shared resources
                  to maximize ROI.
                </p>
              </div>
            </div>
            <div className="rounded-2xl bg-gradient-to-br from-green-50 to-emerald-100 dark:from-green-900/30 dark:to-emerald-900/30 p-8 shadow-lg">
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                🤝 Dedicated Partnership Support
              </h3>
              <div className="space-y-4 text-gray-700 dark:text-gray-300">
                <p>
                  Get dedicated account management, technical support, and training resources to
                  ensure partnership success.
                </p>
                <p>
                  Access exclusive partner portals, marketing materials, and competitive pricing
                  structures.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
};

export default PartnershipEnquiries;