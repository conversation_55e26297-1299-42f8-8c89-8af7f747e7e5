// app/support/page.tsx
import React from 'react';
import { Metadata } from 'next';
import Image from 'next/image'; // Import Image component
import HeroComponent from '@/components/sections/HeroComponent';
import { Heading } from '@/components/sections/Heading';
import { Paragraph } from '@/components/sections/paragraph';
import SupportForm from '@/components/custom/supportForm';

// Force dynamic rendering (server-side) to avoid static generation errors
export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: 'Support & Help - Sudha Software Solutions | Get Expert Assistance',
  description:
    'Get comprehensive support and help from Sudha Software Solutions. Access documentation, contact our expert team, and find solutions to your technical challenges.',
  keywords:
    'Support, Help, Technical Support, Customer Service, Documentation, Sudha Software Solutions, Assistance',
  openGraph: {
    title: 'Support & Help - Sudha Software Solutions',
    description:
      'Access comprehensive support resources and get expert assistance from our dedicated team at Sudha Software Solutions.',
    images: ['/assets/banner/support-banner.jpg'],
    url: 'https://careers.sudhasoftwaresolutions.com/support',
    type: 'website',
  },
  twitter: {
    title: 'Support & Help - Sudha Software Solutions',
    description:
      'Get the support you need with our comprehensive help resources and expert assistance team.',
    images: ['/assets/banner/support-banner.jpg'],
    card: 'summary_large_image',
    site: '@sudha_software_solutions',
    creator: '@sudha_software_solutions',
  },
};

const supportFeatures = [
  {
    icon: '🎯',
    title: '24/7 Expert Support',
    description: 'Round-the-clock assistance from our experienced technical team.',
  },
  {
    icon: '📚',
    title: 'Comprehensive Documentation',
    description:
      'Access detailed guides, tutorials, and best practices for all our solutions.',
  },
  {
    icon: '⚡',
    title: 'Quick Response Time',
    description:
      'Get fast responses to your queries with our streamlined support process.',
  },
  {
    icon: '🔧',
    title: 'Personalized Solutions',
    description:
      'Tailored support solutions designed to meet your specific business needs.',
  },
];

const helpResources = [
  {
    icon: '💬',
    title: 'Live Chat Support',
    description: 'Instant assistance through our live chat feature during business hours.',
  },
  {
    icon: '📞',
    title: 'Phone Support',
    description:
      'Direct phone support for urgent issues and complex technical discussions.',
  },
  {
    icon: '🎓',
    title: 'Training Resources',
    description:
      'Access video tutorials, webinars, and training materials to enhance your skills.',
  },
  {
    icon: '📋',
    title: 'Ticket System',
    description:
      'Organized ticket management system for tracking and resolving your issues.',
  },
];

type FeatureItemProps = {
  icon: React.ReactNode;
  title: string;
  description: string;
  highlight?: boolean;
};

const FeatureItem = ({
  icon,
  title,
  description,
  highlight = false,
}: FeatureItemProps) => {
  return (
    <div
      className={`group relative rounded-2xl p-6 transition-all duration-300 hover:scale-105 ${
        highlight
          ? 'border-2 border-blue-200 bg-gradient-to-br from-blue-50 to-indigo-50 shadow-lg'
          : 'border border-gray-200 dark:border-gray-700 bg-gray-100 dark:bg-gray-900 shadow-md hover:border-gray-300 hover:bg-gray-50 hover:shadow-lg'
      }`}
    >
      <div
        className={`mb-4 inline-flex h-12 w-12 items-center justify-center rounded-xl transition-transform duration-300 group-hover:scale-110 ${
          highlight
            ? 'bg-gradient-to-br from-blue-500 to-indigo-600 text-white shadow-lg'
            : 'bg-gradient-to-br from-gray-100 to-gray-200 text-gray-600 group-hover:from-blue-100 group-hover:to-indigo-100 group-hover:text-blue-600'
        }`}
      >
        {icon}
      </div>

      <div className="space-y-2">
        <h3
          className={`text-lg leading-tight font-bold ${
            highlight
              ? 'text-blue-900'
              : 'text-gray-900 dark:text-gray-200 group-hover:text-blue-900 dark:group-hover:text-gray-100'
          }`}
        >
          {title}
        </h3>
        <p
          className={`text-sm leading-relaxed ${
            highlight
              ? 'text-blue-700'
              : 'text-gray-600 dark:text-gray-400 group-hover:text-gray-700 dark:group-hover:text-gray-300'
          }`}
        >
          {description}
        </p>
      </div>

      {highlight && (
        <div className="absolute top-4 right-4">
          <div className="h-3 w-3 animate-pulse rounded-full bg-gradient-to-br from-yellow-400 to-orange-500"></div>
        </div>
      )}
    </div>
  );
};

export default function SupportPage() {
  return (
    <>
      <HeroComponent
        heading={
          <Heading>
            Get the <br />
            <span className="text-blue-600">Support You Need</span>
          </Heading>
        }
        description={
          <Paragraph>
            Our dedicated support team is here to help you succeed. Access
            comprehensive resources, expert guidance, and personalized
            assistance whenever you need it.
          </Paragraph>
        }
        heroImageSrc="/images/comprehensive support image.jpg"
        heroImageWidth={500}
        heroImageHeight={500}
        heroImageClassName="rounded-xl"
      />

      {/* Why Our Support Stands Out Section (formerly WhyChooseUs) */}
      <section
        className={`w-full bg-gray-100 dark:bg-[#0a0e17] relative overflow-hidden py-16 md:py-24`}
      >
        <div className="pointer-events-none absolute bottom-0 left-0 z-0 hidden h-[400px] w-[400px] dark:block">
          <div
            className="h-full w-full"
            style={{
              background:
                'radial-gradient(circle at 20% 80%, rgba(37,99,235,0.35) 0%, transparent 70%)',
              filter: 'blur(8px)',
            }}
          />
        </div>
        <div className="dark:hidden absolute inset-0 opacity-20">
          <div className="absolute top-10 right-20 h-40 w-40 rounded-full bg-blue-400 blur-3xl"></div>
          <div className="absolute bottom-20 left-10 h-32 w-32 rounded-full bg-purple-400 blur-2xl"></div>
          <div className="absolute top-1/3 left-1/3 h-20 w-20 rounded-full bg-pink-400 blur-xl"></div>
        </div>

        <div className="relative z-10 mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 items-center gap-12 lg:grid-cols-12 lg:gap-16">
            <div className="space-y-8 lg:col-span-7 lg:pr-8">
              <div className="space-y-6">

                <div className="space-y-3">
                  <Heading>
                    🚀 Why Our <br /> <span className="text-blue-600">Support</span> Stands Out
                  </Heading>
                </div>
              </div>

              <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                {supportFeatures.map((feature, index) => (
                  <FeatureItem
                    key={index}
                    icon={feature.icon}
                    title={feature.title}
                    description={feature.description}
                  />
                ))}
              </div>
            </div>

            <div className="flex items-center justify-center lg:col-span-5">
              <div className="group relative">
                <div className="relative transform overflow-hidden rounded-3xl border border-gray-100 bg-white p-6 shadow-2xl transition-transform duration-500 ease-out group-hover:scale-105">
                  <Image
                    src="/images/support why need us.png"
                    width={500}
                    height={500}
                    className="h-auto w-full rounded-2xl object-cover"
                    alt="Why choose us illustration"
                    priority
                  />

                  <div className="pointer-events-none absolute inset-0 rounded-2xl bg-gradient-to-tr from-transparent via-transparent to-blue-500/10"></div>
                </div>

                <div className="absolute -top-6 -right-6 animate-bounce rounded-2xl border border-gray-100 bg-white p-4 shadow-xl">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">100%</div>
                    <div className="text-xs font-medium text-gray-500">
                      Satisfaction
                    </div>
                  </div>
                </div>

                <div className="absolute -bottom-4 -left-4 animate-pulse rounded-2xl bg-gradient-to-r from-blue-500 to-[#000080] p-4 text-white shadow-xl">
                  <div className="flex items-center gap-2">
                    <svg
                      className="h-5 w-5"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                        clipRule="evenodd"
                      />
                    </svg>
                    <span className="text-sm font-semibold">Trusted</span>
                  </div>
                </div>

                <div className="absolute top-8 right-8 -z-10 h-24 w-24 rounded-full bg-blue-200 opacity-50 blur-xl"></div>
                <div className="absolute bottom-8 left-8 -z-10 h-32 w-32 rounded-full bg-purple-200 opacity-30 blur-2xl"></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section 1 - Contact Support */}
      <section className="py-16 px-4 max-w-7xl mx-auto">
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-2xl p-12 text-center border border-blue-100 dark:border-blue-800">
          <Heading>
            Need Immediate <span className="text-blue-600 dark:text-blue-400">Assistance?</span>
          </Heading>
          <div className="mt-6 max-w-3xl mx-auto">
            <Paragraph>
              Don't let technical challenges slow you down. Our expert support
              team is ready to help you resolve issues quickly and efficiently.
            </Paragraph>
          </div>
          <div className="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/contact"
              className="bg-blue-600 dark:bg-blue-500 text-white px-8 py-3 rounded-lg hover:bg-blue-700 dark:hover:bg-blue-600 transition-colors font-semibold"
            >
              Contact Support Now
            </a>
            <a
              href="/resources/faq"
              className="bg-white dark:bg-gray-800 text-blue-600 dark:text-blue-400 px-8 py-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors font-semibold border border-blue-200 dark:border-blue-700"
            >
              Browse FAQ
            </a>
          </div>
        </div>
      </section>

      {/* Comprehensive Help Resources Section (formerly WhyChooseUs) */}
      <section
        className={`w-full bg-gray-100 dark:bg-[#0a0e17] relative overflow-hidden py-16 md:py-24`}
      >
        <div className="pointer-events-none absolute bottom-0 left-0 z-0 hidden h-[400px] w-[400px] dark:block">
          <div
            className="h-full w-full"
            style={{
              background:
                'radial-gradient(circle at 20% 80%, rgba(37,99,235,0.35) 0%, transparent 70%)',
              filter: 'blur(8px)',
            }}
          />
        </div>
        <div className="dark:hidden absolute inset-0 opacity-20">
          <div className="absolute top-10 right-20 h-40 w-40 rounded-full bg-blue-400 blur-3xl"></div>
          <div className="absolute bottom-20 left-10 h-32 w-32 rounded-full bg-purple-400 blur-2xl"></div>
          <div className="absolute top-1/3 left-1/3 h-20 w-20 rounded-full bg-pink-400 blur-xl"></div>
        </div>

        <div className="relative z-10 mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 items-center gap-12 lg:grid-cols-12 lg:gap-16 lg:grid-flow-col-dense">
            {/* Image on the left */}
            <div className="flex items-center justify-center lg:col-span-5 lg:col-start-1 lg:row-start-1">
              <div className="group relative">
                <div className="relative transform overflow-hidden rounded-3xl border border-gray-100 bg-white p-6 shadow-2xl transition-transform duration-500 ease-out group-hover:scale-105">
                  <Image
                    src="/images/support you need.webp"
                    width={500}
                    height={500}
                    className="h-auto w-full rounded-2xl object-cover"
                    alt="Comprehensive Help Resources illustration"
                    priority
                  />

                  <div className="pointer-events-none absolute inset-0 rounded-2xl bg-gradient-to-tr from-transparent via-transparent to-blue-500/10"></div>
                </div>

                <div className="absolute -top-6 -right-6 animate-bounce rounded-2xl border border-gray-100 bg-white p-4 shadow-xl">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">100%</div>
                    <div className="text-xs font-medium text-gray-500">
                      Satisfaction
                    </div>
                  </div>
                </div>

                <div className="absolute -bottom-4 -left-4 animate-pulse rounded-2xl bg-gradient-to-r from-blue-500 to-[#000080] p-4 text-white shadow-xl">
                  <div className="flex items-center gap-2">
                    <svg
                      className="h-5 w-5"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                        clipRule="evenodd"
                      />
                    </svg>
                    <span className="text-sm font-semibold">Trusted</span>
                  </div>
                </div>

                <div className="absolute top-8 right-8 -z-10 h-24 w-24 rounded-full bg-blue-200 opacity-50 blur-xl"></div>
                <div className="absolute bottom-8 left-8 -z-10 h-32 w-32 rounded-full bg-purple-200 opacity-30 blur-2xl"></div>
              </div>
            </div>

            {/* Content on the right */}
            <div className="space-y-8 lg:col-span-7 lg:col-start-6 lg:pl-8">
              <div className="space-y-6">

                <div className="space-y-3">
                  <Heading>
                    📖 Comprehensive <span className="text-blue-600">Help Resources</span>
                  </Heading>
                </div>
              </div>

              <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                {helpResources.map((resource, index) => (
                  <FeatureItem
                    key={index}
                    icon={resource.icon}
                    title={resource.title}
                    description={resource.description}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section 2 - Knowledge Base */}
      <section className="py-16 px-4 max-w-7xl mx-auto">
        <div className="bg-gradient-to-r from-green-50 to-teal-50 dark:from-green-900/20 dark:to-teal-900/20 rounded-2xl p-12 text-center border border-green-100 dark:border-green-800">
          <Heading>
            Explore Our <span className="text-blue-600 dark:text-blue-400">Knowledge Base</span>
          </Heading>
          <div className="mt-6 max-w-3xl mx-auto">
            <Paragraph>
              Find answers to common questions, step-by-step guides, and best
              practices in our comprehensive knowledge base. Self-service support
              available 24/7.
            </Paragraph>
          </div>
          <div className="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/resources/faq"
              className="bg-green-600 dark:bg-green-500 text-white px-8 py-3 rounded-lg hover:bg-green-700 dark:hover:bg-green-600 transition-colors font-semibold"
            >
              Browse Knowledge Base
            </a>
            <a
              href="/contact"
              className="bg-white dark:bg-gray-800 text-green-600 dark:text-green-400 px-8 py-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors font-semibold border border-green-200 dark:border-green-700"
            >
              Request Training
            </a>
          </div>
        </div>
      </section>

      {/* Support Categories Section */}
      {/* <section className="py-16 px-4 max-w-7xl mx-auto">
        <div className="text-center mb-12">
          <Heading>
            🛠️ Support <span className="text-blue-600">Categories</span>
          </Heading>
          <Paragraph className="mt-4 max-w-3xl mx-auto">
            Browse our organized support categories to find the help you need
            quickly and efficiently.
          </Paragraph>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {[
            {
              icon: '🔧',
              title: 'Technical Issues',
              description: 'Get help with bugs, errors, and technical problems.',
              link: '/support/technical',
            },
            {
              icon: '💳',
              title: 'Billing & Payments',
              description: 'Assistance with billing, invoices, and payment issues.',
              link: '/support/billing',
            },
            {
              icon: '👥',
              title: 'Account Management',
              description: 'Help with account settings, user management, and access.',
              link: '/support/account',
            },
            {
              icon: '🔐',
              title: 'Security & Privacy',
              description: 'Security-related questions and privacy concerns.',
              link: '/support/security',
            },
            {
              icon: '🚀',
              title: 'Getting Started',
              description: 'Onboarding help and initial setup guidance.',
              link: '/support/getting-started',
            },
            {
              icon: '📈',
              title: 'Feature Requests',
              description: 'Submit ideas and requests for new features.',
              link: '/support/features',
            },
          ].map((category, index) => (
            <div
              key={index}
              className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow border border-gray-100 dark:border-gray-700"
            >
              <div className="text-3xl mb-4">{category.icon}</div>
              <h3 className="text-xl font-semibold mb-3 text-gray-900 dark:text-white">
                {category.title}
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-4">{category.description}</p>
              <a
                href={category.link}
                className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium inline-flex items-center"
              >
                Get Help →
              </a>
            </div>
          ))}
        </div>
      </section> */}

      <SupportForm />

      {/* Contact Information Section */}
      <section className="py-16 px-4 max-w-7xl mx-auto bg-gray-50 dark:bg-gray-800/50 rounded-2xl border border-gray-100 dark:border-gray-700">
        <div className="text-center mb-12">
          <Heading>
            📞 Contact <span className="text-blue-600 dark:text-blue-400">Information</span>
          </Heading>
          <Paragraph className="mt-4 max-w-3xl mx-auto">
            Multiple ways to reach our support team. Choose the method that works
            best for you.
          </Paragraph>
        </div>

        <div className="grid md:grid-cols-2 gap-8"> {/* modify the grid-cols if adding live chat */}
          <div className="text-center">
            <div className="bg-blue-100 dark:bg-blue-900/50 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 border border-blue-200 dark:border-blue-700">
              <span className="text-2xl">📧</span>
            </div>
            <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">Email Support</h3>
            <p className="text-gray-600 dark:text-gray-300 mb-3">
              Send us detailed questions and we'll respond within 24 hours.
            </p>
            <a
              href="mailto:<EMAIL>"
              className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium"
            >
              <EMAIL>
            </a>
          </div>

          {/* <div className="text-center">
            <div className="bg-blue-100 dark:bg-blue-900/50 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 border border-blue-200 dark:border-blue-700">
              <span className="text-2xl">💬</span>
            </div>
            <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">Live Chat</h3>
            <p className="text-gray-600 dark:text-gray-300 mb-3">
              Get instant help during business hours (9 AM - 6 PM IST).
            </p>
            <button className="bg-blue-600 dark:bg-blue-700 text-white px-6 py-2 rounded-lg hover:bg-blue-700 dark:hover:bg-blue-800 transition-colors">
              Start Chat
            </button>
          </div> */}

          <div className="text-center">
            <div className="bg-blue-100 dark:bg-blue-900/50 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 border border-blue-200 dark:border-blue-700">
              <span className="text-2xl">📞</span>
            </div>
            <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">Phone Support</h3>
            <p className="text-gray-600 dark:text-gray-300 mb-3">
              Call us for urgent issues and complex technical discussions.
            </p>
            <a
              href="tel:+916204351245"
              className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium"
            >
              +91 6204351245
            </a>
          </div>
        </div>
      </section>
    </>
  );
}