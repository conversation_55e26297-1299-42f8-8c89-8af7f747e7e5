import type { Metadata } from 'next';
import { Urbanist, <PERSON><PERSON><PERSON> } from 'next/font/google';
import '../../globals.css';

const urbanist = Urbanist({
  variable: '--font-urbanist',
  subsets: ['latin'],
  weight: ['400', '500', '600', '700', '800'],
  display: 'swap',
});

const nunito = Nunito({
  variable: '--font-nunito',
  subsets: ['latin'],
  weight: ['400', '500', '600', '700'],
  display: 'swap',
});

export const metadata: Metadata = {
  title:
    'Careers Portal | Sudha Software Solutions – Explore Jobs & Internships',
  description:
    'Welcome to the Careers Portal of Sudha Software Solutions. Explore latest job openings, internship opportunities, and training programs in web development, app development, and digital marketing. Build your future with one of Ranchi’s top software companies.',
  keywords:
    'Careers Portal, Sudha Software Solutions, Sudha Software Careers, Jobs at Sudha Software, IT Jobs in Ranchi, Web Development Careers, MERN Stack Jobs, Digital Marketing Roles, Software Internship, Full Stack Developer Jobs, Jharkhand IT Companies, Sudha Software Hiring, Sudha Software Job Openings, Careers at Sudha Software Solutions',
  openGraph: {
    title:
      'Careers Portal | Sudha Software Solutions – Build Your Future with Us',
    description:
      'Join Sudha Software Solutions – a leading tech company offering exciting career paths in software engineering, full-stack development, and digital growth. Explore jobs, internships, and training programs today.',
    images: [
      'https://careers.sudhasoftwaresolutions.com/assets/banner/careers-banner.jpg',
    ],
    url: 'https://careers.sudhasoftwaresolutions.com',
    type: 'website',
  },
  twitter: {
    title: 'Careers at Sudha Software Solutions – Apply for Jobs & Internships',
    description:
      'Apply for developer jobs, internships, and digital roles at Sudha Software Solutions. Join a growing team building powerful tech solutions in India.',
    images: [
      'https://careers.sudhasoftwaresolutions.com/assets/banner/careers-banner.jpg',
    ],
    card: 'summary_large_image',
    site: '@sudha_software_solutions',
    creator: '@sudha_software_solutions',
  },
};

export default function LagalLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={`${urbanist.variable} ${nunito.variable}`}>
      <body className="antialiased dark:bg-[#020012]">
        <main className="font-secondary bg-gray-50 text-gray-900 dark:bg-slate-900 dark:text-gray-50">
          {children}
        </main>
      </body>
    </html>
  );
}
