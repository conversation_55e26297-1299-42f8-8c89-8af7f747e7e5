'use client';

import React, { useState, useEffect } from 'react';
// All icons from Heroicons v2 (24/outline)
import {
  SparklesIcon, // For inspiration, impact
  LightBulbIcon, // For innovation, ideas
  BriefcaseIcon, // For professional growth
  UsersIcon, // For community, collaboration
  ChartBarIcon, // For progress, impact
  ArrowUpRightIcon, // For advancement, future
  CheckCircleIcon, // For core tenets
  AcademicCapIcon, // For education, learning
  HandRaisedIcon, // For support, empowerment
  AdjustmentsHorizontalIcon, // Used as an alternative for 'TargetIcon' (for focus, goals)
  MegaphoneIcon, // For communication
  EnvelopeIcon, // For contact
  PhoneIcon, // For contact
  MapPinIcon, // For location/contact
} from '@heroicons/react/24/outline';
import Beams from '@/components/react-bits/Backgrounds/Beams/Beams';

// --- SECTION CONTAINER COMPONENT ---
interface SectionContainerProps {
  children: React.ReactNode;
  className?: string;
  id?: string;
}

const SectionContainer: React.FC<SectionContainerProps> = ({ children, className, id }) => {
  return (
    <section
      id={id}
      className={`py-16 md:py-24 px-4 sm:px-6 lg:px-8 bg-blue-700/10 transition-colors duration-300 ${className || ''}`}
    >
      <div className="max-w-6xl mx-auto">
        {children}
      </div>
    </section>
  );
};

// --- HEADING COMPONENT ---
interface HeadingProps {
  children: React.ReactNode;
  highlightText?: string;
  level: 'h1' | 'h2' | 'h3' | 'h4';
  className?: string;
}

const Heading: React.FC<HeadingProps> = ({ children, highlightText, level, className }) => {
  const renderContent = () => {
    if (highlightText) {
      const parts = children?.toString().split(new RegExp(`(${highlightText})`, 'gi'));
      return (
        <>
          {parts?.map((part, index) =>
            part.toLowerCase() === highlightText.toLowerCase() ? (
              <span key={index} className="text-indigo-600">
                {part}
              </span>
            ) : (
              part
            )
          )}
        </>
      );
    }
    return children;
  };

  const baseClasses = 'font-extrabold mb-4';
  const levelClasses = {
    h1: 'text-4xl md:text-5xl lg:text-6xl text-slate-900 dark:text-gray-50',
    h2: 'text-3xl md:text-4xl lg:text-5xl text-slate-900 dark:text-gray-50',
    h3: 'text-2xl md:text-3xl lg:text-4xl text-slate-900 dark:text-gray-50',
    h4: 'text-xl md:text-2xl lg:text-3xl text-slate-900 dark:text-gray-50',
  };

  const Htag = level;

  return (
    <Htag className={`${baseClasses} ${levelClasses[level]} ${className || ''}`}>
      {renderContent()}
    </Htag>
  );
};

// --- PARAGRAPH COMPONENT ---
interface ParagraphProps {
  children: React.ReactNode;
  className?: string;
}

const Paragraph: React.FC<ParagraphProps> = ({ children, className }) => {
  return (
    <p className={`text-slate-700 dark:text-gray-300 leading-relaxed ${className || ''}`}>
      {children}
    </p>
  );
};

// --- PRIMARY BUTTON COMPONENT ---
interface PrimaryButtonProps {
  children: React.ReactNode;
  href: string;
  className?: string;
  isExternal?: boolean;
}

const PrimaryButton: React.FC<PrimaryButtonProps> = ({ children, href, className, isExternal = false }) => {
  const commonClasses = `inline-flex items-center justify-center gap-2 px-8 py-3 rounded-lg text-base font-medium transition-colors duration-200 cursor-pointer border-[1px] border-indigo-400
                       dark:text-white text-indigo-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500
                       dark:text-white dark:focus:ring-indigo-600 ${className || ''}`;
  return isExternal ? (
    <a href={href} target="_blank" rel="noopener noreferrer" className={commonClasses}>
      {children}
    </a>
  ) : (
    <a href={href} className={commonClasses}>
      {children}
    </a>
  );
};

// --- PRINCIPLE ITEM CARD COMPONENT (similar to ValueItemCard) ---
interface PrincipleItemCardProps {
  title: string;
  description: string;
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
}

const PrincipleItemCard: React.FC<PrincipleItemCardProps> = ({ title, description, icon: Icon }) => {
  return (
    <div
      className="group relative overflow-hidden rounded-2xl border border-gray-200 bg-blue-50 p-6 shadow-md transition-all duration-300 hover:-translate-y-1 hover:shadow-lg
                  dark:border-slate-700 dark:bg-slate-800"
    >
      {/* Subtle overlay on hover */}
      <div
        className="absolute inset-0 bg-indigo-600 opacity-0 transition-opacity duration-300 group-hover:opacity-[0.03]"
      ></div>

      <div className="relative z-10 flex flex-col h-full items-center text-center">
        {/* Icon with solid primary color background */}
        <div
          className="inline-flex rounded-xl border-[1px] border-indigo-400 w-12 p-3 mb-4 text-gray-800 dark:text-white transition-transform duration-300 group-hover:scale-105"
        >
          <Icon className="h-6 w-6" />
        </div>

        <h3 className="mb-2 text-lg font-semibold text-slate-900 dark:text-gray-50">
          {title}
        </h3>

        <p className="mb-4 text-sm leading-relaxed text-slate-700 dark:text-gray-300 flex-grow">
          {description}
        </p>
      </div>
    </div>
  );
};

// --- FEATURE CHECKLIST ITEM COMPONENT (Reused) ---
interface FeatureChecklistItemProps {
  text: string;
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
}

const FeatureChecklistItem: React.FC<FeatureChecklistItemProps> = ({ text, icon: Icon }) => {
  return (
    <div className="flex items-center gap-3">
      <Icon className="h-6 w-6 text-indigo-600 dark:text-indigo-400" />
      <span className="font-medium text-slate-700 dark:text-gray-300 text-lg">
        {text}
      </span>
    </div>
  );
};

// --- CONTACT INFO CARD COMPONENT (Reused) ---
interface ContactInfoCardProps {
  title: string;
  description: string;
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  contactDetail: string;
  linkHref?: string;
  linkText?: string;
}

const ContactInfoCard: React.FC<ContactInfoCardProps> = ({ title, description, icon: Icon, contactDetail, linkHref, linkText }) => {
  return (
    <div className="flex items-start space-x-4 p-6 rounded-xl bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 shadow-sm">
      <div className="flex-shrink-0 p-3 rounded-full bg-indigo-100 text-indigo-600 dark:bg-indigo-900/50 dark:text-indigo-400">
        <Icon className="h-6 w-6" />
      </div>
      <div>
        <h4 className="text-lg font-semibold text-slate-900 dark:text-gray-50">{title}</h4>
        <Paragraph className="text-sm !mb-1">{description}</Paragraph>
        {linkHref ? (
          <a href={linkHref} className="text-indigo-600 hover:underline dark:text-indigo-500">
            {linkText || contactDetail}
          </a>
        ) : (
          <p className="text-slate-700 dark:text-gray-300">{contactDetail}</p>
        )}
      </div>
    </div>
  );
};


// --- MAIN OUR MISSION PAGE COMPONENT ---
const OurMissionPage: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const keyPrinciples = [
    {
      icon: HandRaisedIcon,
      title: 'Empowerment',
      description: 'Providing individuals with the knowledge and tools to take control of their career paths.',
    },
    {
      icon: UsersIcon,
      title: 'Accessibility',
      description: 'Ensuring our resources and support are available to everyone, everywhere.',
    },
    {
      icon: LightBulbIcon,
      title: 'Innovation',
      description: 'Constantly evolving our methods and content to meet the dynamic needs of the job market.',
    },
    {
      icon: AcademicCapIcon,
      title: 'Education',
      description: 'Committing to continuous learning and sharing expertise to foster growth.',
    },
    {
      icon: BriefcaseIcon,
      title: 'Practicality',
      description: 'Delivering actionable advice and strategies that yield real-world results.',
    },
    {
      icon: ChartBarIcon,
      title: 'Impact',
      description: 'Measuring our success by the tangible career advancements of our users.',
    },
  ];

  const missionCommitments = [
    { icon: CheckCircleIcon, text: 'Provide Up-to-Date & Relevant Content' },
    { icon: CheckCircleIcon, text: 'Offer Personalized Guidance & Support' },
    { icon: CheckCircleIcon, text: 'Foster a Thriving Community of Professionals' },
    { icon: CheckCircleIcon, text: 'Innovate with Cutting-Edge Career Tools' },
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-slate-900 transition-colors duration-300">

      {/* Hero Section */}
      <section className="relative overflow-hidden py-24 md:py-36 bg-slate-200 dark:bg-slate-800 dark:text-white">
        
           <div className="absolute inset-0"> 
          <Beams
            beamWidth={1}
            beamHeight={8}
            beamNumber={5}
            lightColor="#ffffff"
            speed={3}
            noiseIntensity={1.45}
            scale={0.22}
            rotation={39}
          />
        </div>
        
        <div className="relative container mx-auto px-4 text-center z-10">
          <div className={`transition-all duration-1000 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
            <Heading level="h1" highlightText="" className="max-w-4xl mx-auto leading-tight  !text-white !mb-6">
              Our Mission: To Help You Achieve Your Full Career Potential
            </Heading>
            <Paragraph className="max-w-3xl mx-auto mt-6 mb-12 text-xl  dark:!text-white/50 text-white/50">
              We are dedicated to equipping every individual with the resources, knowledge, and confidence needed to navigate their career journey successfully.
            </Paragraph>
            <PrimaryButton href="#our-principles" className="rounded-full px-8 py-4 text-slate-50 bg-blue-800">
              Understand Our Approach
            </PrimaryButton>
          </div>
        </div>
      </section>

      {/* Mission Statement Section */}
      <SectionContainer className="dark:bg-slate-900 bg-slate-100">
        <div className="grid items-center gap-16 lg:grid-cols-2">
          <div className={`relative transition-all delay-300 duration-1000 ${isVisible ? 'translate-x-0 opacity-100' : '-translate-x-10 opacity-0'}`}>
            <img
              src="/images/our-mission.png" // Placeholder image
              alt="Our Mission in Action"
              className="h-auto w-full rounded-2xl shadow-2xl"
            />
            <div className="absolute -top-4 -right-4 animate-bounce rounded-full bg-indigo-600 p-3 text-white shadow-lg">
              <AdjustmentsHorizontalIcon className="h-6 w-6" /> {/* Replaced TargetIcon */}
            </div>
            <div className="absolute -bottom-4 -left-4 animate-pulse rounded-full bg-indigo-600 p-3 text-white shadow-lg">
              <SparklesIcon className="h-6 w-6" /> {/* Replaced PuzzlePieceIcon */}
            </div>
          </div>
          <div>
            <Heading level="h2" className="mb-6">
              Driving Career <span className="text-indigo-600">Success Through Action</span>
            </Heading>
            <Paragraph className="mb-8 text-xl leading-relaxed">
              Our mission is to serve as the ultimate catalyst for career advancement. We achieve this by meticulously curating practical resources, offering expert guidance, and fostering a supportive community that empowers individuals to overcome challenges and seize opportunities in their professional lives.
            </Paragraph>
            <div className="mb-8 grid grid-cols-1 gap-4">
              <FeatureChecklistItem text="Actionable resources for immediate application" icon={ArrowUpRightIcon} />
              <FeatureChecklistItem text="Dedicated support from experienced professionals" icon={HandRaisedIcon} />
              <FeatureChecklistItem text="A community built for shared growth and learning" icon={UsersIcon} />
            </div>
          </div>
        </div>
      </SectionContainer>

      {/* Key Principles Section */}
      <SectionContainer id="our-principles" className="bg-slate-200 dark:bg-slate-800">
        <Heading level="h2" className="text-center mb-6">
          Guiding <span className="text-indigo-600">Principles of Our Mission</span>
        </Heading>
        <Paragraph className="max-w-3xl mx-auto text-center mb-16 text-lg">
          These principles are the foundation of our work, ensuring we consistently deliver on our promise to support your professional development.
        </Paragraph>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {keyPrinciples.map((principle, index) => (
            <PrincipleItemCard
              key={index}
              title={principle.title}
              description={principle.description}
              icon={principle.icon}
            />
          ))}
        </div>
      </SectionContainer>

      {/* Impact and Commitment Section */}
      <SectionContainer className="bg-white dark:bg-slate-900">
        <div className="text-center mb-16">
          <Heading level="h2" className="mb-6">
            Our Commitment to Your <span className="text-indigo-600">Lasting Success</span>
          </Heading>
          <Paragraph className="max-w-3xl mx-auto text-center mb-16 text-lg">
            Our mission is a long-term commitment. We continuously strive to enhance our offerings to ensure every user achieves significant, lasting career improvements.
          </Paragraph>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 max-w-4xl mx-auto mb-12">
          {missionCommitments.map((commitment, index) => (
            <FeatureChecklistItem key={index} text={commitment.text} icon={commitment.icon} />
          ))}
        </div>
        <div className="text-center">
          <PrimaryButton href="/what-we-do">
            See How We Deliver Our Mission
          </PrimaryButton>
        </div>
      </SectionContainer>

      {/* Call to Action / Connect Section */}
      <SectionContainer id="connect" className="bg-slate-100 dark:bg-gray-900">
        <div className="text-center mb-16">
          <Heading level="h2" className="mb-6">
            Join Our Mission: <span className="text-indigo-600">Start Your Journey Today</span>
          </Heading>
          <Paragraph className="max-w-3xl mx-auto text-center mb-16 text-lg">
            Your career aspirations are our mission. Connect with us to discover how we can help you achieve your professional goals.
          </Paragraph>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          <ContactInfoCard
            title="Get Started Now"
            description="Take the first step towards a better career. Our team is ready to assist."
            icon={SparklesIcon}
            contactDetail="Book an Intro Call"
            linkHref="/contact"
            linkText="Schedule a Meeting"
          />
          <ContactInfoCard
            title="General Inquiries"
            description="For any questions about our mission or services, feel free to reach out."
            icon={EnvelopeIcon}
            contactDetail="<EMAIL>"
            linkHref="mailto:<EMAIL>"
            linkText="Email Us"
          />
          <ContactInfoCard
            title="Visit Our Community"
            description="Engage with a network of professionals and mentors."
            icon={UsersIcon}
            contactDetail="Join Our Forum"
            linkHref="/community"
            linkText="Connect Online"
          />
        </div>
      </SectionContainer>
    </div>
  );
};

export default OurMissionPage;
