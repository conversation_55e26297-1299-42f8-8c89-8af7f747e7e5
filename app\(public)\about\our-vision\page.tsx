'use client';

import React, { useState, useEffect } from 'react';
// All icons from Heroicons v2 (24/outline)
import {
  SparklesIcon, // For vision, inspiration
  EyeIcon, // For vision, foresight
  RocketLaunchIcon, // For ambition, growth
  GlobeAltIcon, // For global impact, reach
  HandRaisedIcon, // For integrity, support
  UsersIcon, // For community, collaboration
  ChartBarIcon, // For progress, impact
  ArrowUpRightIcon, // For advancement, future
  LightBulbIcon, // For innovation, ideas
  CheckCircleIcon, // For core values
  BriefcaseIcon, // For professional growth
  AcademicCapIcon, // For education, learning
  MegaphoneIcon, // For communication
  EnvelopeIcon, // For contact
  PhoneIcon, // For contact
  MapPinIcon, // For location/contact
} from '@heroicons/react/24/outline';
import Beams from '@/components/react-bits/Backgrounds/Beams/Beams';

// --- SECTION CONTAINER COMPONENT ---
interface SectionContainerProps {
  children: React.ReactNode;
  className?: string;
  id?: string;
}

const SectionContainer: React.FC<SectionContainerProps> = ({ children, className, id }) => {
  return (
    <section
      id={id}
      className={`py-16 md:py-24 px-4 sm:px-6 lg:px-8 bg-blue-700/10 transition-colors duration-300 ${className || ''}`}
    >
      <div className="max-w-6xl mx-auto">
        {children}
      </div>
    </section>
  );
};

// --- HEADING COMPONENT ---
interface HeadingProps {
  children: React.ReactNode;
  highlightText?: string;
  level: 'h1' | 'h2' | 'h3' | 'h4';
  className?: string;
}

const Heading: React.FC<HeadingProps> = ({ children, highlightText, level, className }) => {
  const renderContent = () => {
    if (highlightText) {
      const parts = children?.toString().split(new RegExp(`(${highlightText})`, 'gi'));
      return (
        <>
          {parts?.map((part, index) =>
            part.toLowerCase() === highlightText.toLowerCase() ? (
              <span key={index} className="text-indigo-600">
                {part}
              </span>
            ) : (
              part
            )
          )}
        </>
      );
    }
    return children;
  };

  const baseClasses = 'font-extrabold mb-4';
  const levelClasses = {
    h1: 'text-4xl md:text-5xl lg:text-6xl text-slate-900 dark:text-gray-50',
    h2: 'text-3xl md:text-4xl lg:text-5xl text-slate-900 dark:text-gray-50',
    h3: 'text-2xl md:text-3xl lg:text-4xl text-slate-900 dark:text-gray-50',
    h4: 'text-xl md:text-2xl lg:text-3xl text-slate-900 dark:text-gray-50',
  };

  const Htag = level;

  return (
    <Htag className={`${baseClasses} ${levelClasses[level]} ${className || ''}`}>
      {renderContent()}
    </Htag>
  );
};

// --- PARAGRAPH COMPONENT ---
interface ParagraphProps {
  children: React.ReactNode;
  className?: string;
}

const Paragraph: React.FC<ParagraphProps> = ({ children, className }) => {
  return (
    <p className={`text-slate-700 dark:text-gray-300 leading-relaxed ${className || ''}`}>
      {children}
    </p>
  );
};

// --- PRIMARY BUTTON COMPONENT ---
interface PrimaryButtonProps {
  children: React.ReactNode;
  href: string;
  className?: string;
  isExternal?: boolean;
}

const PrimaryButton: React.FC<PrimaryButtonProps> = ({ children, href, className, isExternal = false }) => {
  const commonClasses = `inline-flex items-center justify-center gap-2 px-8 py-3 rounded-lg text-base font-medium transition-colors duration-200 cursor-pointer border-[1px] border-indigo-400
                       dark:text-white text-indigo-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500
                       dark:text-white dark:focus:ring-indigo-600 ${className || ''}`;
  return isExternal ? (
    <a href={href} target="_blank" rel="noopener noreferrer" className={commonClasses}>
      {children}
    </a>
  ) : (
    <a href={href} className={commonClasses}>
      {children}
    </a>
  );
};

// --- VALUE ITEM CARD COMPONENT (similar to ServiceItemCard) ---
interface ValueItemCardProps {
  title: string;
  description: string;
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
}

const ValueItemCard: React.FC<ValueItemCardProps> = ({ title, description, icon: Icon }) => {
  return (
    <div
      className="group relative overflow-hidden rounded-2xl border border-gray-200 bg-blue-50 p-6 shadow-md transition-all duration-300 hover:-translate-y-1 hover:shadow-lg
                  dark:border-slate-700 dark:bg-slate-800"
    >
      {/* Subtle overlay on hover */}
      <div
        className="absolute inset-0 bg-indigo-600 opacity-0 transition-opacity duration-300 group-hover:opacity-[0.03]"
      ></div>

      <div className="relative z-10 flex flex-col h-full items-center text-center">
        {/* Icon with solid primary color background */}
        <div
          className="inline-flex rounded-xl border-[1px] border-indigo-400 w-12 p-3 mb-4 text-gray-800 dark:text-white transition-transform duration-300 group-hover:scale-105"
        >
          <Icon className="h-6 w-6" />
        </div>

        <h3 className="mb-2 text-lg font-semibold text-slate-900 dark:text-gray-50">
          {title}
        </h3>

        <p className="mb-4 text-sm leading-relaxed text-slate-700 dark:text-gray-300 flex-grow">
          {description}
        </p>
      </div>
    </div>
  );
};

// --- FEATURE CHECKLIST ITEM COMPONENT (Reused) ---
interface FeatureChecklistItemProps {
  text: string;
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
}

const FeatureChecklistItem: React.FC<FeatureChecklistItemProps> = ({ text, icon: Icon }) => {
  return (
    <div className="flex items-center gap-3">
      <Icon className="h-6 w-6 text-indigo-600 dark:text-indigo-400" />
      <span className="font-medium text-slate-700 dark:text-gray-300 text-lg">
        {text}
      </span>
    </div>
  );
};

// --- CONTACT INFO CARD COMPONENT (Reused) ---
interface ContactInfoCardProps {
  title: string;
  description: string;
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  contactDetail: string;
  linkHref?: string;
  linkText?: string;
}

const ContactInfoCard: React.FC<ContactInfoCardProps> = ({ title, description, icon: Icon, contactDetail, linkHref, linkText }) => {
  return (
    <div className="flex items-start space-x-4 p-6 rounded-xl bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 shadow-sm">
      <div className="flex-shrink-0 p-3 rounded-full bg-indigo-100 text-indigo-600 dark:bg-indigo-900/50 dark:text-indigo-400">
        <Icon className="h-6 w-6" />
      </div>
      <div>
        <h4 className="text-lg font-semibold text-slate-900 dark:text-gray-50">{title}</h4>
        <Paragraph className="text-sm !mb-1">{description}</Paragraph>
        {linkHref ? (
          <a href={linkHref} className="text-indigo-600 hover:underline dark:text-indigo-500">
            {linkText || contactDetail}
          </a>
        ) : (
          <p className="text-slate-700 dark:text-gray-300">{contactDetail}</p>
        )}
      </div>
    </div>
  );
};


// --- MAIN OUR VISION PAGE COMPONENT ---
const OurVisionPage: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const coreValues = [
    {
      icon: HandRaisedIcon,
      title: 'Integrity',
      description: 'Upholding honesty and strong moral principles in all our interactions and services.',
    },
    {
      icon: UsersIcon,
      title: 'Collaboration',
      description: 'Fostering a supportive community where individuals and ideas thrive together.',
    },
    {
      icon: LightBulbIcon,
      title: 'Innovation',
      description: 'Continuously seeking new and better ways to empower career development.',
    },
    {
      icon: AcademicCapIcon,
      title: 'Growth',
      description: 'Dedicated to personal and professional development for every individual.',
    },
    {
      icon: BriefcaseIcon,
      title: 'Excellence',
      description: 'Striving for the highest standards in our guidance and resources.',
    },
    {
      icon: GlobeAltIcon,
      title: 'Inclusivity',
      description: 'Building a platform accessible and beneficial to all, regardless of background.',
    },
  ];

  const futureImpacts = [
    { icon: CheckCircleIcon, text: 'Empowering Millions of Careers Globally' },
    { icon: CheckCircleIcon, text: 'Innovating Career Development Methodologies' },
    { icon: CheckCircleIcon, text: 'Fostering a Diverse & Inclusive Workforce' },
    { icon: CheckCircleIcon, text: 'Becoming the Foremost Career Resource Hub' },
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-slate-900 transition-colors duration-300">

      {/* Hero Section */}
      <section className="relative overflow-hidden py-24 md:py-36 bg-slate-200 dark:bg-slate-800 dark:text-white">
       
       <div className="absolute inset-0"> 
          <Beams
            beamWidth={1}
            beamHeight={8}
            beamNumber={5}
            lightColor="#ffffff"
            speed={3}
            noiseIntensity={1.45}
            scale={0.22}
            rotation={39}
          />
        </div>
       
        <div className="relative container mx-auto px-4 text-center z-10">
          <div className={`transition-all duration-1000 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
            <Heading level="h1" highlightText="" className="max-w-4xl mx-auto leading-tight !text-white !mb-6">
              Our Vision: To Inspire Greatness in Every Career Journey
            </Heading>
            <Paragraph className="max-w-3xl mx-auto mt-6 mb-12 text-xl  dark:!text-white/50 text-white/50">
              We envision a world where every individual has the clarity, confidence, and tools to pursue a fulfilling and impactful career.
            </Paragraph>
            <PrimaryButton href="#our-values" className="rounded-full px-8 py-4 text-slate-50 bg-blue-800">
              Discover Our Values
            </PrimaryButton>
          </div>
        </div>
      </section>

      {/* Vision Statement Section */}
      <SectionContainer className="dark:bg-slate-900 bg-slate-100">
        <div className="grid items-center gap-16 lg:grid-cols-2">
          <div className={`relative transition-all delay-300 duration-1000 ${isVisible ? 'translate-x-0 opacity-100' : '-translate-x-10 opacity-0'}`}>
            <img
              src="/images/our-vision.jpg" // Placeholder image
              alt="Our Vision Statement"
              className="h-auto w-full rounded-2xl shadow-2xl"
            />
            <div className="absolute -top-4 -right-4 animate-bounce rounded-full bg-indigo-600 p-3 text-white shadow-lg">
              <EyeIcon className="h-6 w-6" />
            </div>
            <div className="absolute -bottom-4 -left-4 animate-pulse rounded-full bg-indigo-600 p-3 text-white shadow-lg">
              <SparklesIcon className="h-6 w-6" />
            </div>
          </div>
          <div>
            <Heading level="h2" className="mb-6">
              Crafting a <span className="text-indigo-600">Brighter Future</span> for All
            </Heading>
            <Paragraph className="mb-8 text-xl leading-relaxed">
              Our vision is to revolutionize career development by providing an intuitive, accessible, and insightful platform. We aim to be the indispensable partner for individuals seeking to define, advance, and thrive in their professional lives, fostering a global community of empowered professionals.
            </Paragraph>
            <div className="mb-8 grid grid-cols-1 gap-4">
              <FeatureChecklistItem text="Guidance that transcends boundaries" icon={GlobeAltIcon} />
              <FeatureChecklistItem text="Tools that simplify career navigation" icon={RocketLaunchIcon} />
              <FeatureChecklistItem text="Insights that illuminate future opportunities" icon={ChartBarIcon} />
            </div>
          </div>
        </div>
      </SectionContainer>

      {/* Core Values Section */}
      <SectionContainer id="our-values" className="bg-slate-200 dark:bg-slate-800">
        <Heading level="h2" className="text-center mb-6">
          The Pillars of Our <span className="text-indigo-600">Foundation</span>
        </Heading>
        <Paragraph className="max-w-3xl mx-auto text-center mb-16 text-lg">
          Our core values are the guiding principles that shape our decisions, drive our actions, and define our commitment to you. They are the essence of who we are.
        </Paragraph>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {coreValues.map((value, index) => (
            <ValueItemCard
              key={index}
              title={value.title}
              description={value.description}
              icon={value.icon}
            />
          ))}
        </div>
      </SectionContainer>

      {/* Future Impact Section */}
      <SectionContainer className="bg-white dark:bg-slate-900">
        <div className="text-center mb-16">
          <Heading level="h2" className="mb-6">
            Driving <span className="text-indigo-600">Lasting Impact</span>
          </Heading>
          <Paragraph className="max-w-3xl mx-auto text-center mb-16 text-lg">
            Our vision is not just about today; it's about shaping a future where professional success is within reach for everyone. We're committed to making a tangible difference.
          </Paragraph>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 max-w-4xl mx-auto mb-12">
          {futureImpacts.map((impact, index) => (
            <FeatureChecklistItem key={index} text={impact.text} icon={impact.icon} />
          ))}
        </div>
        <div className="text-center">
          <PrimaryButton href="/about">
            Learn More About Our Journey
          </PrimaryButton>
        </div>
      </SectionContainer>

      {/* Call to Action / Contact Section */}
      <SectionContainer id="connect" className="bg-slate-100 dark:bg-gray-900">
        <div className="text-center mb-16">
          <Heading level="h2" className="mb-6">
            Join Us in <span className="text-indigo-600">Building the Future</span>
          </Heading>
          <Paragraph className="max-w-3xl mx-auto text-center mb-16 text-lg">
            Our vision is ambitious, and we invite you to be a part of it. Whether you're seeking guidance or wish to collaborate, connect with us.
          </Paragraph>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          <ContactInfoCard
            title="Explore Partnership Opportunities"
            description="Let's discuss how we can work together to achieve common goals."
            icon={MegaphoneIcon}
            contactDetail="Partnerships Department"
            linkHref="/partnerships"
            linkText="Partner With Us"
          />
          <ContactInfoCard
            title="Reach Out Directly"
            description="Have questions about our vision or services? Contact our team."
            icon={EnvelopeIcon}
            contactDetail="<EMAIL>"
            linkHref="mailto:<EMAIL>"
            linkText="Email Us"
          />
          <ContactInfoCard
            title="Follow Our Progress"
            description="Stay updated on our journey and impact through our social channels."
            icon={ChartBarIcon}
            contactDetail="On Social Media"
            linkHref="/blog"
            linkText="Read Our Blog"
          />
        </div>
      </SectionContainer>
    </div>
  );
};

export default OurVisionPage;
