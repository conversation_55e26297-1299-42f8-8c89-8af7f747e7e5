// "use client";

import React from 'react';
import { Metadata } from 'next';
import Image from 'next/image';

export const metadata: Metadata = {
  title: 'What We Do - Innovative IT Solutions at Sudha Software Solutions',
  description:
    'Discover how Sudha Software Solutions drives digital transformation with cutting-edge IT services including web & mobile app development, SaaS solutions, digital marketing, and cloud services. Empower your business with our innovative technology solutions.',
  keywords:
    'IT Solutions, Web Development, Mobile App Development, SaaS Solutions, Digital Marketing, Cloud Services, Sudha Software Solutions, Digital Transformation, Innovative Technology',
  openGraph: {
    title: 'What We Do - Innovative IT Solutions at Sudha Software Solutions',
    description:
      'Explore our expertise in web & mobile app development, SaaS, digital marketing, and cloud services. Sudha Software Solutions is your trusted partner in digital transformation and business growth.',
    images: [
      // 'https://careers.sudhasoftwaresolutions.com/assets/banner/careers-banner.jpg',
    ],
    url: 'https://careers.sudhasoftwaresolutions.com/what-we-do',
    type: 'website',
  },
  twitter: {
    title: 'What We Do - Innovative IT Solutions at Sudha Software Solutions',
    description:
      'Learn how we empower businesses with innovative IT solutions in web & mobile app development, SaaS, digital marketing, and cloud services. Join us in driving digital transformation!',
    images: [
      // 'https://careers.sudhasoftwaresolutions.com/assets/banner/careers-banner.jpg',
    ],
    card: 'summary_large_image',
    site: '@sudha_software_solutions',
    creator: '@sudha_software_solutions',
  },
};

const approach = [
  {
    icon: '🔍',
    title: 'Customized Solutions',
    description:
      'We understand that every business is unique, which is why we tailor our services to meet your specific needs.',
  },
  {
    icon: '🤝',
    title: 'Collaborative Partnership',
    description:
      'We work closely with you throughout the development process, ensuring transparency, innovation, and excellence at every step.',
  },
  {
    icon: '🚀',
    title: 'Innovation at Heart',
    description:
      'Our team of experts continuously explores emerging technologies to keep your business ahead of the curve.',
  },
];
const expertise = [
  {
    icon: '🚀',
    title: 'Web & Mobile App Development',
    description:
      'We create responsive, user-friendly web and mobile applications that drive business growth and enhance user engagement.',
    descriptionPadding: 'pl-6',
  },
  {
    icon: '🚀',
    title: 'SaaS Solutions',
    description:
      'Our scalable Software-as-a-Service products empower businesses with robust, cloud-based tools designed for efficiency and performance.',
    descriptionPadding: 'pl-6',
  },
  {
    icon: '🚀',
    title: 'Digital Marketing',
    description:
      'From SEO to social media campaigns, our digital marketing strategies ensure your brand reaches its full potential in the digital space.',
    descriptionPadding: 'pl-9',
  },
  {
    icon: '🚀',
    title: 'Cloud Services',
    description:
      'We offer comprehensive cloud solutions that streamline operations, enhance data security, and boost productivity.',
    descriptionPadding: 'pl-6',
  },
];

const whyChoose = [
  {
    icon: '✅',
    title: 'Proven Expertise',
    description:
      'With a track record of delivering successful projects, we have earned the trust of our clients and the reputation of being industry leaders.',
  },
  {
    icon: '✅',
    title: 'Customer-Centric',
    description:
      'Your success is our priority. We provide dedicated support and maintain open lines of communication to ensure your vision is realized.',
  },
  {
    icon: '✅',
    title: 'Future-Ready',
    description:
      "Our solutions are designed not only for today's challenges but also to adapt to tomorrow's opportunities.",
  },
];
const WhatWeDo = () => {
  return (
    <main className="font-urbanist min-h-screen bg-gradient-to-br from-slate-50 to-gray-100 dark:from-gray-900 dark:to-slate-900">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 md:px-8 lg:px-16 xl:px-40">
          <div className="grid items-center gap-12 lg:grid-cols-2">
            <div className="space-y-8">
              <div className="space-y-6">
                <h1 className="font-nunito text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl lg:text-6xl dark:text-white">
                  Welcome to Our <br />
                  <span className="bg-gradient-to-r from-blue-600 to-purple-800 bg-clip-text text-transparent">
                    Sudha Software Solutions
                  </span>{' '}
                  <span className="text-2xl text-gray-600 lg:text-3xl dark:text-gray-300">
                    Private Limited
                  </span>
                </h1>
                <p className="text-lg leading-relaxed text-gray-600 dark:text-gray-300">
                  Sudha Software Solutions Private Limited is a leading IT and{' '}
                  <span className="font-semibold text-gray-800 dark:text-gray-200">
                    software development company
                  </span>{' '}
                  specializing in{' '}
                  <span className="font-semibold text-gray-800 dark:text-gray-200">
                    web & mobile app development, SaaS solutions, digital
                    marketing
                  </span>{' '}
                  , and{' '}
                  <span className="font-semibold text-gray-800 dark:text-gray-200">
                    cloud services
                  </span>
                  . We help businesses scale through digital transformation and
                  cutting-edge technology.
                </p>
              </div>
            </div>
            <div className="relative">
              <div className="absolute -inset-4 rounded-2xl bg-gradient-to-r from-red-400 to-pink-400 opacity-20 blur-lg"></div>
              <Image
                src="/images/who-we-are.jpg"
                width={500}
                height={500}
                className="relative rounded-2xl shadow-xl"
                alt="We are a team"
                priority
              />
            </div>
          </div>
        </div>
      </section>

      {/* Our Expertise */}
      <section
        id="expertise"
        className="bg-white py-20 lg:py-32 dark:bg-gray-800"
      >
        <div className="mx-auto max-w-7xl px-4 sm:px-6 md:px-8 lg:px-16 xl:px-40">
          <div className="grid items-center gap-12 lg:grid-cols-2">
            <div className="space-y-8">
              <div className="flex items-center gap-4">
                <div className="rounded-full bg-blue-100 p-3 dark:bg-blue-900">
                  <span className="text-2xl">💻</span>
                </div>
                <h2 className="font-nunito text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl dark:text-white">
                  Our{' '}
                  <span className="bg-gradient-to-r from-blue-600 to-purple-800 bg-clip-text text-transparent">
                    Expertise
                  </span>
                </h2>
              </div>
              <div className="space-y-6">
                {expertise.map((feature, index) => (
                  <div
                    key={index}
                    className="rounded-lg bg-gray-50 p-6 transition-all duration-300 hover:bg-gray-100 hover:shadow-md dark:bg-gray-700 dark:hover:bg-gray-600"
                  >
                    <div className="flex items-start gap-4">
                      <span className="flex-shrink-0 text-2xl">
                        {feature.icon}
                      </span>
                      <div>
                        <h3 className="mb-2 font-semibold text-gray-900 dark:text-white">
                          {feature.title}
                        </h3>
                        <p className="leading-relaxed text-gray-600 dark:text-gray-300">
                          {feature.description}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <div className="relative">
              <div className="absolute -inset-4 rounded-2xl bg-gradient-to-r from-blue-400 to-indigo-400 opacity-20 blur-lg"></div>
              <Image
                src="https://images.unsplash.com/photo-1552664730-d307ca884978?w=600&h=600&fit=crop"
                width={500}
                height={500}
                className="relative rounded-2xl shadow-xl"
                alt="Team collaboration"
              />
            </div>
          </div>
        </div>
      </section>
      {/* Our Mission */}
      <section className="bg-gradient-to-br from-blue-50 to-indigo-50 py-20 lg:py-32 dark:from-blue-900/20 dark:to-indigo-900/20">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 md:px-8 lg:px-16 xl:px-40">
          <div className="grid items-center gap-12 lg:grid-cols-2">
            <div className="relative order-2 lg:order-1">
              <div className="absolute -inset-4 rounded-2xl bg-gradient-to-r from-blue-400 to-purple-400 opacity-20 blur-lg"></div>
              <Image
                src="/images/scroll-image.jpg"
                width={500}
                height={500}
                className="relative rounded-2xl shadow-xl"
                alt="Our mission"
              />
            </div>
            <div className="order-1 space-y-8 lg:order-2">
              <div className="flex items-center gap-4">
                <div className="rounded-full bg-blue-100 p-3 dark:bg-blue-900">
                  <span className="text-2xl">🎯</span>
                </div>
                <h2 className="font-nunito text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl dark:text-white">
                  Our{' '}
                  <span className="bg-gradient-to-r from-blue-600 to-purple-800 bg-clip-text text-transparent">
                    Mission
                  </span>
                </h2>
              </div>
              <div className="space-y-6">
                <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
                  <p className="text-lg leading-relaxed text-gray-700 dark:text-gray-300">
                    💡 We are committed to helping businesses scale by embracing
                    digital transformation and leveraging the latest technology.
                    Our goal is to empower your company with innovative
                    solutions that drive success and create lasting impact.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Our Approach */}
      <section className="w-full py-20 lg:py-32">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 md:px-8 lg:px-16 xl:px-40">
          <div className="grid items-center gap-12 lg:grid-cols-2">
            <div className="order-1 space-y-8 lg:order-2">
              <div className="flex items-center gap-4">
                <div className="rounded-full bg-orange-100 p-3 dark:bg-orange-900">
                  <span className="text-2xl">🛠️</span>
                </div>
                <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl dark:text-white">
                  Our{' '}
                  <span className="bg-gradient-to-r from-blue-600 to-purple-800 bg-clip-text text-transparent">
                    Approach
                  </span>
                </h2>
              </div>
              <div className="space-y-6">
                {approach.map((feature, index) => (
                  <div
                    key={index}
                    className="rounded-lg border border-gray-100 bg-white p-6 shadow-sm dark:border-gray-700 dark:bg-gray-800"
                  >
                    <div className="flex items-start gap-4">
                      <span className="flex-shrink-0 text-2xl">
                        {feature.icon}
                      </span>
                      <div>
                        <h3 className="mb-2 font-semibold text-gray-900 dark:text-white">
                          {feature.title}
                        </h3>
                        <p className="leading-relaxed text-gray-600 dark:text-gray-300">
                          {feature.description}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <div className="relative order-2 lg:order-1">
              <div className="absolute -inset-4 rounded-2xl bg-gradient-to-r from-orange-400 to-red-400 opacity-20 blur-lg"></div>
              <Image
                src="/images/meeting.png"
                width={500}
                height={500}
                className="relative rounded-2xl shadow-xl"
                alt="Our approach"
              />
            </div>
          </div>
        </div>
      </section>
      {/* Why Choose Us Section */}
      <section className="bg-gray-50 py-20 lg:py-32 dark:bg-gray-900">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 md:px-8 lg:px-16 xl:px-40">
          <div className="grid items-center gap-12 lg:grid-cols-2">
            <div className="space-y-8">
              <div className="flex items-center gap-4">
                <div className="rounded-full bg-yellow-100 p-3 dark:bg-yellow-900">
                  <span className="text-2xl">🎯</span>
                </div>
                <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl dark:text-white">
                  Why Choose{' '}
                  <span className="bg-gradient-to-r from-blue-600 to-purple-800 bg-clip-text text-transparent">
                    Us
                  </span>
                  ? 🏆
                </h2>
              </div>
              <div className="space-y-6">
                {whyChoose.map((benefit, index) => (
                  <div
                    key={index}
                    className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800"
                  >
                    <div className="flex items-start gap-4">
                      <span className="text-2xl">{benefit.icon}</span>
                      <div>
                        <h3 className="font-semibold text-gray-900 dark:text-white">
                          {benefit.title}
                        </h3>
                        <p className="mt-2 text-gray-600 dark:text-gray-300">
                          {benefit.description}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <div className="relative">
              <div className="absolute -inset-4 rounded-2xl bg-gradient-to-r from-yellow-400 to-orange-400 opacity-20 blur-lg"></div>
              <Image
                src="https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=600&h=600&fit=crop"
                width={600}
                height={600}
                className="relative rounded-2xl shadow-xl"
                alt="Why choose us"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="py-20 lg:py-32">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 md:px-8 lg:px-16 xl:px-40">
          <div className="grid gap-8 md:grid-cols-2">
            <div className="rounded-2xl bg-gradient-to-br from-blue-50 to-indigo-100 p-8 shadow-lg dark:from-blue-900/30 dark:to-indigo-900/30">
              <h3 className="mb-4 text-2xl font-bold text-gray-900 dark:text-white">
                📈 Transform Your Business Today
              </h3>
              <div className="space-y-4 text-gray-700 dark:text-gray-300">
                <p>
                  💥 Embrace digital transformation and propel your business
                  into the future with Sudha Software Solutions Private Limited.
                  Let us be your partner in innovation and growth.
                </p>
              </div>
            </div>
            <div className="rounded-2xl bg-gradient-to-br from-green-50 to-emerald-100 p-8 shadow-lg dark:from-green-900/30 dark:to-emerald-900/30">
              <h3 className="mb-4 text-2xl font-bold text-gray-900 dark:text-white">
                📞 Connect With Us
              </h3>
              <div className="space-y-4 text-gray-700 dark:text-gray-300">
                <p>
                  ✉️ Ready to start your digital journey? Reach out to our team
                  and discover how our tailored IT solutions can drive your
                  business forward.
                </p>
                <p>
                  Transform your ideas into reality—partner with us and
                  experience the future of technology!
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
};

export default WhatWeDo;
