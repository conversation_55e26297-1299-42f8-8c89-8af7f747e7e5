'use client';

import React, { useState, useEffect } from 'react';
// The Beams component import caused a compilation error, so it has been removed.
// If you have this component available, please adjust the path accordingly.

// All icons from Heroicons v2 (24/outline)
import {
 
  LightBulbIcon,
  BriefcaseIcon,
  ClipboardDocumentListIcon,
  EnvelopeIcon,
 
  MapPinIcon,
  ArrowRightIcon,
  CheckCircleIcon,
  AcademicCapIcon,
  Cog6ToothIcon,
  ChartBarIcon,
  UserGroupIcon,
  MegaphoneIcon, 
  RocketLaunchIcon, 
  PuzzlePieceIcon,
  CodeBracketIcon, 
} from '@heroicons/react/24/outline';
import Beams from '@/components/react-bits/Backgrounds/Beams/Beams';

// --- SECTION CONTAINER COMPONENT ---
interface SectionContainerProps {
  children: React.ReactNode;
  className?: string;
  id?: string;
}

const SectionContainer: React.FC<SectionContainerProps> = ({ children, className, id }) => {
  return (
    <section
      id={id}
      // Using bg-blue-700/10 as a base, adjusted for specific section colors
      className={`py-16 md:py-24 px-4 sm:px-6 lg:px-8 bg-blue-700/10 transition-colors duration-300 ${className || ''}`}
    >
      <div className="max-w-6xl mx-auto">
        {children}
      </div>
    </section>
  );
};

// --- HEADING COMPONENT ---
interface HeadingProps {
  children: React.ReactNode;
  highlightText?: string;
  level: 'h1' | 'h2' | 'h3' | 'h4';
  className?: string;
}

const Heading: React.FC<HeadingProps> = ({ children, highlightText, level, className }) => {
  const renderContent = () => {
    if (highlightText) {
      const parts = children?.toString().split(new RegExp(`(${highlightText})`, 'gi'));
      return (
        <>
          {parts?.map((part, index) =>
            part.toLowerCase() === highlightText.toLowerCase() ? (
              <span key={index} className="text-indigo-600">
                {part}
              </span>
            ) : (
              part
            )
          )}
        </>
      );
    }
    return children;
  };

  const baseClasses = 'font-extrabold mb-4';
  const levelClasses = {
    h1: 'text-4xl md:text-5xl lg:text-6xl text-slate-900 dark:text-gray-50',
    h2: 'text-3xl md:text-4xl lg:text-5xl text-slate-900 dark:text-gray-50',
    h3: 'text-2xl md:text-3xl lg:text-4xl text-slate-900 dark:text-gray-50',
    h4: 'text-xl md:text-2xl lg:text-3xl text-slate-900 dark:text-gray-50',
  };

  const Htag = level;

  return (
    <Htag className={`${baseClasses} ${levelClasses[level]} ${className || ''}`}>
      {renderContent()}
    </Htag>
  );
};

// --- PARAGRAPH COMPONENT ---
interface ParagraphProps {
  children: React.ReactNode;
  className?: string;
}

const Paragraph: React.FC<ParagraphProps> = ({ children, className }) => {
  return (
    <p className={`text-slate-700 dark:text-gray-300 leading-relaxed ${className || ''}`}>
      {children}
    </p>
  );
};

// --- PRIMARY BUTTON COMPONENT ---
interface PrimaryButtonProps {
  children: React.ReactNode;
  href: string;
  className?: string;
  isExternal?: boolean;
}

const PrimaryButton: React.FC<PrimaryButtonProps> = ({ children, href, className, isExternal = false }) => {
  const commonClasses = `inline-flex items-center justify-center gap-2 px-8 py-3 rounded-lg text-base font-medium transition-colors duration-200 cursor-pointer border-[1px] border-indigo-400
                       dark:text-white text-indigo-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500
                       dark:text-white dark:focus:ring-indigo-600 ${className || ''}`;
  return isExternal ? (
    <a href={href} target="_blank" rel="noopener noreferrer" className={commonClasses}>
      {children}
    </a>
  ) : (
    <a href={href} className={commonClasses}>
      {children}
    </a>
  );
};

// --- SERVICE ITEM CARD COMPONENT (Reusing ResourceItemCard styling) ---
interface ServiceItemCardProps {
  title: string;
  description: string;
  href: string;
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
}

const ServiceItemCard: React.FC<ServiceItemCardProps> = ({ title, description, href, icon: Icon }) => {
  return (
    <div
      className="group relative overflow-hidden rounded-2xl border border-gray-200 bg-blue-50 p-6 shadow-md transition-all duration-300 hover:-translate-y-1 hover:shadow-lg
                  dark:border-slate-700 dark:bg-slate-800"
    >
      {/* Subtle overlay on hover */}
      <div
        className="absolute inset-0 bg-indigo-600 opacity-0 transition-opacity duration-300 group-hover:opacity-[0.03]"
      ></div>

      <div className="relative z-10 flex flex-col h-full">
        {/* Icon with solid primary color background */}
        <div
          className="inline-flex rounded-xl border-[1px] border-indigo-400 w-12 p-3 mb-4 text-gray-800 dark:text-white transition-transform duration-300 group-hover:scale-105"
        >
          <Icon className="h-6 w-6" />
        </div>

        <h3 className="mb-2 text-lg font-semibold text-slate-900 dark:text-gray-50">
          {title}
        </h3>

        <p className="mb-4 text-sm leading-relaxed text-slate-700 dark:text-gray-300 flex-grow">
          {description}
        </p>

        {/* Button with solid primary colors */}
        <PrimaryButton href={href} className="w-full !rounded-lg !px-4 !py-2 text-sm">
          Learn More
          <ArrowRightIcon className="h-3 w-3 transition-transform group-hover:translate-x-1" />
        </PrimaryButton>
      </div>
    </div>
  );
};


// --- FEATURE CHECKLIST ITEM COMPONENT (Reused from ResourcesPage) ---
interface FeatureChecklistItemProps {
  text: string;
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
}

const FeatureChecklistItem: React.FC<FeatureChecklistItemProps> = ({ text, icon: Icon }) => {
  return (
    <div className="flex items-center gap-3">
      <Icon className="h-6 w-6 text-indigo-600 dark:text-indigo-400" />
      <span className="font-medium text-slate-700 dark:text-gray-300 text-lg">
        {text}
      </span>
    </div>
  );
};

// --- CONTACT INFO CARD COMPONENT (Reused from ResourcesPage) ---
interface ContactInfoCardProps {
  title: string;
  description: string;
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  contactDetail: string;
  linkHref?: string;
  linkText?: string;
}

const ContactInfoCard: React.FC<ContactInfoCardProps> = ({ title, description, icon: Icon, contactDetail, linkHref, linkText }) => {
  return (
    <div className="flex items-start space-x-4 p-6 rounded-xl bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 shadow-sm">
      <div className="flex-shrink-0 p-3 rounded-full bg-indigo-100 text-indigo-600 dark:bg-indigo-900/50 dark:text-indigo-400">
        <Icon className="h-6 w-6" />
      </div>
      <div>
        <h4 className="text-lg font-semibold text-slate-900 dark:text-gray-50">{title}</h4>
        <Paragraph className="text-sm !mb-1">{description}</Paragraph>
        {linkHref ? (
          <a href={linkHref} className="text-indigo-600 hover:underline dark:text-indigo-500">
            {linkText || contactDetail}
          </a>
        ) : (
          <p className="text-slate-700 dark:text-gray-300">{contactDetail}</p>
        )}
      </div>
    </div>
  );
};


// --- MAIN WHAT WE DO PAGE COMPONENT ---
const WhatWeDoPage: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const servicesOffered = [
    {
      icon: BriefcaseIcon,
      title: 'Career Coaching & Mentorship',
      description: 'Personalized guidance to navigate career transitions, define goals, and unlock your full potential.',
      href: '/services/career-coaching',
    },
    {
      icon: ClipboardDocumentListIcon,
      title: 'Resume & Cover Letter Optimization',
      description: 'Craft compelling documents that highlight your strengths and grab the attention of hiring managers.',
      href: '/services/document-optimization',
    },
    {
      icon: LightBulbIcon,
      title: 'Interview Skill Development',
      description: 'Master interview techniques with mock interviews and strategic advice to ace your next opportunity.',
      href: '/services/interview-skills',
    },
    {
      icon: AcademicCapIcon,
      title: 'Skill Development & Training',
      description: 'Access curated courses and workshops to acquire in-demand skills relevant to your career path.',
      href: '/services/skill-development',
    },
    {
      icon: UserGroupIcon,
      title: 'Professional Networking Strategies',
      description: 'Build a strong professional network and learn how to leverage connections for career advancement.',
      href: '/services/networking',
    },
    {
      icon: ChartBarIcon,
      title: 'Market Analysis & Insights',
      description: 'Stay informed with current industry trends and job market demands to make strategic career decisions.',
      href: '/services/market-analysis',
    },
    {
      icon: RocketLaunchIcon,
      title: 'Startup & Entrepreneurship Support',
      description: 'Guidance for aspiring entrepreneurs on launching and growing their ventures.',
      href: '/services/startup-support',
    },
    {
      icon: CodeBracketIcon,
      title: 'Tech Career Pathways',
      description: 'Specialized support and resources for navigating the dynamic world of technology careers.',
      href: '/services/tech-careers',
    },
  ];

  const ourApproachFeatures = [
    { icon: CheckCircleIcon, text: 'Personalized Plans' },
    { icon: CheckCircleIcon, text: 'Actionable Strategies' },
    { icon: CheckCircleIcon, text: 'Continuous Support' },
    { icon: CheckCircleIcon, text: 'Industry Expert Mentors' },
    { icon: CheckCircleIcon, text: 'Data-Driven Insights' },
    { icon: CheckCircleIcon, text: 'Community & Collaboration' },
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-slate-900 transition-colors duration-300">

      {/* Hero Section */}
      <section className="relative overflow-hidden py-24 md:py-36 bg-slate-200 dark:bg-slate-800 dark:text-white">
        
       <div className="absolute inset-0"> 
          <Beams
            beamWidth={1}
            beamHeight={8}
            beamNumber={5}
            lightColor="#ffffff"
            speed={3}
            noiseIntensity={1.45}
            scale={0.22}
            rotation={39}
          />
        </div>

        <div className="relative container mx-auto px-4 text-center z-10">
          <div className={`transition-all duration-1000 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
            <Heading level="h1" highlightText="" className="max-w-4xl mx-auto leading-tight !text-white !mb-6">
              Empower Your Future: Discover What We Do
            </Heading>
            <Paragraph className="max-w-3xl mx-auto mt-6 mb-12 text-xl  dark:!text-white/50 text-white/50">
              We provide comprehensive career development solutions, from personalized coaching to skill enhancement, designed to help you achieve your professional aspirations.
            </Paragraph>
            <PrimaryButton href="#our-services" className="rounded-full px-8 py-4 text-slate-50 bg-blue-800">
              Explore Our Services
            </PrimaryButton>
          </div>
        </div>
      </section>

      {/* Services Offered Section */}
      <SectionContainer id="our-services" className="dark:bg-slate-900 bg-slate-100">
        <Heading level="h2" className="text-center mb-6">
          Our <span className="text-indigo-600">Core Services</span>
        </Heading>
        <Paragraph className="max-w-3xl mx-auto text-center mb-16 text-lg">
          We offer a range of specialized services tailored to meet your unique career development needs, ensuring you have the tools and support to succeed.
        </Paragraph>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {servicesOffered.map((service, index) => (
            <ServiceItemCard
              key={index}
              title={service.title}
              description={service.description}
              href={service.href}
              icon={service.icon}
            />
          ))}
        </div>
      </SectionContainer>

      {/* Our Approach Section */}
      <SectionContainer className="bg-slate-200 dark:bg-slate-800">
        <div className="grid items-center gap-16 lg:grid-cols-2">
          <div className={`relative transition-all delay-300 duration-1000 ${isVisible ? 'translate-x-0 opacity-100' : '-translate-x-10 opacity-0'}`}>
            <img
              src="/images/our-strategic-approach.png" // Placeholder image
              alt="Our Strategic Approach"
              className="h-auto w-full rounded-2xl shadow-2xl"
            />
            <div className="absolute -top-4 -right-4 animate-bounce rounded-full bg-indigo-600 p-3 text-white shadow-lg">
              <Cog6ToothIcon className="h-6 w-6" />
            </div>
            <div className="absolute -bottom-4 -left-4 animate-pulse rounded-full bg-indigo-600 p-3 text-white shadow-lg">
              <PuzzlePieceIcon className="h-6 w-6" />
            </div>
          </div>
          <div>
            <Heading level="h2" className="mb-6">
              Our <span className="text-indigo-600">Unique Approach</span>
            </Heading>
            <Paragraph className="mb-8 text-xl leading-relaxed">
              We believe in a holistic and personalized approach to career development. Our methodology is designed to empower you with clarity, confidence, and the practical skills needed for lasting success.
            </Paragraph>
            <div className="mb-8 grid grid-cols-1 sm:grid-cols-2 gap-4">
              {ourApproachFeatures.map((feature, index) => (
                <FeatureChecklistItem key={index} text={feature.text} icon={feature.icon} />
              ))}
            </div>
            <PrimaryButton href="/contact">
              Connect With An Expert
            </PrimaryButton>
          </div>
        </div>
      </SectionContainer>

      {/* Call to Action / Contact Section (similar to Assistance) */}
      <SectionContainer id="get-started" className="bg-white dark:bg-gray-900">
        <div className="text-center mb-16">
          <Heading level="h2" className="mb-6">
            Ready to <span className="text-indigo-600">Elevate</span> Your Career?
          </Heading>
          <Paragraph className="max-w-3xl mx-auto text-center mb-16 text-lg">
            Take the first step towards a brighter professional future. Reach out to us today to discuss your goals and how we can help you achieve them.
          </Paragraph>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          <ContactInfoCard
            title="Book a Consultation"
            description="Schedule a free introductory call with one of our career advisors."
            icon={MegaphoneIcon}
            contactDetail="Available Slots"
            linkHref="/book-consultation"
            linkText="Schedule Now"
          />
          <ContactInfoCard
            title="Send Us a Message"
            description="Have specific questions? We're here to help."
            icon={EnvelopeIcon}
            contactDetail="<EMAIL>"
            linkHref="mailto:<EMAIL>"
            linkText="Email Us"
          />
          <ContactInfoCard
            title="Visit Our Office"
            description="Find us at our central location during business hours."
            icon={MapPinIcon}
            contactDetail="01, Ground Floor, BOI Zonal Audit Office Building, Pragati Path, Makchund Toli, Chutia, Ranchi, Jharkhand, India - 834001"
            linkHref="/contact#location"
            linkText="Get Directions"
          />
        </div>
      </SectionContainer>
    </div>
  );
};

export default WhatWeDoPage;
