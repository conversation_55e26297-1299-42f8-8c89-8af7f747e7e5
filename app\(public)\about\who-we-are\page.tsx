'use client';

import React, { useState, useEffect } from 'react';

// All icons from Heroicons v2 (24/outline)
import {
  BookOpenIcon,
  LightBulbIcon,
  BriefcaseIcon,
  ClipboardDocumentListIcon,
  EnvelopeIcon,
  PhoneIcon,
  MapPinIcon,
  MagnifyingGlassIcon,
  ArrowRightIcon,
  CheckCircleIcon,
  AcademicCapIcon,
  Cog6ToothIcon,
  ChartBarIcon,
  BuildingOffice2Icon,
  UserGroupIcon,
  GlobeAltIcon,
  AdjustmentsHorizontalIcon,
  EyeIcon,
  SparklesIcon,
  ChartPieIcon,
  MegaphoneIcon,
  DocumentTextIcon,
  PencilSquareIcon,
  CurrencyDollarIcon,
  HandRaisedIcon, // For Values: Integrity
  PuzzlePieceIcon, // For Values: Innovation
  HeartIcon, // For Values: Customer Focus
  BoltIcon, // For Values: Efficiency
  CubeTransparentIcon, // For transparency/verification
  IdentificationIcon, // For Company Registration
  CalendarDaysIcon, // For founding date
} from '@heroicons/react/24/outline';
import Beams from '@/components/react-bits/Backgrounds/Beams/Beams';

// --- SECTION CONTAINER COMPONENT ---
interface SectionContainerProps {
  children: React.ReactNode;
  className?: string;
  id?: string;
}

const SectionContainer: React.FC<SectionContainerProps> = ({ children, className, id }) => {
  return (
    <section
      id={id}
      // Using bg-blue-700/10 as a base, adjusted for specific section colors
      className={`py-16 md:py-24 px-4 sm:px-6 lg:px-8 bg-blue-700/10 transition-colors duration-300 ${className || ''}`}
    >
      <div className="max-w-6xl mx-auto">
        {children}
      </div>
    </section>
  );
};

// --- HEADING COMPONENT ---
interface HeadingProps {
  children: React.ReactNode;
  highlightText?: string;
  level: 'h1' | 'h2' | 'h3' | 'h4';
  className?: string;
}

const Heading: React.FC<HeadingProps> = ({ children, highlightText, level, className }) => {
  const renderContent = () => {
    if (highlightText) {
      const parts = children?.toString().split(new RegExp(`(${highlightText})`, 'gi'));
      return (
        <>
          {parts?.map((part, index) =>
            part.toLowerCase() === highlightText.toLowerCase() ? (
              <span key={index} className="text-indigo-600">
                {part}
              </span>
            ) : (
              part
            )
          )}
        </>
      );
    }
    return children;
  };

  const baseClasses = 'font-extrabold mb-4';
  const levelClasses = {
    h1: 'text-4xl md:text-5xl lg:text-6xl text-slate-900 dark:text-gray-50',
    h2: 'text-3xl md:text-4xl lg:text-5xl text-slate-900 dark:text-gray-50',
    h3: 'text-2xl md:text-3xl lg:text-4xl text-slate-900 dark:text-gray-50',
    h4: 'text-xl md:text-2xl lg:text-3xl text-slate-900 dark:text-gray-50',
  };

  const Htag = level;

  return (
    <Htag className={`${baseClasses} ${levelClasses[level]} ${className || ''}`}>
      {renderContent()}
    </Htag>
  );
};

// --- PARAGRAPH COMPONENT ---
interface ParagraphProps {
  children: React.ReactNode;
  className?: string;
}

const Paragraph: React.FC<ParagraphProps> = ({ children, className }) => {
  return (
    <p className={`text-slate-700 dark:text-gray-300 leading-relaxed ${className || ''}`}>
      {children}
    </p>
  );
};

// --- PRIMARY BUTTON COMPONENT ---
interface PrimaryButtonProps {
  children: React.ReactNode;
  href: string;
  className?: string;
  isExternal?: boolean;
}

const PrimaryButton: React.FC<PrimaryButtonProps> = ({ children, href, className, isExternal = false }) => {
  const commonClasses = `inline-flex items-center justify-center gap-2 px-8 py-3 rounded-lg text-base font-medium transition-colors duration-200 cursor-pointer border-[1px] border-indigo-400
                         dark:text-white text-indigo-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500
                         dark:text-white dark:focus:ring-indigo-600 ${className || ''}`;
  return isExternal ? (
    <a href={href} target="_blank" rel="noopener noreferrer" className={commonClasses}>
      {children}
    </a>
  ) : (
    <a href={href} className={commonClasses}>
      {children}
    </a>
  );
};

// --- FEATURE CHECKLIST ITEM COMPONENT (Reused for Mission/Vision/Values points) ---
interface FeatureChecklistItemProps {
  text: string;
  // Explicitly typing icon component to accept className as SVGProps
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
}

const FeatureChecklistItem: React.FC<FeatureChecklistItemProps> = ({ text, icon: Icon }) => {
  return (
    <div className="flex items-center gap-3">
      <Icon className="h-6 w-6 text-indigo-600 dark:text-indigo-400" />
      <span className="font-medium text-slate-700 dark:text-gray-300 text-lg">
        {text}
      </span>
    </div>
  );
};


// --- CONTACT INFO CARD COMPONENT (Reused for company details) ---
interface ContactInfoCardProps {
  title: string;
  description: string;
  // Explicitly typing icon component to accept className as SVGProps
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  contactDetail: string;
  linkHref?: string;
  linkText?: string;
}

const ContactInfoCard: React.FC<ContactInfoCardProps> = ({ title, description, icon: Icon, contactDetail, linkHref, linkText }) => {
  return (
    <div className="flex items-start space-x-4 p-6 rounded-xl bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 shadow-sm">
      <div className="flex-shrink-0 p-3 rounded-full bg-indigo-100 text-indigo-600 dark:bg-indigo-900/50 dark:text-indigo-400">
        <Icon className="h-6 w-6" />
      </div>
      <div>
        <h4 className="text-lg font-semibold text-slate-900 dark:text-gray-50">{title}</h4>
        <Paragraph className="text-sm !mb-1">{description}</Paragraph>
        {linkHref ? (
          <a href={linkHref} className="text-indigo-600 hover:underline dark:text-indigo-500">
            {linkText || contactDetail}
          </a>
        ) : (
          <p className="text-slate-700 dark:text-gray-300">{contactDetail}</p>
        )}
      </div>
    </div>
  );
};

// --- VALUE CARD COMPONENT (New, similar to ResourceItemCard but for values) ---
interface ValueCardProps {
  title: string;
  description: string;
  // Explicitly typing icon component to accept className as SVGProps
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
}

const ValueCard: React.FC<ValueCardProps> = ({ title, description, icon: Icon }) => {
  return (
    <div
      className="group relative overflow-hidden rounded-2xl border border-gray-200 bg-blue-50 p-6 shadow-md transition-all duration-300 hover:-translate-y-1 hover:shadow-lg
                 dark:border-slate-700 dark:bg-slate-800"
    >
      <div
        className="absolute inset-0 bg-indigo-600 opacity-0 transition-opacity duration-300 group-hover:opacity-[0.03]"
      ></div>

      <div className="relative z-10 flex flex-col h-full">
        <div
          className="inline-flex rounded-xl border-[1px] border-indigo-400 w-12 p-3 mb-4 text-gray-800 dark:text-white transition-transform duration-300 group-hover:scale-105"
        >
          {/* Removed trailing space from className for consistency */}
          <Icon className="h-6 w-6" />
        </div>

        <h3 className="mb-2 text-lg font-semibold text-slate-900 dark:text-gray-50">
          {title}
        </h3>

        <p className="mb-4 text-sm leading-relaxed text-slate-700 dark:text-gray-300 flex-grow">
          {description}
        </p>
      </div>
    </div>
  );
};


// --- MAIN WHO WE ARE PAGE COMPONENT ---
const WhoWeArePage: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const coreValues = [
    {
      icon: HandRaisedIcon,
      title: 'Integrity',
      description: 'Upholding honesty and strong moral principles in all our interactions and services.',
    },
    {
      icon: PuzzlePieceIcon,
      title: 'Innovation',
      description: 'Continuously seeking new and improved ways to empower career development.',
    },
    {
      icon: HeartIcon,
      title: 'Customer Focus',
      description: 'Prioritizing the needs and success of our users in every decision we make.',
    },
    {
      icon: UserGroupIcon,
      title: 'Collaboration',
      description: 'Fostering a supportive environment where collective effort leads to shared success.',
    },
    {
      icon: SparklesIcon,
      title: 'Excellence',
      description: 'Committing to the highest standards of quality in our content and tools.',
    },
    {
      icon: GlobeAltIcon,
      title: 'Inclusivity',
      description: 'Ensuring our resources are accessible and beneficial to everyone, regardless of background.',
    },
  ];

  const companyDetails = [
    {
      title: 'Official Company Name',
      description: 'The registered legal name of our organization.',
      icon: BuildingOffice2Icon,
      contactDetail: 'Sudha Software Solutions Pvt. Ltd.',
    },
    {
      title: 'Company Registration Number',
      description: 'Our unique identifier with the regulatory authorities.',
      icon: IdentificationIcon,
      contactDetail: 'CIN: U62099JH2025PTC02377', 
    },
    {
      title: 'Registered Address',
      description: 'Our primary legal and operational headquarters.',
      icon: MapPinIcon,
      contactDetail: '01, Ground Floor, BOI Zonal Audit Office Building, Pragati Path, Makchund Toli, Chutia, Ranchi, Jharkhand, India - 834001',
    },
    {
      title: 'Official Contact Email',
      description: 'For all official inquiries and communication.',
      icon: EnvelopeIcon,
      contactDetail: '<EMAIL>',
      linkHref: 'mailto:<EMAIL>',
      linkText: 'Email Us',
    },
    {
      title: 'Customer Service Hotline',
      description: 'Reach our support team during business hours.',
      icon: PhoneIcon,
      contactDetail: '+91 6204351245',
      linkHref: 'tel:+************',
      linkText: 'Call Now',
    },
    {
      title: 'Date of Establishment',
      description: 'The year our company was officially founded.',
      icon: CalendarDaysIcon,
      contactDetail: 'January 9, 2025',
    },
  ];


  return (
    <div className="min-h-screen bg-gray-50 dark:bg-slate-900 transition-colors duration-300 font-sans">
      {/* Removed direct <title> and <meta> tags from JSX as they belong in the HTML document's <head> */}

      {/* Hero Section */}
      <section className="relative overflow-hidden py-24 md:py-36 bg-slate-200 dark:bg-slate-800 dark:text-white">
        
        <div className="absolute inset-0"> 
          <Beams
            beamWidth={1}
            beamHeight={8}
            beamNumber={5}
            lightColor="#ffffff"
            speed={3}
            noiseIntensity={1.45}
            scale={0.22}
            rotation={39}
          />
        </div>

         <div className="relative container mx-auto px-4 text-center">
          <div className={`transition-all duration-1000 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
            <Heading level="h1" highlightText="" className="max-w-4xl mx-auto leading-tight !text-white !mb-6">
              Who We Are: Discover Our Story & Mission
            </Heading>
            <Paragraph className="max-w-3xl mx-auto mt-6 mb-12 text-xl dark:!text-white/50 text-white/50">
              At Career Portal, we're dedicated to transforming career journeys. We believe in empowering individuals with the right resources, insights, and support to achieve their professional aspirations.
            </Paragraph>
            <PrimaryButton href="/contact" className="rounded-full px-8 py-4 text-slate-50 bg-blue-800">
              Get In Touch
              <ArrowRightIcon className="h-4 w-4" />
            </PrimaryButton>
          </div>
        </div>
      </section>

      {/* Our Story Section */}
      <SectionContainer id="our-story" className="bg-white dark:bg-slate-900">
        <div className="grid items-center gap-16 lg:grid-cols-2">
          <div className={`transition-all delay-200 duration-1000 ${isVisible ? 'translate-x-0 opacity-100' : '-translate-x-10 opacity-0'}`}>
            <img
              src="/images/our-story.png"
              alt="Our Story Illustration"
              className="h-auto w-full rounded-2xl shadow-2xl"
            />
            <div className="absolute -top-4 -left-4 animate-pulse rounded-full bg-indigo-600 p-3 text-white shadow-lg">
              <BookOpenIcon className="h-6 w-6" />
            </div>
            <div className="absolute -bottom-4 -right-4 animate-bounce rounded-full bg-indigo-600 p-3 text-white shadow-lg">
              <LightBulbIcon className="h-6 w-6" />
            </div>
          </div>
          <div>
            <Heading level="h2" className="mb-6">
              Our Journey: A Story of <span className="text-indigo-600">Growth & Dedication</span>
            </Heading>
            <Paragraph className="mb-6 text-lg">
              Founded in 2025, Career Portal began with a simple yet profound vision: to bridge the gap between ambition and opportunity. We recognized the challenges individuals face in a dynamic job market and set out to create a comprehensive platform offering reliable resources and guidance.
            </Paragraph>
            <Paragraph className="mb-6 text-lg">
              From humble beginnings, we've grown into a trusted hub for career development, constantly evolving our content and tools to meet the changing needs of professionals worldwide. Our commitment to user success remains at the heart of everything we do.
            </Paragraph>
            <PrimaryButton href="/resources">
              Explore Our Resources
              <ArrowRightIcon className="h-4 w-4" />
            </PrimaryButton>
          </div>
        </div>
      </SectionContainer>

      {/* Mission & Vision Section */}
      <SectionContainer id="mission-vision" className="bg-slate-100 dark:bg-gray-900">
        <div className="text-center mb-16">
          <Heading level="h2" className="mb-6">
            Our <span className="text-indigo-600">Mission</span> & Vision
          </Heading>
          <Paragraph className="max-w-3xl mx-auto text-center text-lg">
            Guiding principles that drive our innovation and dedication to your success.
          </Paragraph>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Mission Card */}
          <div className="p-8 rounded-xl bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 shadow-lg">
            <div className="flex items-center space-x-4 mb-4">
              <div className="p-3 rounded-full bg-indigo-100 text-indigo-600 dark:bg-indigo-900/50 dark:text-indigo-400">
                <EyeIcon className="h-7 w-7" />
              </div>
              <Heading level="h3" className="!mb-0">Our Mission</Heading>
            </div>
            <Paragraph className="mb-4">
              To empower individuals globally with comprehensive, up-to-date, and actionable resources for job search, professional development, and career advancement, fostering a confident and successful workforce.
            </Paragraph>
            <ul className="space-y-3">
              <FeatureChecklistItem text="Provide High-Quality Content" icon={CheckCircleIcon} />
              <FeatureChecklistItem text="Offer Practical Tools" icon={CheckCircleIcon} />
              <FeatureChecklistItem text="Promote Continuous Learning" icon={CheckCircleIcon} />
            </ul>
          </div>

          {/* Vision Card */}
          <div className="p-8 rounded-xl bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 shadow-lg">
            <div className="flex items-center space-x-4 mb-4">
              <div className="p-3 rounded-full bg-indigo-100 text-indigo-600 dark:bg-indigo-900/50 dark:text-indigo-400">
                <SparklesIcon className="h-7 w-7" />
              </div>
              <Heading level="h3" className="!mb-0">Our Vision</Heading>
            </div>
            <Paragraph className="mb-4">
              To be the leading global platform for career empowerment, recognized for our commitment to excellence, innovation, and for positively impacting millions of professional lives.
            </Paragraph>
            <ul className="space-y-3">
              <FeatureChecklistItem text="Be a Global Leader" icon={CheckCircleIcon} />
              <FeatureChecklistItem text="Drive Positive Impact" icon={CheckCircleIcon} />
              <FeatureChecklistItem text="Inspire Professional Growth" icon={CheckCircleIcon} />
            </ul>
          </div>
        </div>
      </SectionContainer>

      {/* Our Values Section */}
      <SectionContainer id="our-values" className="bg-slate-200 dark:bg-slate-800">
        <Heading level="h2" className="text-center mb-6">
          Core Values That <span className="text-indigo-600">Guide Us</span>
        </Heading>
        <Paragraph className="max-w-3xl mx-auto text-center mb-16 text-lg">
          These fundamental principles define our culture, drive our decisions, and shape our commitment to you.
        </Paragraph>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {coreValues.map((value, index) => (
            <ValueCard
              key={index}
              title={value.title}
              description={value.description}
              icon={value.icon}
            />
          ))}
        </div>
      </SectionContainer>

      {/* Company Details for Verification Section */}
      <SectionContainer id="company-details" className="bg-white dark:bg-slate-900">
        <div className="text-center mb-16">
          <Heading level="h2" className="mb-6">
            Transparency & <span className="text-indigo-600">Company Verification</span>
          </Heading>
          <Paragraph className="max-w-3xl mx-auto text-center text-lg">
            We believe in complete transparency. Here are our official company details for your verification and assurance.
          </Paragraph>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {companyDetails.map((detail, index) => (
            <ContactInfoCard
              key={index}
              title={detail.title}
              description={detail.description}
              icon={detail.icon}
              contactDetail={detail.contactDetail}
              linkHref={detail.linkHref || undefined}
              linkText={detail.linkText || undefined}
            />
          ))}
        </div>
      </SectionContainer>

      {/* Assistance/Contact Section (Reused from original) */}
      <SectionContainer id="assistance" className="bg-slate-100 dark:bg-gray-900">
        <div className="text-center mb-16">
          <Heading level="h2" className="mb-6">
            Need <span className="text-indigo-600">Personalized</span> Assistance?
          </Heading>
          <Paragraph className="max-w-3xl mx-auto text-center mb-16 text-lg">
            If our extensive resources don't fully answer your questions, our dedicated support team is here to provide personalized guidance.
          </Paragraph>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          <ContactInfoCard
            title="Email Support"
            description="Send us your detailed queries, and we'll respond within 24 hours."
            icon={EnvelopeIcon}
            contactDetail="<EMAIL>"
            linkHref="mailto:<EMAIL>"
            linkText="Email Us Directly"
          />
          <ContactInfoCard
            title="Call Us"
            description="Speak directly with a career advisor for immediate support."
            icon={PhoneIcon}
            contactDetail="+91-************"
            linkHref="tel:+************"
            linkText="Call Now"
          />
          <ContactInfoCard
            title="Office Hours"
            description="Our team is available during these hours for your convenience."
            icon={MapPinIcon}
            contactDetail="Mon-Fri, 9:00 AM - 5:00 PM IST"
          />
        </div>
      </SectionContainer>
    </div>
  );
};

export default WhoWeArePage;
