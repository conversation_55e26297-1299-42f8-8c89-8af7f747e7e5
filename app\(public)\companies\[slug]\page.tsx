import { 
  Building2, 
  Calendar, 
  MapPin, 
  Users, 
  Mail, 
  Phone, 
  Globe, 
  Linkedin, 
  Twitter,
  Settings,
  FileText,
  Briefcase,
  Clock,
  Edit,
  ExternalLink,
  Star,
  TrendingUp
} from 'lucide-react';
import { baseUrl, getCompanyBySlug } from '@/store/features/companyApi';


// Force dynamic (server-side) rendering
export const dynamic = 'force-dynamic';


import type { Metadata} from 'next'
 
type Props = {
  params: Promise<{ slug: string }>
}
 
export async function generateMetadata(
  { params}: Props,
): Promise<Metadata> {
  // read route params
  const { slug } = await params

  try{
    const response = await getCompanyBySlug(slug)
	  const company = response?.data?.data || {};
    // console.log("params company ", company)

	return {
		title: `${company.name} | Company Details`,
		description: company.description,
		openGraph: {
			title: `${company.name} | Company Details`,
			description: company.description,
			images: [company.logo],
			url: `${baseUrl}/companies/${slug}`,
			type: 'website',
		},
		twitter: {
			title: `${company.name} | Company Details`,
			description: company.description,
			images: [company.logo],
			card: 'summary_large_image',
		},
	};
}
  catch(error){
    console.error("Error fetching company metadata", error);
    return {
      title: 'Company Details',
      description: 'Company Details',
    };
  } 
  
}
 

export default async function CompanyDetailsPage({
  params,
}: {
  params: Promise<{ slug: string }>
}) {
  const { slug } = await params


  const response = await getCompanyBySlug(slug)
    const company = response?.data?.data || {};

    const formatDate = (dateString:string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getYearsSinceFoundation = (foundedDate:string) => {
    const founded = new Date(foundedDate);
    const now = new Date();
    return now.getFullYear() - founded.getFullYear();
  };

  const getSocialIcon = (platform:string) => {
    switch (platform.toLowerCase()) {
      case 'linkedin':
        return <Linkedin className="w-5 h-5" />;
      case 'twitter':
        return <Twitter className="w-5 h-5" />;
      default:
        return <Globe className="w-5 h-5" />;
    }
  };

  const getVerificationBadge = (isVerified:boolean) => {
    return isVerified ? (
      <div className="flex items-center text-slate-600 dark:text-slate-400">
        <div className="w-2 h-2 bg-emerald-500 rounded-full mr-2"></div>
        <span className="text-xs font-medium whitespace-nowrap">Verified</span> {/* Added whitespace-nowrap */}
      </div>
    ) : (
      <div className="flex items-center text-slate-500 dark:text-slate-500">
        <div className="w-2 h-2 bg-slate-400 rounded-full mr-2"></div>
        <span className="text-xs font-medium whitespace-nowrap">Pending</span> {/* Added whitespace-nowrap */}
      </div>
    );
  };

  return (
    <div className="min-h-screen transition-all duration-500 bg-gray-100 dark:bg-slate-900">
  <div className="max-w-7xl px-5 mx-auto  min-h-screen pt-10">
    {/* Hero Section */}
    <div className="relative mb-20">
      <div className="rounded-4xl border absolute inset-0 bg-gradient-to-br bg-white dark:from-slate-900 dark:via-slate-800 dark:to-slate-900"></div>
      <div className="relative px-6 sm:px-8 pt-10 pb-6 sm:pt-12 sm:pb-8">
        <div className="max-w-6xl mx-auto">
          <div className="flex items-start justify-between flex-col sm:flex-row sm:items-center mb-6 sm:mb-8">
            <div className="flex items-center space-x-4 sm:space-x-6 mb-4 sm:mb-0">
              <div className="relative flex-shrink-0">
                <div className="w-20 h-20 sm:w-24 sm:h-24 bg-gradient-to-br from-slate-100 to-slate-200 dark:from-slate-700 dark:to-slate-800 rounded-2xl sm:rounded-3xl flex items-center justify-center shadow-inner border border-slate-200 dark:border-slate-600">
                  <Building2 className="w-10 h-10 sm:w-12 sm:h-12 text-slate-700 dark:text-slate-300" />
                </div>
                <div className="absolute -top-1 -right-1 w-5 h-5 sm:w-6 sm:h-6 bg-emerald-500 rounded-full flex items-center justify-center">
                  <Star className="w-3 h-3 text-white" />
                </div>
              </div>
              <div className='pr-4'>
                <h1 className="text-3xl sm:text-4xl lg:text-5xl font-light text-slate-900 dark:text-slate-100 mb-1 sm:mb-2 tracking-tight leading-tight">
                  {company.name}
                </h1>
                <p className="text-base sm:text-lg text-slate-600 dark:text-slate-400 mb-0.5 sm:mb-1 font-light">
                  {company.organization}
                </p>
                <p className="text-slate-500 dark:text-slate-500 font-mono text-xs sm:text-sm tracking-wide">
                  @{company.slug}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2 sm:space-x-3 flex-wrap justify-end">
              <span className={`px-3 py-1.5 sm:px-4 sm:py-2 rounded-sm text-xs sm:text-sm font-medium ${
                company.isDeleted
                  ? 'bg-red-50 text-red-700 border border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800/30'
                  : 'bg-emerald-50 text-emerald-700 border border-emerald-200 dark:bg-emerald-900/20 dark:text-emerald-400 dark:border-emerald-800/30'
              }`}>
                {company.isDeleted ? 'Inactive' : 'Active'}
              </span>
              {/* {company.isStartupIndiaRegistered && ( */}
                <span className="px-3 py-1.5 sm:px-4 sm:py-2 bg-amber-50 text-amber-700 rounded-sm text-xs sm:text-sm font-medium border border-amber-200 dark:bg-amber-900/20 dark:text-amber-400 dark:border-amber-800/30">
                  Startup India Registered
                </span>
              {/* )} */}
            </div>
          </div>
          <p className="text-sm sm:text-base text-slate-700 dark:text-slate-300 leading-relaxed font-light max-w-3xl">
            {company.description}
          </p>
        </div>
      </div>
    </div>

    {/* Main Content */}
    <div className=" pb-10 sm:pb-12">
      <div className="grid grid-cols-1 md:grid-cols-3 xl:grid-cols-4 gap-6 sm:gap-8 -mt-10 sm:-mt-12">
        {/* Left Column - Company Details */}
        <div className="md:col-span-2 xl:col-span-3 space-y-6 sm:space-y-8">
          {/* Company Overview */}
          <div className="bg-white dark:bg-slate-800/60 backdrop-blur-xl rounded-2xl sm:rounded-3xl p-6 sm:p-8 shadow-sm border border-slate-200 dark:border-slate-700/50">
            <div className="flex items-center justify-between mb-6 sm:mb-8">
              <h2 className="text-xl sm:text-2xl font-light text-slate-900 dark:text-slate-100 flex items-center">
                <Briefcase className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-slate-600 dark:text-slate-400" />
                Company Overview
              </h2>
              <TrendingUp className="w-4 h-4 sm:w-5 sm:h-5 text-slate-500 dark:text-slate-400" />
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
              {/* Industry */}
              <div className="group">
                <div className="p-5 min-h-40 bg-slate-50 dark:bg-slate-700/30 rounded-xl sm:rounded-2xl border border-slate-200 dark:border-slate-600/50 hover:shadow-lg transition-all duration-300 hover:scale-[1.02]">
                  <div className="flex items-center space-x-3 mb-3">
                    <div className="w-9 h-9 sm:w-10 sm:h-10 bg-slate-100 dark:bg-slate-600 rounded-lg sm:rounded-xl flex items-center justify-center">
                      <Settings className="w-5 h-5 text-slate-600 dark:text-slate-400" />
                    </div>
                    <p className="text-sm font-medium text-slate-500 dark:text-slate-400">Industry</p>
                  </div>
                  <p className="font-medium text-slate-900 dark:text-slate-100 text-base sm:text-lg">{company.industry}</p>
                </div>
              </div>

              {/* Sector */}
              <div className="group">
                <div className="p-5 min-h-40 bg-slate-50 dark:bg-slate-700/30 rounded-xl sm:rounded-2xl border border-slate-200 dark:border-slate-600/50 hover:shadow-lg transition-all duration-300 hover:scale-[1.02]">
                  <div className="flex items-center space-x-3 mb-3">
                    <div className="w-9 h-9 sm:w-10 sm:h-10 bg-slate-100 dark:bg-slate-600 rounded-lg sm:rounded-xl flex items-center justify-center">
                      <Building2 className="w-5 h-5 text-slate-600 dark:text-slate-400" />
                    </div>
                    <p className="text-sm font-medium text-slate-500 dark:text-slate-400">Sector</p>
                  </div>
                  <p className="font-medium text-slate-900 dark:text-slate-100 text-base sm:text-lg">{company.sector}</p>
                </div>
              </div>

              {/* Location */}
              <div className="group">
                <div className="p-5 min-h-40 bg-slate-50 dark:bg-slate-700/30 rounded-xl sm:rounded-2xl border border-slate-200 dark:border-slate-600/50 hover:shadow-lg transition-all duration-300 hover:scale-[1.02]">
                  <div className="flex items-center space-x-3 mb-3">
                    <div className="w-9 h-9 sm:w-10 sm:h-10 bg-slate-100 dark:bg-slate-600 rounded-lg sm:rounded-xl flex items-center justify-center">
                      <MapPin className="w-5 h-5 text-slate-600 dark:text-slate-400" />
                    </div>
                    <p className="text-sm font-medium text-slate-500 dark:text-slate-400">Location</p>
                  </div>
                  <p className="font-medium text-slate-900 dark:text-slate-100 text-base sm:text-lg">{company.country}</p>
                </div>
              </div>

              {/* Team Size */}
              <div className="group">
                <div className="p-5 min-h-40 bg-slate-50 dark:bg-slate-700/30 rounded-xl sm:rounded-2xl border border-slate-200 dark:border-slate-600/50 hover:shadow-lg transition-all duration-300 hover:scale-[1.02]">
                  <div className="flex items-center space-x-3 mb-3">
                    <div className="w-9 h-9 sm:w-10 sm:h-10 bg-slate-100 dark:bg-slate-600 rounded-lg sm:rounded-xl flex items-center justify-center">
                      <Users className="w-5 h-5 text-slate-600 dark:text-slate-400" />
                    </div>
                    <p className="text-sm font-medium text-slate-500 dark:text-slate-400">Team Size</p>
                  </div>
                  <p className="font-medium text-slate-900 dark:text-slate-100 text-base sm:text-lg">{company.companySize}</p>
                </div>
              </div>

              {/* Founded */}
              <div className="group sm:col-span-2">
                <div className="p-5 min-h-40 bg-slate-50 dark:bg-slate-700/30 rounded-xl sm:rounded-2xl border border-slate-200 dark:border-slate-600/50 hover:shadow-lg transition-all duration-300 hover:scale-[1.02]">
                  <div className="flex items-center space-x-3 mb-3">
                    <div className="w-9 h-9 sm:w-10 sm:h-10 bg-slate-100 dark:bg-slate-600 rounded-lg sm:rounded-xl flex items-center justify-center">
                      <Calendar className="w-5 h-5 text-slate-600 dark:text-slate-400" />
                    </div>
                    <p className="text-sm font-medium text-slate-500 dark:text-slate-400">Founded</p>
                  </div>
                  <p className="font-medium text-slate-900 dark:text-slate-100 text-base sm:text-lg">{formatDate(company.founded)}</p>
                  <p className="text-xs sm:text-sm text-slate-500 dark:text-slate-400 mt-1">{getYearsSinceFoundation(company.founded)} years in business</p>
                </div>
              </div>
            </div>
          </div>

          {/* Contact Information */}
          <div className="bg-white dark:bg-slate-800/60 backdrop-blur-xl rounded-2xl sm:rounded-3xl p-6 sm:p-8 shadow-sm border border-slate-200 dark:border-slate-700/50">
            <h2 className="text-xl sm:text-2xl font-light text-slate-900 dark:text-slate-100 mb-6 sm:mb-8">Contact Information</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* Emails */}
              <div>
                <h3 className="text-lg font-medium text-slate-900 dark:text-slate-100 mb-4 flex items-center">
                  <Mail className="w-5 h-5 mr-2 text-slate-600 dark:text-slate-400" />
                  Email
                </h3>
                <div className="space-y-3">
                  {company.emails.map((email:{ email: string; isVerified: boolean; isDefault: boolean}, index:number) => (
                    <div key={index} className="p-3 bg-slate-50 dark:bg-slate-700/30 rounded-lg border border-slate-200 dark:border-slate-600/50 hover:shadow-md transition-all duration-200">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-slate-900 dark:text-slate-100 text-sm break-words">{email.email}</p>
                          <div className="flex items-center space-x-3 mt-2 flex-wrap">
                            {getVerificationBadge(email.isVerified)}
                            {email.isDefault && (
                              <span className="px-2 py-1 bg-slate-100 dark:bg-slate-600 text-slate-700 dark:text-slate-300 text-xs rounded-full font-medium whitespace-nowrap">
                                Primary
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Phones */}
              <div>
                <h3 className="text-lg font-medium text-slate-900 dark:text-slate-100 mb-4 flex items-center">
                  <Phone className="w-5 h-5 mr-2 text-slate-600 dark:text-slate-400" />
                  Phone
                </h3>
                <div className="space-y-3">
                  {company.phones.map((phone:{ phone: string; isVerified: boolean; isDefault: boolean}, index:number) => (
                    <div key={index} className="p-3 bg-slate-50 dark:bg-slate-700/30 rounded-lg border border-slate-200 dark:border-slate-600/50 hover:shadow-md transition-all duration-200">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-slate-900 dark:text-slate-100 text-sm break-words">{phone.phone}</p>
                          <div className="flex items-center space-x-3 mt-2 flex-wrap">
                            {getVerificationBadge(phone.isVerified)}
                            {phone.isDefault && (
                              <span className="px-2 py-1 bg-slate-100 dark:bg-slate-600 text-slate-700 dark:text-slate-300 text-xs rounded-full font-medium whitespace-nowrap">
                                Primary
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Websites */}
              <div>
                <h3 className="text-lg font-medium text-slate-900 dark:text-slate-100 mb-4 flex items-center">
                  <Globe className="w-5 h-5 mr-2 text-slate-600 dark:text-slate-400" />
                  Websites
                </h3>
                <div className="space-y-3">
                  {company.websites.map((website:{ website: string; isVerified: boolean; isDefault: boolean}, index:number) => (
                    <div key={index} className="p-3 bg-slate-50 dark:bg-slate-700/30 rounded-lg border border-slate-200 dark:border-slate-600/50 hover:shadow-md transition-all duration-200">
                      <div className="flex items-center justify-between">
                        <div>
                          <a
                            href={website.website}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="font-medium text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-slate-100 text-sm flex items-center group break-words"
                          >
                            {website.website}
                            <ExternalLink className="w-3 h-3 ml-2 opacity-0 group-hover:opacity-100 transition-opacity flex-shrink-0" />
                          </a>
                          <div className="flex items-center space-x-3 mt-2 flex-wrap">
                            {getVerificationBadge(website.isVerified)}
                            {website.isDefault && (
                              <span className="px-2 py-1 bg-slate-100 dark:bg-slate-600 text-slate-700 dark:text-slate-300 text-xs rounded-full font-medium whitespace-nowrap">
                                Primary
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Right Column - Sidebar */}
        <div className="md:col-span-1 space-y-6 sm:space-y-8">
          {/* Social Media */}
          <div className="bg-white dark:bg-slate-800/60 backdrop-blur-xl rounded-2xl sm:rounded-3xl p-6 shadow-sm border border-slate-200 dark:border-slate-700/50">
            <h2 className="text-xl font-light text-slate-900 dark:text-slate-100 mb-6">Social Media</h2>
            <div className="space-y-4">
              {company.socialMedia.map((social:{ platform: string; value: string }, index:number) => (
                <a
                  key={index}
                  href={social.value}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center space-x-4 p-3 sm:p-4 bg-slate-50 dark:bg-slate-700/30 rounded-xl hover:bg-slate-100 dark:hover:bg-slate-600/30 transition-all duration-200 border border-slate-200 dark:border-slate-600/50 group"
                >
                  <div className="w-9 h-9 sm:w-10 sm:h-10 bg-slate-100 dark:bg-slate-600 rounded-lg sm:rounded-xl flex items-center justify-center text-slate-600 dark:text-slate-400 group-hover:text-slate-900 dark:group-hover:text-slate-100 transition-colors flex-shrink-0">
                    {getSocialIcon(social.platform)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="font-medium text-slate-900 dark:text-slate-100 truncate">{social.platform}</p>
                    <p className="text-sm text-slate-500 dark:text-slate-400 truncate">Follow us</p>
                  </div>
                  <ExternalLink className="w-4 h-4 text-slate-400 dark:text-slate-500 group-hover:text-slate-600 dark:group-hover:text-slate-300 transition-colors flex-shrink-0" />
                </a>
              ))}
            </div>
          </div>

          {/* Quick Stats */}
          <div className="bg-white dark:bg-slate-800/60 backdrop-blur-xl rounded-2xl sm:rounded-3xl p-6 shadow-sm border border-slate-200 dark:border-slate-700/50">
            <h2 className="text-xl font-light text-slate-900 dark:text-slate-100 mb-6">Quick Stats</h2>
            <div className="space-y-4">
              <div className="flex justify-between items-center p-3 bg-slate-50 dark:bg-slate-700/30 rounded-xl">
                <span className="text-slate-600 dark:text-slate-400 font-medium">Contacts</span>
                <span className="font-semibold text-xl text-slate-900 dark:text-slate-100">{company.emails.length + company.phones.length}</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-slate-50 dark:bg-slate-700/30 rounded-xl">
                <span className="text-slate-600 dark:text-slate-400 font-medium">Websites</span>
                <span className="font-semibold text-xl text-slate-900 dark:text-slate-100">{company.websites.length}</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-slate-50 dark:bg-slate-700/30 rounded-xl">
                <span className="text-slate-600 dark:text-slate-400 font-medium">Social</span>
                <span className="font-semibold text-xl text-slate-900 dark:text-slate-100">{company.socialMedia.length}</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-slate-50 dark:bg-slate-700/30 rounded-xl">
                <span className="text-slate-600 dark:text-slate-400 font-medium">Years Active</span>
                <span className="font-semibold text-xl text-slate-900 dark:text-slate-100">{getYearsSinceFoundation(company.founded)}</span>
              </div>
            </div>
          </div>

          {/* System Information */}
          <div className="bg-white dark:bg-slate-800/60 backdrop-blur-xl rounded-2xl sm:rounded-3xl p-6 shadow-sm border border-slate-200 dark:border-slate-700/50">
            <h2 className="text-xl font-light text-slate-900 dark:text-slate-100 mb-6 flex items-center">
              <FileText className="w-5 h-5 mr-2 text-slate-600 dark:text-slate-400" />
              System Info
            </h2>
            <div className="space-y-4">
              <div className="p-3 bg-slate-50 dark:bg-slate-700/30 rounded-xl">
                <div className="flex items-center space-x-2 mb-1">
                  <Clock className="w-4 h-4 text-slate-500 dark:text-slate-400" />
                  <p className="text-sm text-slate-500 dark:text-slate-400">Created</p>
                </div>
                <p className="text-sm font-medium text-slate-900 dark:text-slate-100">{formatDate(company.createdAt)}</p>
              </div>
              <div className="p-3 bg-slate-50 dark:bg-slate-700/30 rounded-xl">
                <div className="flex items-center space-x-2 mb-1">
                  <Edit className="w-4 h-4 text-slate-500 dark:text-slate-400" />
                  <p className="text-sm text-slate-500 dark:text-slate-400">Updated</p>
                </div>
                <p className="text-sm font-medium text-slate-900 dark:text-slate-100">{formatDate(company.updatedAt)}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
  );
};

