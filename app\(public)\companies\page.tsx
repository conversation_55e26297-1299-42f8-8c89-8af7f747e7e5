import { Metadata } from 'next';
import HeroComponent from '@/components/sections/HeroComponent';
import WhyChooseUs from '@/components/sections/WhyChooseUs';
import { Heading } from '@/components/sections/Heading';
import { Paragraph } from '@/components/sections/paragraph';
import { getAllCompanies } from '@/store/features/companyApi';
import CompanyCard from '@/components/ui/companyCard';

// Force dynamic (server-side) rendering
export const dynamic = 'force-dynamic';

const metadata: Metadata = {
  title: 'Our Companies - Sudha Software Solutions',
  description:
    'Discover our group companies at Sudha Software Solutions. Explore innovative businesses driving digital transformation and growth.',
  keywords:
    'Companies, Digital Transformation, Sudha Software Solutions, Group Companies, Innovation',
  openGraph: {
    title: 'Our Companies - Sudha Software Solutions',
    description:
      'Explore our group companies and their innovative contributions to digital transformation and business growth.',
    images: [
      'https://careers.sudhasoftwaresolutions.com/assets/banner/careers-banner.jpg',
    ],
    url: 'https://careers.sudhasoftwaresolutions.com/companies',
    type: 'website',
  },
  twitter: {
    title: 'Our Companies - Sudha Software Solutions',
    description:
      'Discover our group companies driving innovation and digital transformation.',
    images: [
      'https://careers.sudhasoftwaresolutions.com/assets/banner/careers-banner.jpg',
    ],
    card: 'summary_large_image',
    site: '@sudha_software_solutions',
    creator: '@sudha_software_solutions',
  },
};

const features = [
  {
    icon: '✅',
    title: 'Proven Expertise',
    description:
      'With a track record of delivering successful projects, we have earned the trust of our clients and the reputation of being industry leaders.',
  },
  {
    icon: '✅',
    title: 'Customer-Centric',
    description:
      'Your success is our priority. We provide dedicated support and maintain open lines of communication to ensure your vision is realized.',
  },
  {
    icon: '✅',
    title: 'Future-Ready',
    description:
      'Our solutions are designed not only for today’s challenges but also to adapt to tomorrow’s opportunities.',
  },
];

 const getAllCompaniesData = async() => {     
  try{
    const response = await getAllCompanies();
  
    return response?.data?.data?.companies || [];
  }
  catch(error:any){
    throw new Error(
      error.response?.data?.error ||
      error.message ||
      'Failed to fetch companies'
    )
  }
  }

export default async function CompaniesPage() {

  const companies = await getAllCompaniesData();

  return (
    <main>
      <HeroComponent
        heading={
          <Heading>
            Unlock the Future of <br />
            <span className="text-blue-600">Talent Acquisition</span>{' '}
            <span className="text-sm">for Our Group Companies</span>
          </Heading>
        }
        description={
          <Paragraph>
            Sudha Software Solutions, along with its innovative product
            companies and dynamic subsidiaries, is committed to building
            exceptional teams that drive digital transformation. Our unified
            career portal connects top talent with diverse opportunities across
            our group—ensuring that every company finds the right people to
            innovate, excel, and lead.
          </Paragraph>
        }
        heroImageSrc="/images/annie-spratt-QckxruozjRg-unsplash.jpg"
        heroImageClassName="h-72 rounded-xl object-cover object-top"
      />

        <div className='mx-[4%] xl:mx-[10%] py-24'>
          <Heading className='mb-10'>Our Group  <span className="text-blue-600">Companies</span></Heading>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {companies?.map((company : any) => (              
              <CompanyCard key={company.slug} company={company}/>              
            ))
            }
          </div>
        </div>
 

      <WhyChooseUs
        heading={
          <Heading>
            Why
            <span className="text-blue-600"> Choose </span> Us
          </Heading>
        }
        iconSrc="/images/archery2.png"
        imageSrc="/images/why-choose-us.jpg"
        features={features}
      />
    </main>
  );
}