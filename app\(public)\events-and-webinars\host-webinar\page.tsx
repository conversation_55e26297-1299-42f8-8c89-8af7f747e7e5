'use client';
import React, { useState } from 'react';
import Heading from '@/components/sections/Heading';
import Paragraph from '@/components/sections/paragraph';

export default function HostEventPage() {
  const [submitted, setSubmitted] = useState(false);

  return (
    <div className="mx-auto max-w-2xl px-4 py-12">
      <Heading as="h1" className="mb-4 text-3xl">
        Host an Event / Webinar
      </Heading>
      <Paragraph className="mb-8">
        Fill out the form below to propose your event or webinar. Our team will
        review your submission and get in touch with you soon.
      </Paragraph>
      {submitted ? (
        <div className="rounded-lg bg-green-100 p-6 text-center font-semibold text-green-800">
          Thank you for your interest! We have received your submission.
        </div>
      ) : (
        <form
          className="space-y-6"
          onSubmit={e => {
            e.preventDefault();
            setSubmitted(true);
          }}
        >
          <div>
            <label className="mb-1 block font-medium">Title</label>
            <input
              type="text"
              required
              className="w-full rounded border px-3 py-2"
              placeholder="Event title"
              name="title"
            />
          </div>
          <div>
            <label className="mb-1 block font-medium">Slug</label>
            <input
              type="text"
              required
              className="w-full rounded border px-3 py-2"
              placeholder="event-slug"
              name="slug"
            />
          </div>
          <div>
            <label className="mb-1 block font-medium">Description</label>
            <textarea
              required
              className="w-full rounded border px-3 py-2"
              placeholder="Describe your event/webinar"
              rows={4}
              name="description"
            />
          </div>
          <div>
            <label className="mb-1 block font-medium">
              Tags (comma separated)
            </label>
            <input
              type="text"
              className="w-full rounded border px-3 py-2"
              placeholder="e.g. react, frontend, web"
              name="tags"
            />
          </div>
          <div>
            <label className="mb-1 block font-medium">Hosted By Users</label>
            {/* TODO: Replace with multi-select dropdown populated from backend */}
            <select
              multiple
              className="w-full rounded border px-3 py-2"
              name="hostedByUsers"
            >
              <option value="">Select users</option>
            </select>
          </div>
          <div>
            <label className="mb-1 block font-medium">
              Hosted By Companies
            </label>
            {/* TODO: Replace with multi-select dropdown populated from backend */}
            <select
              multiple
              className="w-full rounded border px-3 py-2"
              name="hostedByCompanies"
            >
              <option value="">Select companies</option>
            </select>
          </div>
          <div>
            <label className="mb-1 block font-medium">Host Name</label>
            <input
              type="text"
              className="w-full rounded border px-3 py-2"
              placeholder="Host name"
              name="hostName"
            />
          </div>
          <div>
            <label className="mb-1 block font-medium">Host Designation</label>
            <input
              type="text"
              className="w-full rounded border px-3 py-2"
              placeholder="Host designation"
              name="hostDesignation"
            />
          </div>
          <div>
            <label className="mb-1 block font-medium">Host Image URL</label>
            <input
              type="url"
              className="w-full rounded border px-3 py-2"
              placeholder="https://example.com/image.jpg"
              name="hostImage"
            />
          </div>
          <div>
            <label className="mb-1 block font-medium">Banner Image URL</label>
            <input
              type="url"
              className="w-full rounded border px-3 py-2"
              placeholder="https://example.com/banner.jpg"
              name="bannerImage"
            />
          </div>
          <div>
            <label className="mb-1 block font-medium">Language</label>
            <input
              type="text"
              className="w-full rounded border px-3 py-2"
              placeholder="Language"
              name="language"
            />
          </div>
          <div>
            <label className="mb-1 block font-medium">Category</label>
            <input
              type="text"
              className="w-full rounded border px-3 py-2"
              placeholder="Category"
              name="category"
            />
          </div>
          <div>
            <label className="mb-1 block font-medium">Level</label>
            <select className="w-full rounded border px-3 py-2" name="level">
              <option value="">Select level</option>
              <option value="Beginner">Beginner</option>
              <option value="Intermediate">Intermediate</option>
              <option value="Advanced">Advanced</option>
            </select>
          </div>
          <div>
            <label className="mb-1 block font-medium">Mode</label>
            <select
              className="w-full rounded border px-3 py-2"
              name="mode"
              required
            >
              <option value="">Select mode</option>
              <option value="Online">Online</option>
              <option value="Offline">Offline</option>
            </select>
          </div>
          <div>
            <label className="mb-1 block font-medium">Platform</label>
            <input
              type="text"
              className="w-full rounded border px-3 py-2"
              placeholder="Platform (e.g. Zoom, Google Meet)"
              name="platform"
            />
          </div>
          <div>
            <label className="mb-1 block font-medium">Meeting Link</label>
            <input
              type="url"
              className="w-full rounded border px-3 py-2"
              placeholder="https://meeting-link.com"
              name="meetingLink"
            />
          </div>
          <div>
            <label className="mb-1 block font-medium">Location</label>
            <input
              type="text"
              className="w-full rounded border px-3 py-2"
              placeholder="Location"
              name="location"
            />
          </div>
          <div>
            <label className="mb-1 block font-medium">Timezone</label>
            <input
              type="text"
              className="w-full rounded border px-3 py-2"
              placeholder="Timezone (e.g. Asia/Kolkata)"
              name="timezone"
            />
          </div>
          <div>
            <label className="mb-1 block font-medium">Start Time</label>
            <input
              type="datetime-local"
              required
              className="w-full rounded border px-3 py-2"
              name="startTime"
            />
          </div>
          <div>
            <label className="mb-1 block font-medium">End Time</label>
            <input
              type="datetime-local"
              className="w-full rounded border px-3 py-2"
              name="endTime"
            />
          </div>
          <div>
            <label className="mb-1 block font-medium">Duration (minutes)</label>
            <input
              type="number"
              min={1}
              className="w-full rounded border px-3 py-2"
              placeholder="Duration in minutes"
              name="durationMinutes"
            />
          </div>
          <div>
            <label className="mb-1 block font-medium">Is Paid?</label>
            <select
              className="w-full rounded border px-3 py-2"
              name="isPaid"
              required
            >
              <option value="">Select</option>
              <option value="true">Yes</option>
              <option value="false">No</option>
            </select>
          </div>
          <div>
            <label className="mb-1 block font-medium">Price</label>
            <input
              type="number"
              min={0}
              className="w-full rounded border px-3 py-2"
              placeholder="Price"
              name="price"
            />
          </div>
          <div>
            <label className="mb-1 block font-medium">Currency</label>
            <input
              type="text"
              className="w-full rounded border px-3 py-2"
              placeholder="Currency (e.g. USD, INR)"
              name="currency"
            />
          </div>
          <div>
            <label className="mb-1 block font-medium">
              Registration Deadline
            </label>
            <input
              type="datetime-local"
              className="w-full rounded border px-3 py-2"
              name="registrationDeadline"
            />
          </div>
          <div>
            <label className="mb-1 block font-medium">Capacity</label>
            <input
              type="number"
              min={1}
              className="w-full rounded border px-3 py-2"
              placeholder="Capacity"
              name="capacity"
            />
          </div>
          <div>
            <label className="mb-1 block font-medium">
              Certificate Offered?
            </label>
            <select
              className="w-full rounded border px-3 py-2"
              name="certificateOffered"
            >
              <option value="">Select</option>
              <option value="true">Yes</option>
              <option value="false">No</option>
            </select>
          </div>
          <div>
            <label className="mb-1 block font-medium">
              Resources Provided?
            </label>
            <select
              className="w-full rounded border px-3 py-2"
              name="resourcesProvided"
            >
              <option value="">Select</option>
              <option value="true">Yes</option>
              <option value="false">No</option>
            </select>
          </div>
          <div>
            <label className="mb-1 block font-medium">Recording Access?</label>
            <select
              className="w-full rounded border px-3 py-2"
              name="recordingAccess"
            >
              <option value="">Select</option>
              <option value="true">Yes</option>
              <option value="false">No</option>
            </select>
          </div>
          <button
            type="submit"
            className="w-full rounded-lg bg-blue-600 px-6 py-3 font-semibold text-white shadow transition hover:bg-blue-700"
          >
            Submit
          </button>
        </form>
      )}
    </div>
  );
}
