'use client';
import React, { useEffect, useState } from 'react';
import HeroComponent from '@/components/sections/HeroComponent';
import WhyChooseUs from '@/components/sections/WhyChooseUs';
import Heading from '@/components/sections/Heading';
import Paragraph from '@/components/sections/paragraph';
import axios from 'axios';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { ArrowRight } from 'lucide-react';
import { eventBenefits } from '@/constant/events';
import UpcomingEventCards from '@/components/custom/upcomingEventsCard';
import PastEventCards from '@/components/custom/pastEventsCard';

type EventType = {
  _id: string;
  bannerImage?: string | null;
  title: string;
  description: string;
  startTime: string;
  endTime: string;
  timezone: string;
  durationMinutes: number;
  mode: string;
  platform: string;
  registeredUsers?: any[];
  isActive: boolean;
  isDeleted: boolean;
  isFeatured?: boolean;
  recordingAccess?: boolean;
  certificateOffered?: boolean;
  resourcesProvided?: boolean;
  tags?: string[];
  category?: string;
  level?: string;
  hostName?: string;
  isPaid?: boolean;
  currency?: string;
  price?: number;
  capacity?: number;
};

export default function EventsPage() {
  const [events, setEvents] = useState<EventType[]>([]);

  useEffect(() => {
    async function fetchEvents() {
      try {
        const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
        if (!baseUrl)
          throw new Error(
            'NEXT_PUBLIC_BASE_URL environment variable is not set'
          );
        const url = new URL('/api/v1/events', baseUrl).toString();
        const response = await axios.get(url);
        if (!response.data.success)
          throw new Error(response.data.error || 'Failed to fetch companies');
        setEvents(response.data.data.events);
        console.log('Events fetched:', response.data.data.events);
      } catch (error: any) {
        console.error(error);
      }
    }
    fetchEvents();
  }, []);

  return (
    <>
      <HeroComponent
        heading={
          <Heading>
            Join Our <span className="text-blue-600">Events & Webinars</span>
          </Heading>
        }
        description={
          <Paragraph>
            Participate in our interactive events and webinars to learn from
            industry experts, expand your network, and stay ahead in your
            career. Whether you’re looking for the latest tech insights or want
            to connect with like-minded professionals, our events are designed
            for you.
          </Paragraph>
        }
        heroImageSrc="/images/job.png"
        heroImageWidth={500}
        heroImageHeight={500}
        heroImageClassName="rounded-xl"
      />

      <section className="relative z-10 mx-auto max-w-7xl px-4 py-16 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, type: 'spring' }}
          viewport={{ once: true }}
        >
          <div className="rounded-2xl bg-white/80 p-6 shadow-md md:p-10 dark:bg-gray-900/80">
            <div className="mb-6 flex items-center justify-between">
              <Heading as="h2" className="mb-0 text-2xl md:text-3xl">
                Upcoming Events & Webinars
              </Heading>
              <Link
                href="/events-and-webinars/upcoming-events"
                className="group flex items-center gap-1 font-medium text-blue-600 hover:underline"
              >
                View More
                <ArrowRight className="h-4 w-4 transition-transform group-hover:translate-x-1" />
              </Link>
            </div>
            <UpcomingEventCards events={events} />
          </div>
        </motion.div>
      </section>

      <section className="relative z-10 mx-auto max-w-7xl px-4 py-16 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, type: 'spring' }}
          viewport={{ once: true }}
        >
          <div className="rounded-2xl bg-white/80 p-6 shadow-md md:p-10 dark:bg-gray-900/80">
            <div className="mb-6 flex items-center justify-between">
              <Heading as="h2" className="mb-0 text-2xl md:text-3xl">
                Past Events & Recorded Webinars
              </Heading>
              <Link
                href="/events-and-webinars/past-events"
                className="group flex items-center gap-1 font-medium text-blue-600 hover:underline"
              >
                View More
                <ArrowRight className="h-4 w-4 transition-transform group-hover:translate-x-1" />
              </Link>
            </div>
            <PastEventCards events={events} />
          </div>
        </motion.div>
      </section>

      <section className="relative z-10 mx-auto flex max-w-4xl flex-col items-center px-4 py-16 text-center sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          whileInView={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5, type: 'spring' }}
          viewport={{ once: true }}
        >
          <div className="rounded-2xl bg-white/80 p-6 shadow-md md:p-10 dark:bg-gray-900/80">
            <Heading as="h2" className="mb-4 text-2xl md:text-3xl">
              Want to Host an Event or Webinar?
            </Heading>
            <Paragraph className="mb-6">
              Share your knowledge, inspire others, and grow your network by
              hosting your own event or webinar with Sudha Software Solutions.
            </Paragraph>
            <Link
              href="/events-and-webinars/host-webinar"
              className="inline-block rounded-lg bg-blue-600 px-6 py-3 font-semibold text-white shadow transition hover:bg-blue-700"
            >
              Host an Event / Webinar
            </Link>
          </div>
        </motion.div>
      </section>

      <WhyChooseUs
        heading={
          <Heading>
            🌟 Why Attend Our{' '}
            <span className="text-blue-600">Events & Webinars</span>?
          </Heading>
        }
        iconSrc="/images/archery2.png"
        imageSrc="/images/internships.png"
        features={eventBenefits}
      />
    </>
  );
}
