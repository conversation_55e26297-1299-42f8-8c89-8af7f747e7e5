import NavBar from '@/components/custom/nav-bar';
import Footer from '@/components/sections/Footer';
import Disclaimer from '@/components/sections/Disclaimer';

export default function PublicLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <>
      <NavBar />
      <main className="font-secondary min-h-screen bg-gray-50 text-gray-900 dark:bg-slate-900 dark:text-gray-50">
        {children}
      </main>
      <Footer />
    </>
  );
}
