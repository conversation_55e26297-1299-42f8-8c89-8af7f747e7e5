// app/freelance/page.tsx
import React from 'react';
import { Metadata } from 'next';
import { Heading } from '@/components/sections/Heading';
import { Paragraph } from '@/components/sections/paragraph';
import { popularServices } from '@/constant/opportunities';

// Force dynamic rendering (server-side) to avoid static generation errors
export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: 'Freelance Opportunities - Sudha Software Solutions Career Portal',
  description:
    'Connect with top freelance opportunities through Sudha Software Solutions. Find projects in web development, UI/UX design, digital marketing, and more. Join our network of trusted freelancers.',
  keywords:
    'Freelance, Remote Work, Web Development, UI/UX Design, Digital Marketing, Graphic Design, Video Production, Freelance Jobs, Contract Work',
  openGraph: {
    title: 'Freelance Opportunities - Sudha Software Solutions',
    description:
      'Discover freelance projects and connect with businesses looking for talented professionals. Build your career with flexible, remote opportunities.',
    images: ['https://careers.sudhasoftwaresolutions.com/images/freelance.png'],
    url: 'https://careers.sudhasoftwaresolutions.com/freelance',
    type: 'website',
  },
  twitter: {
    title: 'Freelance Opportunities - Sudha Software Solutions',
    description:
      'Join our network of trusted freelancers and discover exciting project opportunities in tech, design, and digital services.',
    images: ['https://careers.sudhasoftwaresolutions.com/images/freelance.png'],
    card: 'summary_large_image',
    site: '@sudha_software_solutions',
    creator: '@sudha_software_solutions',
  },
};

export default function FreelancePage() {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-slate-900">
      {/* Hero Section */}
      <section className="lg:px-[7%] relative overflow-hidden lg:py-4 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
        <div className="container mx-auto px-4">
          <div className="max-w-8xl mx-auto ">
            {/* Main Card */}
            <div className="relative overflow-hidden rounded-3xl border border-gray-200 bg-gradient-to-br from-indigo-600 via-cyan to-cyan-100 py-16 shadow-2xl dark:border-slate-700 dark:bg-slate-800 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
              <div className="hidden lg:relative lg:inset-0 lg:flex">
                <h1 className="pointer-events-none mx-auto text-9xl font-black tracking-wider text-gray-100 select-none lg:text-[12rem] dark:text-slate-700">
                  FREELANCE
                </h1>
              </div>

              {/* Content */}
              <div className="relative z-10 p-8 lg:p-12">
                <div className="grid grid-cols-1 items-center gap-12 lg:grid-cols-2">
                  {/* Left Content */}
                  <div className="z-9 space-y-8">
                    <div className="space-y-6">
                      <Heading className="text-4xl font-bold text-slate-900 lg:text-5xl dark:text-gray-50">
                        Connect with Top{' '}
                        <span className="text-indigo-600">Freelance</span>{' '}
                        Talent
                      </Heading>
                      <Paragraph className="text-lg text-slate-600 dark:text-gray-300">
                        A freelance service web portal connects businesses with
                        freelancers, facilitating project collaboration and
                        hiring.
                      </Paragraph>
                    </div>

                    {/* Search Bar */}
                    <div className="relative">
                      <div className="flex items-center overflow-hidden rounded-full border border-gray-200 bg-gray-50 p-2 shadow-lg dark:border-slate-600 dark:bg-slate-700">
                        <div className="flex flex-1 items-center px-4">
                          <svg
                            className="mr-3 h-5 w-5 text-gray-400"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                            />
                          </svg>
                          <input
                            type="text"
                            placeholder="Search for any services..."
                            className="flex-1 bg-transparent text-gray-700 outline-none dark:text-gray-300"
                          />
                        </div>
                        <button className="rounded-full bg-slate-900 p-3 text-white transition-colors hover:bg-slate-800 dark:bg-indigo-600 dark:hover:bg-indigo-700">
                          <svg
                            className="h-4 w-4"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M9 5l7 7-7 7"
                            />
                          </svg>
                        </button>
                      </div>
                    </div>

                    {/* Popular Skills */}
                    <div className="space-y-3">
                      <p className="text-sm font-medium text-slate-700 dark:text-gray-300">
                        Popular skills:
                      </p>
                      <div className="flex flex-wrap gap-2">
                        {[
                          'web design',
                          'ui/ux design',
                          'databases',
                          'business cards',
                        ].map(skill => (
                          <span
                            key={skill}
                            className="cursor-pointer rounded-full bg-gray-200 px-3 py-1 text-sm text-gray-700 transition-colors hover:bg-gray-300 dark:bg-slate-600 dark:text-gray-300 dark:hover:bg-slate-500"
                          >
                            {skill}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* Right Image */}
                  <div className="hidden lg:absolute lg:right-2 lg:z-8 lg:flex lg:justify-end">
                    <img
                      src="/images/freelance.png"
                      alt="Freelancer working on laptop"
                      className="max-w-8xl h-auto w-full scale-x-[-1] transform rounded-2xl object-cover"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Popular Services Section */}
      <section className="lg:px-[7%] bg-gray-50 py-16 dark:bg-slate-900">
        <div className="container mx-auto px-4">
          <div className="mb-12 text-center">
            <Heading className="mb-4 text-3xl font-bold text-slate-900 lg:text-4xl dark:text-gray-50">
              Popular <span className="text-indigo-600">Services</span>
            </Heading>
            <Paragraph className="mx-auto max-w-2xl text-slate-600 dark:text-gray-400">
              Freelancing offers a diverse range of popular services, from web
              development to content writing, catering to various clients'
              needs.
            </Paragraph>
          </div>

          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
            {popularServices.map((service, index) => (
              <div
                key={index}
                className="group rounded-2xl border border-gray-200 bg-white p-6 shadow-lg transition-all duration-300 hover:-translate-y-1 hover:shadow-xl dark:border-slate-700 dark:bg-slate-800"
              >
                <div
                  className={`h-12 w-12 rounded-xl bg-gradient-to-r ${service.gradient} mb-4 flex items-center justify-center text-xl text-white transition-transform duration-300 group-hover:scale-110`}
                >
                  {service.icon}
                </div>

                <h3 className="mb-3 text-xl font-semibold text-slate-900 dark:text-gray-50">
                  {service.title}
                </h3>

                <p className="mb-4 text-sm leading-relaxed text-slate-600 dark:text-gray-400">
                  {service.description}
                </p>

                <div className="mb-4 flex flex-wrap gap-2">
                  {service.tags.map((tag, tagIndex) => (
                    <span
                      key={tagIndex}
                      className="rounded-md bg-gray-100 px-2 py-1 text-xs text-gray-600 dark:bg-slate-700 dark:text-gray-300"
                    >
                      {tag}
                    </span>
                  ))}
                </div>

                <button className="w-full rounded-lg bg-slate-900 py-2 text-sm font-medium text-white transition-colors hover:bg-slate-800 dark:bg-indigo-600 dark:hover:bg-indigo-700">
                  View Projects
                </button>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="lg:px-[7%] bg-gradient-to-r from-indigo-600 to-purple-700 py-16 dark:from-indigo-700 dark:to-purple-800">
        <div className="container mx-auto px-4 text-center">
          <Heading className="mb-4 text-3xl font-bold text-white lg:text-4xl">
            Ready to Start Your Freelance Journey?
          </Heading>
          <Paragraph className="mx-auto mb-8 max-w-2xl text-indigo-100">
            Join thousands of successful freelancers who have built their
            careers through our platform. Connect with clients, showcase your
            skills, and grow your business.
          </Paragraph>
          <div className="flex flex-col justify-center gap-4 sm:flex-row">
            <button className="rounded-lg bg-white px-8 py-3 font-semibold text-indigo-600 transition-colors hover:bg-gray-100">
              Get Started as Freelancer
            </button>
            <button className="rounded-lg border-2 border-white bg-transparent px-8 py-3 font-semibold text-white transition-colors hover:bg-white hover:text-indigo-600">
              Post a Project
            </button>
          </div>
        </div>
      </section>
    </div>
  );
}
