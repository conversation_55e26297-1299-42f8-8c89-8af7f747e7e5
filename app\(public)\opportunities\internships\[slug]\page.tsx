import React from 'react';
import {
  GraduationCap,
  Calendar,
  MapPin,
  Users,
  Clock,
  DollarSign,
  Target,
  Award,
  CheckCircle,
  Star,
  TrendingUp,
  Building2,
  Globe,
  Edit,
  FileText,
  ArrowRight,
  Phone, // Added Phone icon
  Mail, // Added Mail icon
  BookOpen,
  Timer,
  Languages, // Added Languages icon
  MessageSquare,
  User,
  Briefcase
} from 'lucide-react';



import { Metadata } from 'next';
import { baseUrl, getInternshipBySlug } from '@/store/features/internshipsApi';

// Force dynamic (server-side) rendering
export const dynamic = 'force-dynamic';

type Props = {
  params: Promise<{ slug: string }>
}
 
export async function generateMetadata(
  { params}: Props,
): Promise<Metadata> {
  // read route params
  const { slug } = await params

  try{
    const response = await getInternshipBySlug(slug)
    const internship = response?.data?.data || {};
    // console.log("params internship ", internship)

  return {
    title: `${internship.title} | Internship Details`,
    description: internship.description,
    openGraph: {
      title: `${internship.title} | Internship Details`,
      description: internship.description,
      images: [internship.logo],
      url: `${baseUrl}/companies/${slug}`,
      type: 'website',
    },
    twitter: {
      title: `${internship.title} | Internship Details`,
      description: internship.description,
      images: [internship.logo],
      card: 'summary_large_image',
    },
  };
}
  catch(error){
    console.error("Error fetching internship metadata", error);
    return {
      title: 'Internship Details',
      description: 'Internship Details',
    };
  }
  }




const InternshipDetailsPage = async ({
  params,
}: {
  params: Promise<{ slug: string }>
}) => {
  const { slug } = await params
  const response = await getInternshipBySlug(slug)
  const internship = response?.data?.data || {};




  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getSkillLevelColor = (level: string) => {
    switch (level.toLowerCase()) {
      case 'beginner':
        return 'bg-green-50 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800/30';
      case 'intermediate':
        return 'bg-amber-50 text-amber-700 border-amber-200 dark:bg-amber-900/20 dark:text-amber-400 dark:border-amber-800/30';
      case 'advanced':
        return 'bg-red-50 text-red-700 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800/30';
      default:
        return 'bg-slate-50 text-slate-700 border-slate-200 dark:bg-slate-900/20 dark:text-slate-400 dark:border-slate-800/30';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return 'bg-green-50 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800/30';
      case 'draft':
        return 'bg-yellow-50 text-yellow-700 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800/30';
      case 'closed':
        return 'bg-red-50 text-red-700 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800/30';
      default:
        return 'bg-slate-50 text-slate-700 border-slate-200 dark:bg-slate-900/20 dark:text-slate-400 dark:border-slate-800/30';
    }
  };

  return (
    <div className="min-h-screen transition-all duration-500 bg-gray-100 dark:bg-slate-900">
      <div className="max-w-7xl px-5 mx-auto min-h-screen pt-10">
        {/* Hero Section */}
        <div className="relative mb-20">
          <div className="rounded-4xl border absolute inset-0 bg-gradient-to-br bg-white dark:from-slate-900 dark:via-slate-800 dark:to-slate-900"></div>
          <div className="relative px-6 sm:px-8 pt-10 pb-6 sm:pt-12 sm:pb-8">
            <div className="max-w-6xl mx-auto">
              <div className="flex items-start justify-between flex-col sm:flex-row sm:items-center mb-6 sm:mb-8">
                <div className="flex items-center space-x-4 sm:space-x-6 mb-4 sm:mb-0">
                  <div className="relative flex-shrink-0">
                    <div className="w-20 h-20 sm:w-24 sm:h-24 bg-gradient-to-br from-emerald-100 to-emerald-200 dark:from-emerald-700 dark:to-emerald-800 rounded-2xl sm:rounded-3xl flex items-center justify-center shadow-inner border border-emerald-200 dark:border-emerald-600">
                      <GraduationCap className="w-10 h-10 sm:w-12 sm:h-12 text-emerald-700 dark:text-emerald-300" />
                    </div>
                    <div className="absolute -top-1 -right-1 w-5 h-5 sm:w-6 sm:h-6 bg-emerald-500 rounded-full flex items-center justify-center">
                      <BookOpen className="w-3 h-3 text-white" />
                    </div>
                  </div>
                  <div className='pr-4'>
                    <h1 className="text-3xl sm:text-4xl lg:text-5xl font-light text-slate-900 dark:text-slate-100 mb-1 sm:mb-2 tracking-tight leading-tight">
                      {internship.title}
                    </h1>
                    <p className="text-base sm:text-lg text-slate-600 dark:text-slate-400 mb-0.5 sm:mb-1 font-light">
                      {internship.company?.name}
                    </p>
                    <p className="text-xs sm:text-sm text-slate-600 dark:text-slate-400 mb-0.5 sm:mb-1 font-light">
                      {internship.location?.city}, {internship.location?.state}, {internship.location?.country}
                    </p>
                    <p className="text-slate-500 dark:text-slate-500 font-mono text-xs sm:text-sm tracking-wide">
                      {internship.mode} • {internship.workPlaceType} • {internship.duration.min}-{internship.duration.max} {internship.duration.frequency}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2 sm:space-x-3 flex-wrap justify-end">
                  <span className="px-3 py-1.5 sm:px-4 sm:py-2 bg-emerald-50 text-emerald-700 border border-emerald-200 rounded-sm text-xs sm:text-sm font-medium dark:bg-emerald-900/20 dark:text-emerald-400 dark:border-emerald-800/30">
                    {internship.openings} Opening{internship.openings > 1 ? 's' : ''}
                  </span>
                  {/* <span className="px-3 py-1.5 sm:px-4 sm:py-2 bg-blue-50 text-blue-700 border border-blue-200 rounded-sm text-xs sm:text-sm font-medium dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800/30">
                    {internship.stipend}
                  </span> */}
                  {/* <span className={`px-3 py-1.5 sm:px-4 sm:py-2 rounded-sm text-xs sm:text-sm font-medium border ${getStatusColor(internship.status)}`}>
                    {internship.status.charAt(0).toUpperCase() + internship.status.slice(1)}
                  </span> */}
                </div>
              </div>
              <p className="text-sm sm:text-base text-slate-700 dark:text-slate-300 leading-relaxed font-light max-w-4xl">
                {internship.description}
              </p>

              {/* Tags */}
              <div className="flex flex-wrap gap-2 mt-6">
                {internship.tags.map((tag: string, index: number) => (
                  <span key={index} className="px-3 py-1 bg-slate-100 text-slate-700 rounded-full text-sm dark:bg-slate-700 dark:text-slate-300">
                    #{tag}
                  </span>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="pb-10 sm:pb-12">
          <div className="grid grid-cols-1 md:grid-cols-3 xl:grid-cols-4 gap-6 sm:gap-8 -mt-10 sm:-mt-12">
            {/* Left Column - Internship Details */}
            <div className="md:col-span-2 xl:col-span-3 space-y-6 sm:space-y-8">
              {/* Internship Overview */}
              <div className="bg-white dark:bg-slate-800/60 backdrop-blur-xl rounded-2xl sm:rounded-3xl p-6 sm:p-8 shadow-sm border border-slate-200 dark:border-slate-700/50">
                <div className="flex items-center justify-between mb-6 sm:mb-8">
                  <h2 className="text-xl sm:text-2xl font-light text-slate-900 dark:text-slate-100 flex items-center">
                    <GraduationCap className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-slate-600 dark:text-slate-400" />
                    Internship Overview
                  </h2>
                  <TrendingUp className="w-4 h-4 sm:w-5 sm:h-5 text-slate-500 dark:text-slate-400" />
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
                  {/* Stipend */}
                  <div className="group">
                    <div className="p-5 bg-slate-50 dark:bg-slate-700/30 rounded-xl sm:rounded-2xl border border-slate-200 dark:border-slate-600/50 hover:shadow-lg transition-all duration-300 hover:scale-[1.02]">
                      <div className="flex items-center space-x-3 mb-3">
                        <div className="w-9 h-9 sm:w-10 sm:h-10 bg-slate-100 dark:bg-slate-600 rounded-lg sm:rounded-xl flex items-center justify-center">
                          <DollarSign className="w-5 h-5 text-slate-600 dark:text-slate-400" />
                        </div>
                        <p className="text-sm font-medium text-slate-500 dark:text-slate-400">Stipend</p>
                      </div>
                      <p className="font-medium text-slate-900 dark:text-slate-100 text-base sm:text-lg">{internship.stipend}</p>
                    </div>
                  </div>

                  {/* Duration */}
                  <div className="group">
                    <div className="p-5 bg-slate-50 dark:bg-slate-700/30 rounded-xl sm:rounded-2xl border border-slate-200 dark:border-slate-600/50 hover:shadow-lg transition-all duration-300 hover:scale-[1.02]">
                      <div className="flex items-center space-x-3 mb-3">
                        <div className="w-9 h-9 sm:w-10 sm:h-10 bg-slate-100 dark:bg-slate-600 rounded-lg sm:rounded-xl flex items-center justify-center">
                          <Timer className="w-5 h-5 text-slate-600 dark:text-slate-400" />
                        </div>
                        <p className="text-sm font-medium text-slate-500 dark:text-slate-400">Duration</p>
                      </div>
                      <p className="font-medium text-slate-900 dark:text-slate-100 text-base sm:text-lg">{internship.duration.min}-{internship.duration.max} {internship.duration.frequency}</p>
                    </div>
                  </div>

                  {/* Location */}
                  <div className="group">
                    <div className="p-5 bg-slate-50 dark:bg-slate-700/30 rounded-xl sm:rounded-2xl border border-slate-200 dark:border-slate-600/50 hover:shadow-lg transition-all duration-300 hover:scale-[1.02]">
                      <div className="flex items-center space-x-3 mb-3">
                        <div className="w-9 h-9 sm:w-10 sm:h-10 bg-slate-100 dark:bg-slate-600 rounded-lg sm:rounded-xl flex items-center justify-center">
                          <MapPin className="w-5 h-5 text-slate-600 dark:text-slate-400" />
                        </div>
                        <p className="text-sm font-medium text-slate-500 dark:text-slate-400">Location</p>
                      </div>
                      <p className="font-medium text-slate-900 dark:text-slate-100 text-base sm:text-lg">{internship.location?.city}, {internship.location?.country}</p>
                    </div>
                  </div>

                  {/* Work Type */}
                  <div className="group">
                    <div className="p-5 bg-slate-50 dark:bg-slate-700/30 rounded-xl sm:rounded-2xl border border-slate-200 dark:border-slate-600/50 hover:shadow-lg transition-all duration-300 hover:scale-[1.02]">
                      <div className="flex items-center space-x-3 mb-3">
                        <div className="w-9 h-9 sm:w-10 sm:h-10 bg-slate-100 dark:bg-slate-600 rounded-lg sm:rounded-xl flex items-center justify-center">
                          <Building2 className="w-5 h-5 text-slate-600 dark:text-slate-400" />
                        </div>
                        <p className="text-sm font-medium text-slate-500 dark:text-slate-400">Work Type</p>
                      </div>
                      <p className="font-medium text-slate-900 dark:text-slate-100 text-base sm:text-lg">{internship.workPlaceType}</p>
                    </div>
                  </div>

                  {/* Working Hours */}
                  <div className="group">
                    <div className="p-5 bg-slate-50 dark:bg-slate-700/30 rounded-xl sm:rounded-2xl border border-slate-200 dark:border-slate-600/50 hover:shadow-lg transition-all duration-300 hover:scale-[1.02]">
                      <div className="flex items-center space-x-3 mb-3">
                        <div className="w-9 h-9 sm:w-10 sm:h-10 bg-slate-100 dark:bg-slate-600 rounded-lg sm:rounded-xl flex items-center justify-center">
                          <Clock className="w-5 h-5 text-slate-600 dark:text-slate-400" />
                        </div>
                        <p className="text-sm font-medium text-slate-500 dark:text-slate-400">Working Hours</p>
                      </div>
                      <p className="font-medium text-slate-900 dark:text-slate-100 text-base sm:text-lg">{internship.workingHours}</p>
                    </div>
                  </div>

                  {/* Deadline */}
                  <div className="group">
                    <div className="p-5 bg-slate-50 dark:bg-slate-700/30 rounded-xl sm:rounded-2xl border border-slate-200 dark:border-slate-600/50 hover:shadow-lg transition-all duration-300 hover:scale-[1.02]">
                      <div className="flex items-center space-x-3 mb-3">
                        <div className="w-9 h-9 sm:w-10 sm:h-10 bg-slate-100 dark:bg-slate-600 rounded-lg sm:rounded-xl flex items-center justify-center">
                          <Calendar className="w-5 h-5 text-slate-600 dark:text-slate-400" />
                        </div>
                        <p className="text-sm font-medium text-slate-500 dark:text-slate-400">Application Deadline</p>
                      </div>
                      <p className="font-medium text-slate-900 dark:text-slate-100 text-base sm:text-lg">{formatDate(internship.applicationDeadline)}</p>
                    </div>
                  </div>

                  {/* Working Days - NEW */}
                  {internship.workingDays?.length > 0 && (
                    <div className="group">
                      <div className="p-5 bg-slate-50 dark:bg-slate-700/30 rounded-xl sm:rounded-2xl border border-slate-200 dark:border-slate-600/50 hover:shadow-lg transition-all duration-300 hover:scale-[1.02]">
                        <div className="flex items-center space-x-3 mb-3">
                          <div className="w-9 h-9 sm:w-10 sm:h-10 bg-slate-100 dark:bg-slate-600 rounded-lg sm:rounded-xl flex items-center justify-center">
                            <Calendar className="w-5 h-5 text-slate-600 dark:text-slate-400" />
                          </div>
                          <p className="text-sm font-medium text-slate-500 dark:text-slate-400">Working Days</p>
                        </div>
                        <p className="font-medium text-slate-900 dark:text-slate-100 text-base sm:text-lg">
                          {internship.workingDays.join(', ')}
                        </p>
                      </div>
                    </div>
                  )}

                  {/* Preferred Languages - NEW */}
                  {internship.preferredLanguages?.length > 0 && (
                    <div className="group">
                      <div className="p-5 bg-slate-50 dark:bg-slate-700/30 rounded-xl sm:rounded-2xl border border-slate-200 dark:border-slate-600/50 hover:shadow-lg transition-all duration-300 hover:scale-[1.02]">
                        <div className="flex items-center space-x-3 mb-3">
                          <div className="w-9 h-9 sm:w-10 sm:h-10 bg-slate-100 dark:bg-slate-600 rounded-lg sm:rounded-xl flex items-center justify-center">
                            <Languages className="w-5 h-5 text-slate-600 dark:text-slate-400" />
                          </div>
                          <p className="text-sm font-medium text-slate-500 dark:text-slate-400">Preferred Languages</p>
                        </div>
                        <p className="font-medium text-slate-900 dark:text-slate-100 text-base sm:text-lg">
                          {internship.preferredLanguages.join(', ')}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Skills Required */}
              {internship.skills?.length > 0 && (
                <div className="bg-white dark:bg-slate-800/60 backdrop-blur-xl rounded-2xl sm:rounded-3xl p-6 sm:p-8 shadow-sm border border-slate-200 dark:border-slate-700/50">
                  <h2 className="text-xl sm:text-2xl font-light text-slate-900 dark:text-slate-100 mb-6 sm:mb-8 flex items-center">
                    <Award className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-slate-600 dark:text-slate-400" />
                    Required Skills
                  </h2>
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                    {internship.skills.map((skill: any, index: number) => (
                      <div key={index} className="p-4 bg-slate-50 dark:bg-slate-700/30 rounded-xl border border-slate-200 dark:border-slate-600/50 hover:shadow-md transition-all duration-200">
                        <div className="flex items-center justify-between mb-2">
                          <p className="font-medium text-slate-900 dark:text-slate-100">{skill.skill}</p>
                          <span className={`px-2 py-1 text-xs rounded-full font-medium border ${getSkillLevelColor(skill.level)}`}>
                            {skill.level}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Qualifications */}
              {internship.qualification?.length > 0 && (
                <div className="bg-white dark:bg-slate-800/60 backdrop-blur-xl rounded-2xl sm:rounded-3xl p-6 sm:p-8 shadow-sm border border-slate-200 dark:border-slate-700/50">
                  <h2 className="text-xl sm:text-2xl font-light text-slate-900 dark:text-slate-100 mb-6 sm:mb-8 flex items-center">
                    <Users className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-slate-600 dark:text-slate-400" />
                    Qualifications
                  </h2>
                  <div className="space-y-4 md:space-y-0">
                    {internship.qualification.map((qual: string, index: number) => (
                      <div key={index} className="flex gap-3 list-disc pl-5 space-y-3 text-slate-700 dark:text-slate-300">
                                              <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                                <CheckCircle className="w-3 h-3 text-blue-600 dark:text-blue-400" />
                                              </div>
                                              <p className="text-slate-700 dark:text-slate-300 leading-relaxed">{qual}</p>
                                            </div> 
                    ))}
                  </div>
                </div>
              )}

              {/* Eligibility Criteria */}
              {internship.eligibilityCriteria?.length > 0 && (
                <div className="bg-white dark:bg-slate-800/60 backdrop-blur-xl rounded-2xl sm:rounded-3xl p-6 sm:p-8 shadow-sm border border-slate-200 dark:border-slate-700/50">
                  <h2 className="text-xl sm:text-2xl font-light text-slate-900 dark:text-slate-100 mb-6 sm:mb-8 flex items-center">
                    <CheckCircle className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-slate-600 dark:text-slate-400" />
                    Eligibility Criteria
                  </h2>
                  <div className="space-y-4 md:space-y-0">
                    {internship.eligibilityCriteria.map((criteria: string, index: number) => (
                     <div key={index} className="flex gap-3 list-disc pl-5 space-y-3 text-slate-700 dark:text-slate-300">
                                             <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                               <CheckCircle className="w-3 h-3 text-blue-600 dark:text-blue-400" />
                                             </div>
                                             <p className="text-slate-700 dark:text-slate-300 leading-relaxed">{criteria}</p>
                                           </div> 
                    ))}
                  </div>
                </div>
              )}

              {/* Responsibilities */}
              {internship.responsibilities?.length > 0 && (
                <div className="bg-white dark:bg-slate-800/60 backdrop-blur-xl rounded-2xl sm:rounded-3xl p-6 sm:p-8 shadow-sm border border-slate-200 dark:border-slate-700/50">
                  <h2 className="text-xl sm:text-2xl font-light text-slate-900 dark:text-slate-100 mb-6 sm:mb-8 flex items-center">
                    <Target className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-slate-600 dark:text-slate-400" />
                    Key Responsibilities
                  </h2>
                  <div className="space-y-4 md:space-y-0">
                    {internship.responsibilities.map((responsibility: any, index: number) => (
                     <div key={index} className="flex gap-3 list-disc pl-5 space-y-3 text-slate-700 dark:text-slate-300">
                                             <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                               <CheckCircle className="w-3 h-3 text-blue-600 dark:text-blue-400" />
                                             </div>
                                             <p className="text-slate-700 dark:text-slate-300 leading-relaxed">{responsibility.title}</p>
                                           </div> 
                    ))}
                  </div>
                </div>
              )}

              {/* Selection Process */}
              {internship.selectionProcess?.length > 0 && (
                <div className="bg-white dark:bg-slate-800/60 backdrop-blur-xl rounded-2xl sm:rounded-3xl p-6 sm:p-8 shadow-sm border border-slate-200 dark:border-slate-700/50">
                  <h2 className="text-xl sm:text-2xl font-light text-slate-900 dark:text-slate-100 mb-6 sm:mb-8 flex items-center">
                    <FileText className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-slate-600 dark:text-slate-400" />
                    Selection Process
                  </h2>
                  <div className="space-y-4">
                    {internship.selectionProcess.map((step: any, index: number) => (
                      <div key={index} className="flex items-start space-x-4 p-4 bg-slate-50 dark:bg-slate-700/30 rounded-xl border border-slate-200 dark:border-slate-600/50">
                        <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center flex-shrink-0">
                          <span className="text-blue-600 dark:text-blue-400 font-semibold text-sm">{index + 1}</span>
                        </div>
                        <div>
                          <h3 className="font-medium text-slate-900 dark:text-slate-100 mb-1">{step.stage}</h3>
                          <p className="text-slate-600 dark:text-slate-400 text-sm">{step.description}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Interview Rounds */}
              {internship.interviews?.length > 0 && (
                <div className="bg-white dark:bg-slate-800/60 backdrop-blur-xl rounded-2xl sm:rounded-3xl p-6 sm:p-8 shadow-sm border border-slate-200 dark:border-slate-700/50">
                  <h2 className="text-xl sm:text-2xl font-light text-slate-900 dark:text-slate-100 mb-6 sm:mb-8 flex items-center">
                    <MessageSquare className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-slate-600 dark:text-slate-400" />
                    Interview Rounds
                  </h2>
                  <div className="space-y-4">
                    {internship.interviews.map((interview: any, index: number) => (
                      <div key={index} className="flex items-start space-x-4 p-4 bg-slate-50 dark:bg-slate-700/30 rounded-xl border border-slate-200 dark:border-slate-600/50">
                        <div className="w-8 h-8 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center flex-shrink-0">
                          <span className="text-purple-600 dark:text-purple-400 font-semibold text-sm">{index + 1}</span>
                        </div>
                        <div>
                          <h3 className="font-medium text-slate-900 dark:text-slate-100 mb-1">{interview.roundTitle}</h3>
                          <p className="text-slate-600 dark:text-slate-400 text-sm">{interview.description}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

             
            </div>

            {/* Right Column - Sidebar */}
            <div className="md:col-span-1 space-y-6 sm:space-y-8">
              {/* Apply Now CTA */}
              <div className="bg-white dark:bg-slate-800/60 backdrop-blur-xl rounded-2xl sm:rounded-3xl p-6 shadow-sm border border-slate-200 dark:border-slate-700/50">
                <button className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-4 px-6 rounded-xl transition-all duration-200 flex items-center justify-center space-x-2 group">
                  <span>Apply Now</span>
                  <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
                </button>
              </div>

              {/* Company Information */}
              <div className="bg-white dark:bg-slate-800/60 backdrop-blur-xl rounded-2xl sm:rounded-3xl p-6 shadow-sm border border-slate-200 dark:border-slate-700/50">
                <h2 className="text-xl sm:text-2xl font-light text-slate-900 dark:text-slate-100 mb-6 sm:mb-8 flex items-center">
                  <Building2 className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-slate-600 dark:text-slate-400" />
                  Company Information
                </h2>
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-6 h-6 bg-slate-100 dark:bg-slate-600 rounded-full flex items-center justify-center flex-shrink-0">
                      <User className="w-3 h-3 text-slate-600 dark:text-slate-400" />
                    </div>
                    <p className="text-slate-700 dark:text-slate-300 leading-relaxed">{internship.company.name}</p>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-6 h-6 bg-slate-100 dark:bg-slate-600 rounded-full flex items-center justify-center flex-shrink-0">
                      <Briefcase className="w-3 h-3 text-slate-600 dark:text-slate-400" />
                    </div>
                    <p className="text-slate-700 dark:text-slate-300 leading-relaxed">Company Size: {internship.company.companySize}</p>
                  </div>
                  {internship.company.email && (
                    <div className="flex items-center space-x-3">
                      <div className="w-6 h-6 bg-slate-100 dark:bg-slate-600 rounded-full flex items-center justify-center flex-shrink-0">
                        <Mail className="w-3 h-3 text-slate-600 dark:text-slate-400" />
                      </div>
                      <a href={`mailto:${internship.company.email}`} className="text-blue-600 dark:text-blue-400 hover:underline">
                        {internship.company.email}
                      </a>
                    </div>
                  )}
                  {internship.company.phone && (
                    <div className="flex items-center space-x-3">
                      <div className="w-6 h-6 bg-slate-100 dark:bg-slate-600 rounded-full flex items-center justify-center flex-shrink-0">
                        <Phone className="w-3 h-3 text-slate-600 dark:text-slate-400" />
                      </div>
                      <a href={`tel:${internship.company.phone}`} className="text-blue-600 dark:text-blue-400 hover:underline">
                        {internship.company.phone}
                      </a>
                    </div>
                  )}
                  {internship.company.website && (
                    <div className="flex items-center space-x-3">
                      <div className="w-6 h-6 bg-slate-100 dark:bg-slate-600 rounded-full flex items-center justify-center flex-shrink-0">
                        <Globe className="w-3 h-3 text-slate-600 dark:text-slate-400" />
                      </div>
                      <a href={internship.company.website} target="_blank" rel="noopener noreferrer" className="text-blue-600 dark:text-blue-400 hover:underline">
                        Visit Website
                      </a>
                    </div>
                  )}
                </div>
              </div>

              {/* Custom Fields / Additional Details - NEW */}
              {/* {(internship.customFields && Object.keys(internship.customFields).length > 0) && (
                <div className="bg-white dark:bg-slate-800/60 backdrop-blur-xl rounded-2xl sm:rounded-3xl p-6 shadow-sm border border-slate-200 dark:border-slate-700/50">
                  <h2 className="text-xl sm:text-2xl font-light text-slate-900 dark:text-slate-100 mb-6 sm:mb-8 flex items-center">
                    <Edit className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-slate-600 dark:text-slate-400" />
                    Additional Details
                  </h2>
                  <div className="space-y-3">
                    {Object.entries(internship.customFields).map(([key, value]) => (
                      <div key={key} className="flex items-center justify-between">
                        <p className="text-slate-700 dark:text-slate-300 capitalize">{key.replace(/_/g, ' ')}:</p>
                        <p className="font-medium text-slate-900 dark:text-slate-100">{value}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )} */}

               {/* Perks - NEW section, assuming it's useful to highlight */}
              {internship.perks?.length > 0 && (
                <div className="bg-white dark:bg-slate-800/60 backdrop-blur-xl rounded-2xl sm:rounded-3xl p-6 sm:p-8 shadow-sm border border-slate-200 dark:border-slate-700/50">
                  <h2 className="text-xl sm:text-2xl font-light text-slate-900 dark:text-slate-100 mb-6 sm:mb-8 flex items-center">
                    <Star className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-slate-600 dark:text-slate-400" />
                    Perks & Benefits
                  </h2>
                  <ul className="space-y-4">
                    {internship.perks.map((perk: any, index : number) => (
                      <div key={index} className="flex gap-3 list-disc  space-y-3 text-slate-700 dark:text-slate-300">
                        <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                          <CheckCircle className="w-3 h-3 text-blue-600 dark:text-blue-400" />
                        </div>
                        <p className="text-slate-700 dark:text-slate-300 leading-relaxed">{perk.title}</p>
                      </div> 
                    ))}
                  </ul>
                </div>
              )}


              {/* Application Questions Note - NEW (Optional, to set expectations) */}
              {internship.questions?.length > 0 && (
                <div className="bg-white dark:bg-slate-800/60 backdrop-blur-xl rounded-2xl sm:rounded-3xl p-6 shadow-sm border border-slate-200 dark:border-slate-700/50">
                  <h2 className="text-xl sm:text-2xl font-light text-slate-900 dark:text-slate-100 mb-6 sm:mb-8 flex items-center">
                    <FileText className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-slate-600 dark:text-slate-400" />
                    Application Process Note
                  </h2>
                  <p className="text-slate-700 dark:text-slate-300">
                    This application includes a few questions regarding your interest and qualifications.
                    {/* {internship.questions.some(q => q.isRequired) && " Some questions are required."} */}
                  </p>
                </div>
              )}

            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InternshipDetailsPage;