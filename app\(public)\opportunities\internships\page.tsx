// app/internships/page.tsx
import React from 'react';
import { Metadata } from 'next';
import HeroComponent from '@/components/sections/HeroComponent';
import WhyChooseUs from '@/components/sections/WhyChooseUs';
import { Heading } from '@/components/sections/Heading';
import { Paragraph } from '@/components/sections/paragraph';
import { internshipsFeature } from '@/constant/opportunities';
import InternshipCard from '@/components/ui/internshipCard'
import { getAllInternships } from '@/store/features/internshipsApi';


// Force dynamic rendering (server-side) to avoid static generation errors
export const dynamic = 'force-dynamic';


export const metadata: Metadata = {
  title: 'Internships at Sudha Software Solutions - Start Your Journey',
  description:
    'Explore exciting internship opportunities at Sudha Software Solutions. Kickstart your career with hands-on experience in digital innovation and growth.',
  keywords:
    'Internships, Sudha Software Solutions, Digital Innovation, Intern, Career, Experience',
  openGraph: {
    title: 'Internships at Sudha Software Solutions',
    description:
      'Discover internship opportunities at Sudha Software Solutions and kickstart your journey in digital innovation.',
    images: ['/assets/banner/internships-banner.jpg'],
    url: 'https://careers.sudhasoftwaresolutions.com/internships',
    type: 'website',
  },
  twitter: {
    title: 'Internships at Sudha Software Solutions',
    description:
      'Join our internship program and gain invaluable experience at Sudha Software Solutions. Apply today to start your journey.',
    images: ['/assets/banner/internships-banner.jpg'],
    card: 'summary_large_image',
    site: '@sudha_software_solutions',
    creator: '@sudha_software_solutions',
  },
};

const getAllInternshipsData = async() => {     
  try{
    const response = await getAllInternships();
    if(!response.data?.success){
      throw new Error(response.data?.error || 'Failed to fetch Internships');
    }
    return response?.data?.data?.internships || [];
  }
  catch(error: any){
    throw new Error(
       error.response?.data?.error ||
      error.message ||
      'Failed to fetch Internships'
    )
  }
  }


export default async function InternshipsPage() {

  const internships = await getAllInternshipsData();
  // console.log(internships)

  return (
    <>
      <HeroComponent
        heading={
          <Heading>
            Launch Your Career with <br />
            <span className="text-blue-600"> Our Internships</span>
          </Heading>
        }
        description={
          <Paragraph>
            At Sudha Software Solutions, our internship program offers you the
            opportunity to learn, grow, and gain hands-on experience in a
            dynamic digital environment.
          </Paragraph>
        }
        heroImageSrc="/images/internships.png"
        heroImageWidth={500}
        heroImageHeight={500}
        heroImageClassName="rounded-xl"
      />




    <div className='mx-[4%] xl:mx-[10%] py-24'>
          <Heading className='mb-10'>Unlock your  <span className="text-blue-600">Career opportunities</span></Heading>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {internships.map((internship : any) => (              
              <InternshipCard key={internship._id} internship={internship}/>              
            ))
            }
          </div>
        </div>

      <WhyChooseUs
        heading={
          <Heading>
            🌟 Why Choose Our
            <span className="text-blue-600"> Internships</span> ?
          </Heading>
        }
        iconSrc="/images/archery2.png"
        imageSrc="/images/why-choose-us.jpg"
        features={internshipsFeature}
      />
    </>
  );
}
