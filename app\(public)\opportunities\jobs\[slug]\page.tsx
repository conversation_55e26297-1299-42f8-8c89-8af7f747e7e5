import React from 'react';
import { 
  Briefcase, 
  Calendar, 
  MapPin, 
  Users, 
  Clock, 
  DollarSign,
  Target,
  Award,
  CheckCircle,
  Star,
  TrendingUp,
  Building2,
  Globe,
  Edit,
  FileText,
  ArrowRight,
  Phone,
  Mail
} from 'lucide-react';
import { baseUrl, getJobBySlug } from '@/store/features/jobsApi';
import { Metadata } from 'next';
import JobApplyButton from '@/components/job/JobApplyButton';

// Force dynamic (server-side) rendering
export const dynamic = 'force-dynamic';

type Props = {
  params: Promise<{ slug: string }>
}
 
export async function generateMetadata(
  { params}: Props,
): Promise<Metadata> {
  // read route params
  const { slug } = await params

  try{
    const response = await getJobBySlug(slug)
    const jobs = response?.data?.data || {};
    // console.log("params jobs ", jobs)

  return {
    title: `${jobs.title} | Jobs Details`,
    description: jobs.description,
    openGraph: {
      title: `${jobs.title} | Jobs Details`,
      description: jobs.description,
      images: [jobs.logo],
      url: `${baseUrl}/companies/${slug}`,
      type: 'website',
    },
    twitter: {
      title: `${jobs.title} | Jobs Details`,
      description: jobs.description,
      images: [jobs.logo],
      card: 'summary_large_image',
    },
  };
}
  catch(error){
    console.error("Error fetching jobs metadata", error);
    return {
      title: 'Jobs Details',
      description: 'Jobs Details',
    };
  }
  }



const JobDetailsPage = async ({
  params,
}: {
  params: Promise<{ slug: string }>
}) => {
  const { slug } = await params
  const response = await getJobBySlug(slug)
  const job = response?.data?.data || {};

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getSkillLevelColor = (level: string) => {
    switch (level.toLowerCase()) {
      case 'beginner':
        return 'bg-green-50 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800/30';
      case 'intermediate':
        return 'bg-amber-50 text-amber-700 border-amber-200 dark:bg-amber-900/20 dark:text-amber-400 dark:border-amber-800/30';
      case 'advanced':
        return 'bg-red-50 text-red-700 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800/30';
      default:
        return 'bg-slate-50 text-slate-700 border-slate-200 dark:bg-slate-900/20 dark:text-slate-400 dark:border-slate-800/30';
    }
  };

  return (
    <div className="min-h-screen transition-all duration-500 bg-gray-100 dark:bg-slate-900">
      <div className="max-w-7xl px-5 mx-auto min-h-screen pt-10">
        {/* Hero Section */}
        <div className="relative mb-20">
          <div className="rounded-4xl border absolute inset-0 bg-gradient-to-br bg-white dark:from-slate-900 dark:via-slate-800 dark:to-slate-900"></div>
          <div className="relative px-6 sm:px-8 pt-10 pb-6 sm:pt-12 sm:pb-8">
            <div className="max-w-6xl mx-auto">
              <div className="flex items-start justify-between flex-col sm:flex-row sm:items-center mb-6 sm:mb-8">
                <div className="flex items-center space-x-4 sm:space-x-6 mb-4 sm:mb-0">
                  <div className="relative flex-shrink-0">
                    <div className="w-20 h-20 sm:w-24 sm:h-24 bg-gradient-to-br from-slate-100 to-slate-200 dark:from-slate-700 dark:to-slate-800 rounded-2xl sm:rounded-3xl flex items-center justify-center shadow-inner border border-slate-200 dark:border-slate-600">
                      <Briefcase className="w-10 h-10 sm:w-12 sm:h-12 text-slate-700 dark:text-slate-300" />
                    </div>
                    <div className="absolute -top-1 -right-1 w-5 h-5 sm:w-6 sm:h-6 bg-blue-500 rounded-full flex items-center justify-center">
                      <Star className="w-3 h-3 text-white" />
                    </div>
                  </div>
                  <div className='pr-4'>
                    <h1 className="text-3xl sm:text-4xl lg:text-5xl font-light text-slate-900 dark:text-slate-100 mb-1 sm:mb-2 tracking-tight leading-tight">
                      {job.title}
                    </h1>
                    <p className="text-base sm:text-lg text-slate-600 dark:text-slate-400 mb-0.5 sm:mb-1 font-light">
                      {job.company?.name}
                    </p>
                    <p className="text-xs sm:text-sm text-slate-600 dark:text-slate-400 mb-0.5 sm:mb-1 font-light">
                      {job.location?.city}, {job.location?.state}, {job.location?.country}
                    </p>
                    <p className="text-slate-500 dark:text-slate-500 font-mono text-xs sm:text-sm tracking-wide">
                      {job.mode} • {job.workPlaceType}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2 sm:space-x-3 flex-wrap justify-end">
                  <span className="px-3 py-1.5 sm:px-4 sm:py-2 bg-emerald-50 text-emerald-700 border border-emerald-200 rounded-sm text-xs sm:text-sm font-medium dark:bg-emerald-900/20 dark:text-emerald-400 dark:border-emerald-800/30">
                    {job.openings} Opening{job.openings > 1 ? 's' : ''}
                  </span>
                  <span className="px-3 py-1.5 sm:px-4 sm:py-2 bg-blue-50 text-blue-700 border border-blue-200 rounded-sm text-xs sm:text-sm font-medium dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800/30">
                    {job.salaryRange}
                  </span>
                </div>
              </div>
              <p className="text-sm sm:text-base text-slate-700 dark:text-slate-300 leading-relaxed font-light max-w-3xl">
                {job.description}
              </p>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="pb-10 sm:pb-12">
          <div className="grid grid-cols-1 md:grid-cols-3 xl:grid-cols-4 gap-6 sm:gap-8 -mt-10 sm:-mt-12">
            {/* Left Column - Job Details */}
            <div className="md:col-span-2 xl:col-span-3 space-y-6 sm:space-y-8">
              {/* Job Overview */}
              <div className="bg-white dark:bg-slate-800/60 backdrop-blur-xl rounded-2xl sm:rounded-3xl p-6 sm:p-8 shadow-sm border border-slate-200 dark:border-slate-700/50">
                <div className="flex items-center justify-between mb-6 sm:mb-8">
                  <h2 className="text-xl sm:text-2xl font-light text-slate-900 dark:text-slate-100 flex items-center">
                    <Briefcase className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-slate-600 dark:text-slate-400" />
                    Job Overview
                  </h2>
                  <TrendingUp className="w-4 h-4 sm:w-5 sm:h-5 text-slate-500 dark:text-slate-400" />
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
                  {/* Salary */}
                  <div className="group">
                    <div className="p-5 bg-slate-50 dark:bg-slate-700/30 rounded-xl sm:rounded-2xl border border-slate-200 dark:border-slate-600/50 hover:shadow-lg transition-all duration-300 hover:scale-[1.02]">
                      <div className="flex items-center space-x-3 mb-3">
                        <div className="w-9 h-9 sm:w-10 sm:h-10 bg-slate-100 dark:bg-slate-600 rounded-lg sm:rounded-xl flex items-center justify-center">
                          <DollarSign className="w-5 h-5 text-slate-600 dark:text-slate-400" />
                        </div>
                        <p className="text-sm font-medium text-slate-500 dark:text-slate-400">Salary</p>
                      </div>
                      <p className="font-medium text-slate-900 dark:text-slate-100 text-base sm:text-lg">{job.salaryRange}</p>
                    </div>
                  </div>

                  {/* Location */}
                  <div className="group">
                    <div className="p-5 bg-slate-50 dark:bg-slate-700/30 rounded-xl sm:rounded-2xl border border-slate-200 dark:border-slate-600/50 hover:shadow-lg transition-all duration-300 hover:scale-[1.02]">
                      <div className="flex items-center space-x-3 mb-3">
                        <div className="w-9 h-9 sm:w-10 sm:h-10 bg-slate-100 dark:bg-slate-600 rounded-lg sm:rounded-xl flex items-center justify-center">
                          <MapPin className="w-5 h-5 text-slate-600 dark:text-slate-400" />
                        </div>
                        <p className="text-sm font-medium text-slate-500 dark:text-slate-400">Location</p>
                      </div>
                      <p className="font-medium text-slate-900 dark:text-slate-100 text-base sm:text-lg">{job.location?.city}, {job.location?.country}</p>
                    </div>
                  </div>

                  {/* Work Type */}
                  <div className="group">
                    <div className="p-5 bg-slate-50 dark:bg-slate-700/30 rounded-xl sm:rounded-2xl border border-slate-200 dark:border-slate-600/50 hover:shadow-lg transition-all duration-300 hover:scale-[1.02]">
                      <div className="flex items-center space-x-3 mb-3">
                        <div className="w-9 h-9 sm:w-10 sm:h-10 bg-slate-100 dark:bg-slate-600 rounded-lg sm:rounded-xl flex items-center justify-center">
                          <Building2 className="w-5 h-5 text-slate-600 dark:text-slate-400" />
                        </div>
                        <p className="text-sm font-medium text-slate-500 dark:text-slate-400">Work Type</p>
                      </div>
                      <p className="font-medium text-slate-900 dark:text-slate-100 text-base sm:text-lg">{job.workPlaceType}</p>
                    </div>
                  </div>

                  {/* Working Hours */}
                  <div className="group">
                    <div className="p-5 bg-slate-50 dark:bg-slate-700/30 rounded-xl sm:rounded-2xl border border-slate-200 dark:border-slate-600/50 hover:shadow-lg transition-all duration-300 hover:scale-[1.02]">
                      <div className="flex items-center space-x-3 mb-3">
                        <div className="w-9 h-9 sm:w-10 sm:h-10 bg-slate-100 dark:bg-slate-600 rounded-lg sm:rounded-xl flex items-center justify-center">
                          <Clock className="w-5 h-5 text-slate-600 dark:text-slate-400" />
                        </div>
                        <p className="text-sm font-medium text-slate-500 dark:text-slate-400">Working Hours</p>
                      </div>
                      <p className="font-medium text-slate-900 dark:text-slate-100 text-base sm:text-lg">{job.workingHours}</p>
                    </div>
                  </div>

                  {/* Deadline */}
                  <div className="group sm:col-span-2">
                    <div className="p-5 bg-slate-50 dark:bg-slate-700/30 rounded-xl sm:rounded-2xl border border-slate-200 dark:border-slate-600/50 hover:shadow-lg transition-all duration-300 hover:scale-[1.02]">
                      <div className="flex items-center space-x-3 mb-3">
                        <div className="w-9 h-9 sm:w-10 sm:h-10 bg-slate-100 dark:bg-slate-600 rounded-lg sm:rounded-xl flex items-center justify-center">
                          <Calendar className="w-5 h-5 text-slate-600 dark:text-slate-400" />
                        </div>
                        <p className="text-sm font-medium text-slate-500 dark:text-slate-400">Application Deadline</p>
                      </div>
                      <p className="font-medium text-slate-900 dark:text-slate-100 text-base sm:text-lg">{formatDate(job.applicationDeadline)}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Skills Required */}
              {job.skills?.length > 0 && (
                <div className="bg-white dark:bg-slate-800/60 backdrop-blur-xl rounded-2xl sm:rounded-3xl p-6 sm:p-8 shadow-sm border border-slate-200 dark:border-slate-700/50">
                  <h2 className="text-xl sm:text-2xl font-light text-slate-900 dark:text-slate-100 mb-6 sm:mb-8 flex items-center">
                    <Award className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-slate-600 dark:text-slate-400" />
                    Required Skills
                  </h2>
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                    {job.skills.map((skill: any, index: number) => (
                      <div key={index} className="p-4 bg-slate-50 dark:bg-slate-700/30 rounded-xl border border-slate-200 dark:border-slate-600/50 hover:shadow-md transition-all duration-200">
                        <div className="flex items-center justify-between mb-2">
                          <p className="font-medium text-slate-900 dark:text-slate-100">{skill.skill}</p>
                          <span className={`px-2 py-1 text-xs rounded-full font-medium border ${getSkillLevelColor(skill.level)}`}>
                            {skill.level}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* General Requirements */}
              {job.qualification?.length > 0 && (
                <div className="bg-white dark:bg-slate-800/60 backdrop-blur-xl rounded-2xl sm:rounded-3xl p-6 sm:p-8 shadow-sm border border-slate-200 dark:border-slate-700/50">
                  <h2 className="text-xl sm:text-2xl font-light text-slate-900 dark:text-slate-100 mb-6 sm:mb-8 flex items-center">
                    <Users className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-slate-600 dark:text-slate-400" />
                    Requirements
                  </h2>
                  <div className="space-y-4 md:space-y-0">
                    {job.qualification.map((req: string, index: number) => (
                      <div key={index} className="flex gap-3 list-disc pl-5 space-y-3 text-slate-700 dark:text-slate-300">
                        <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                          <CheckCircle className="w-3 h-3 text-blue-600 dark:text-blue-400" />
                        </div>
                        <p className="text-slate-700 dark:text-slate-300 leading-relaxed">{req}</p>
                      
                      </div> 
                    ))}
                  </div>
                </div>
              )}

              {/* Responsibilities */}
              {job.responsibilities?.length > 0 && (
                <div className="bg-white dark:bg-slate-800/60 backdrop-blur-xl rounded-2xl sm:rounded-3xl p-6 sm:p-8 shadow-sm border border-slate-200 dark:border-slate-700/50">
                  <h2 className="text-xl sm:text-2xl font-light text-slate-900 dark:text-slate-100 mb-6 sm:mb-8 flex items-center">
                    <Target className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-slate-600 dark:text-slate-400" />
                    Key Responsibilities
                  </h2>
                  <div className="space-y-4 md:space-y-0">
                    {job.responsibilities.map((responsibility: any, index: number) => (
                      <div key={index} className="flex gap-3 list-disc pl-5 space-y-3 text-slate-700 dark:text-slate-300">
                        <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                          <CheckCircle className="w-3 h-3 text-blue-600 dark:text-blue-400" />
                        </div>
                        <p className="text-slate-700 dark:text-slate-300 leading-relaxed">{responsibility.title}</p>
                      </div> 
                    ))}
                  </div>
                </div>
              )}

              {/* Selection Process */}
              {job.selectionProcess?.length > 0 && (
                <div className="bg-white dark:bg-slate-800/60 backdrop-blur-xl rounded-2xl sm:rounded-3xl p-6 sm:p-8 shadow-sm border border-slate-200 dark:border-slate-700/50">
                  <h2 className="text-xl sm:text-2xl font-light text-slate-900 dark:text-slate-100 mb-6 sm:mb-8 flex items-center">
                    <FileText className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-slate-600 dark:text-slate-400" />
                    Selection Process
                  </h2>
                  <div className="space-y-4">
                    {job.selectionProcess.map((step: any, index: number) => (
                      <div key={index} className="flex items-start space-x-4 p-4 bg-slate-50 dark:bg-slate-700/30 rounded-xl border border-slate-200 dark:border-slate-600/50">
                        <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center flex-shrink-0">
                          <span className="text-blue-600 dark:text-blue-400 font-semibold text-sm">{index + 1}</span>
                        </div>
                        <div>
                          <h3 className="font-medium text-slate-900 dark:text-slate-100 mb-1">{step.stage}</h3>
                          <p className="text-slate-600 dark:text-slate-400 text-sm">{step.description}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Right Column - Sidebar */}
            <div className="md:col-span-1 space-y-6 sm:space-y-8">
              {/* Apply Now CTA */}
              <div className="bg-white dark:bg-slate-800/60 backdrop-blur-xl rounded-2xl sm:rounded-3xl p-6 shadow-sm border border-slate-200 dark:border-slate-700/50">
                <button className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-4 px-6 rounded-xl transition-all duration-200 flex items-center justify-center space-x-2 group">
                  <span>Apply Now</span>
                  <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
                </button>
              </div>

              {/* Perks & Benefits */}
              {job.perks?.length > 0 && (
                <div className="bg-white dark:bg-slate-800/60 backdrop-blur-xl rounded-2xl sm:rounded-3xl p-6 shadow-sm border border-slate-200 dark:border-slate-700/50">
                  <h2 className="text-xl font-light text-slate-900 dark:text-slate-100 mb-6">Perks & Benefits</h2>
                  <div className="space-y-3">
                    {job.perks.map((perk: any, index: number) => (
                      <div key={index} className="flex items-center space-x-3 p-3 bg-slate-50 dark:bg-slate-700/30 rounded-xl border border-slate-200 dark:border-slate-600/50">
                        <div className="w-8 h-8 bg-emerald-100 dark:bg-emerald-900/30 rounded-lg flex items-center justify-center flex-shrink-0">
                          <Star className="w-4 h-4 text-emerald-600 dark:text-emerald-400" />
                        </div>
                        <p className="text-sm text-slate-700 dark:text-slate-300">{perk.title}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Quick Stats */}
              <div className="bg-white dark:bg-slate-800/60 backdrop-blur-xl rounded-2xl sm:rounded-3xl p-6 shadow-sm border border-slate-200 dark:border-slate-700/50">
                <h2 className="text-xl font-light text-slate-900 dark:text-slate-100 mb-6">Quick Stats</h2>
                <div className="space-y-4">
                  <div className="flex justify-between items-center p-3 bg-slate-50 dark:bg-slate-700/30 rounded-xl">
                    <span className="text-slate-600 dark:text-slate-400 font-medium">Openings</span>
                    <span className="font-semibold text-xl text-slate-900 dark:text-slate-100">{job.openings}</span>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-slate-50 dark:bg-slate-700/30 rounded-xl">
                    <span className="text-slate-600 dark:text-slate-400 font-medium">Team Size</span>
                    <span className="font-semibold text-xl text-slate-900 dark:text-slate-100">{job.company?.teamSize || 'N/A'}</span>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-slate-50 dark:bg-slate-700/30 rounded-xl">
                    <span className="text-slate-600 dark:text-slate-400 font-medium">Skills</span>
                    <span className="font-semibold text-xl text-slate-900 dark:text-slate-100">{job.skills?.length || 0}</span>
                  </div>
                </div>
              </div>

              {/* Additional Info */}
              <div className="bg-white dark:bg-slate-800/60 backdrop-blur-xl rounded-2xl sm:rounded-3xl p-6 shadow-sm border border-slate-200 dark:border-slate-700/50">
                <h2 className="text-xl font-light text-slate-900 dark:text-slate-100 mb-6 flex items-center">
                  <FileText className="w-5 h-5 mr-2 text-slate-600 dark:text-slate-400" />
                  Additional Info
                </h2>
                <div className="space-y-4">
                  {job.customFields?.expectedStartDate && (
                    <div className="p-3 bg-slate-50 dark:bg-slate-700/30 rounded-xl">
                      <div className="flex items-center space-x-2 mb-1">
                        <Clock className="w-4 h-4 text-slate-500 dark:text-slate-400" />
                        <p className="text-sm text-slate-500 dark:text-slate-400">Expected Start Date</p>
                      </div>
                      <p className="text-sm font-medium text-slate-900 dark:text-slate-100">{job.customFields.expectedStartDate}</p>
                    </div>
                  )}
                  <div className="p-3 bg-slate-50 dark:bg-slate-700/30 rounded-xl">
                    <div className="flex items-center space-x-2 mb-1">
                      <Edit className="w-4 h-4 text-slate-500 dark:text-slate-400" />
                      <p className="text-sm text-slate-500 dark:text-slate-400">Work Mode</p>
                    </div>
                    <p className="text-sm font-medium text-slate-900 dark:text-slate-100">{job.mode}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default JobDetailsPage;