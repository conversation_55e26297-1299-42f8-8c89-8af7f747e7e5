// app/jobs/page.tsx


import React from 'react';
import { Metadata } from 'next';
import HeroComponent from '@/components/sections/HeroComponent';
import WhyChooseUs from '@/components/sections/WhyChooseUs';
import { Heading } from '@/components/sections/Heading';
import { Paragraph } from '@/components/sections/paragraph';
import { jobsFeature } from '@/constant/opportunities';
import JobsCard from '@/components/ui/jobCard';
import { getAllJobs } from '@/store/features/jobsApi';


// Force dynamic rendering (server-side) to avoid static generation errors
export const dynamic = 'force-dynamic';
export const metadata: Metadata = {
  title: 'Our Careers - Transform Your Future at Sudha Software Solutions',
  description:
    'Explore exciting career opportunities across Sudha Software Solutions, including our core business, innovative product companies, and dynamic subsidiaries. Join us in driving digital transformation, collaboration, and growth.',
  keywords:
    'Careers, Jobs, Sudha Software Solutions, Digital Transformation, Innovation, Product Companies, Subsidiaries, Technology Careers',
  openGraph: {
    title: 'Careers at Sudha Software Solutions',
    description:
      'Discover career opportunities at Sudha Software Solutions and its group companies. Be part of a transformative journey in digital innovation and growth.',
    images: [
      'https://careers.sudhasoftwaresolutions.com/assets/banner/careers-banner.jpg',
    ],
    url: 'https://careers.sudhasoftwaresolutions.com/companies',
    type: 'website',
  },
  twitter: {
    title: 'Careers at Sudha Software Solutions',
    description:
      'Join Sudha Software Solutions and its dynamic subsidiaries in shaping the future of digital innovation. Explore rewarding career opportunities today.',
    images: [
      'https://careers.sudhasoftwaresolutions.com/assets/banner/careers-banner.jpg',
    ],
    card: 'summary_large_image',
    site: '@sudha_software_solutions',
    creator: '@sudha_software_solutions',
  },
};


const getAllJobsData = async() => {     
  try{
    const response = await getAllJobs();
    
    if(!response.data?.success){
      throw new Error(response.data?.error || 'Failed to fetch jobs');
    }   
    return response?.data?.data?.jobs || [];
  }
  catch(error:any){
    throw new Error(
      error.response?.data?.error ||
      error.message ||
      'Failed to fetch jobs'
    )
  }
  }


  
export default async function JobsPage() {

  const jobs = await getAllJobsData();
  // console.log(jobs)
  


  return (
    <>
      <HeroComponent
        heading={
          <Heading>
            Discover Your <span className="text-blue-600">Future Career</span>{' '}
            <br />
            with Sudha Software Solutions
          </Heading>
        }
        description={
          <Paragraph>
            At Sudha Software Solutions, we don&apos;t just offer jobs—we offer
            a journey of innovation, growth, and transformation. Whether
            you&apos;re seeking opportunities in our core business, innovative
            product companies, or dynamic subsidiaries, our career portal
            connects you with roles that inspire and empower.
          </Paragraph>
        }
        heroImageSrc="/images/job.png"
        heroImageWidth={500}
        heroImageHeight={500}
        heroImageClassName="h-72 rounded-xl object-cover object-top"
      />


        <div className='mx-[4%] xl:mx-[10%] py-24'>
                  <Heading className='mb-10'>Find Your  <span className="text-blue-600">New Role !</span></Heading>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    {jobs.map((job: any) => (              
                      <JobsCard key={job._id} jobs={job} />              
                    ))
                    }                     
              </div>
        </div>


      <WhyChooseUs
        heading={
          <Heading>
            🌟 Why
            <span className="text-blue-600"> Choose</span> Us?
          </Heading>
        }
        iconSrc="/images/archery2.png"
        imageSrc="/images/why-choose-us.jpg"
        features={jobsFeature}
      />
    </>
  );
}
