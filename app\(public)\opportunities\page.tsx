'use client';
import React, { useState, useEffect } from 'react';
import {
  HiOutlineBriefcase,
  HiOutlineAcademicCap,
  HiOutlineCog,
  HiOutlineChartBar,
  HiOutlineBuildingOffice2,
  HiOutlineUserGroup,
  HiOutlineStar,
  HiOutlineCheckCircle,
  HiOutlineArrowRight,
  HiOutlineLightBulb,
  HiOutlineGlobeAlt,
  HiOutlineChartPie,
  HiOutlineEye,
} from 'react-icons/hi2';
import {
  HiOutlineCode,
  HiOutlineTrendingUp,
  HiOutlineFilter,
} from 'react-icons/hi';
import Link from 'next/link';

export default function OpportunitiesPage() {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const opportunities = [
    {
      icon: <HiOutlineBriefcase className="h-8 w-8" />,
      title: 'Full-Time Jobs',
      description:
        'Launch your career with permanent positions at top companies',
      count: '2,500+ jobs available',
      color: 'from-indigo-500 to-purple-600',
      href: '/opportunities/jobs',
    },
    {
      icon: <HiOutlineAcademicCap className="h-8 w-8" />,
      title: 'Internships',
      description:
        'Gain valuable experience and build your professional network',
      count: '800+ internships available',
      color: 'from-emerald-500 to-teal-600',
      href: '/opportunities/internships',
    },
    // {
    //   icon: <HiOutlineCog className="h-8 w-8" />,
    //   title: 'Freelance Projects',
    //   description: 'Work on flexible projects and build your portfolio',
    //   count: '1,200+ projects available',
    //   color: 'from-amber-500 to-orange-600',
    //   href: '/opportunities/freelance',
    // },
  ];

  const categories = [
    {
      icon: <HiOutlineChartBar className="h-6 w-6" />,
      name: 'Marketing',
      jobs: '140 jobs available',
    },
    {
      icon: <HiOutlineChartPie className="h-6 w-6" />,
      name: 'Finance',
      jobs: '95 jobs available',
    },
    {
      icon: <HiOutlineCode className="h-6 w-6" />,
      name: 'Technology',
      jobs: '434 jobs available',
    },
    {
      icon: <HiOutlineCog className="h-6 w-6" />,
      name: 'Engineering',
      jobs: '542 jobs available',
    },
    {
      icon: <HiOutlineBuildingOffice2 className="h-6 w-6" />,
      name: 'Business',
      jobs: '211 jobs available',
    },
    {
      icon: <HiOutlineUserGroup className="h-6 w-6" />,
      name: 'Human Resource',
      jobs: '348 jobs available',
    },
  ];

  const features = [
    { icon: <HiOutlineGlobeAlt className="h-5 w-5" />, text: 'Global Reach' },
    {
      icon: <HiOutlineFilter className="h-5 w-5" />,
      text: 'Advanced Filtering',
    },
    { icon: <HiOutlineEye className="h-5 w-5" />, text: 'Easy Posting' },
    {
      icon: <HiOutlineChartBar className="h-5 w-5" />,
      text: 'Analytics & Insights',
    },
  ];

  const benefits = [
    {
      icon: <HiOutlineTrendingUp className="h-12 w-12 text-indigo-600" />,
      title: 'Career Growth',
      description:
        'Access to thousands of opportunities tailored to your skills and aspirations',
    },
    {
      icon: <HiOutlineUserGroup className="h-12 w-12 text-emerald-500" />,
      title: 'Expert Network',
      description:
        'Connect with top employers and industry professionals worldwide',
    },
    {
      icon: <HiOutlineLightBulb className="h-12 w-12 text-amber-500" />,
      title: 'Smart Matching',
      description:
        'AI-powered recommendations to find your perfect career match',
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50 transition-colors duration-300 dark:bg-slate-900 ">
      {/* Hero Section */}
      <section className="lg:px-[7%] relative overflow-hidden bg-gradient-to-br from-indigo-600 via-cyan to-cyan-100 dark:from-slate-900 dark:via-slate-800 dark:to-black">
        <div className="absolute inset-0 bg-[url('/api/placeholder/1920/1080')] opacity-10"></div>

        <div className="relative container mx-auto px-4 py-4">
          <div className="grid items-center gap-12 lg:grid-cols-2">
            <div
              className={`transition-all duration-1000 ${isVisible ? 'translate-x-0 opacity-100' : '-translate-x-10 opacity-0'}`}
            >
              <h1 className="mb-6 text-5xl leading-tight font-bold text-black dark:text-white lg:text-6xl">
                Unlock Your Next
                <span className="block bg-gradient-to-r from-indigo-800 to-purple-800 dark:from-indigo-400 dark:to-purple-400 bg-clip-text text-transparent">
                  Career Move
                </span>
              </h1>

              <p className="mb-8 text-xl leading-relaxed text-gray-800 dark:text-gray-300">
                Explore thousands of opportunities tailored to your skills and
                aspirations. Connect with top employers and take the next step
                in your career journey with confidence.
              </p>

              {/* <div className="mb-8 flex flex-col gap-4 sm:flex-row">
                <div className="flex-1">
                  <div className="relative">
                    <input
                      type="email"
                      placeholder="Your email address"
                      className="w-full rounded-full border-2 border-transparent bg-white/10 px-6 py-4 text-gray-800 dark:text-white placeholder-gray-800 dark:placeholder-gray-300 backdrop-blur-sm transition-all focus:border-indigo-400 focus:outline-none"
                    />
                  </div>
                </div>
                <button className="rounded-full bg-gradient-to-r from-indigo-600 to-purple-600 px-8 py-4 font-semibold text-white transition-all duration-200 hover:scale-105 hover:shadow-lg">
                  Get Started
                </button>
              </div> */}
            </div>

            <div
              className={`transition-all delay-300 duration-1000 ${isVisible ? 'translate-x-0 opacity-100' : 'translate-x-10 opacity-0'}`}
            >
              <div className="relative">
                <img
                  src="/images/opportunities.jpeg"
                  alt="Career opportunities"
                  className="h-auto w-full rounded-2xl shadow-2xl"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="lg:px-[7%] bg-white py-20 dark:bg-slate-800">
        <div className="container mx-auto px-4">
          <div className="mb-16 text-center">
            <h2 className="mb-4 text-4xl font-bold text-slate-900 dark:text-white">
              Explore Your Career Path And Discover Opportunities
            </h2>
            <p className="mx-auto max-w-3xl text-xl text-gray-600 dark:text-gray-300">
              Whether you're looking to break into a new industry or advance in
              your current field, discover the perfect match for your
              professional journey.
            </p>
          </div>

          <div className="grid grid-cols-2 gap-6 md:grid-cols-3 lg:grid-cols-6">
            {categories.map((category, index) => (
              <div
                key={index}
                className="group cursor-pointer rounded-2xl bg-gray-50 p-6 transition-all duration-300 hover:-translate-y-1 hover:bg-gradient-to-br hover:from-indigo-50 hover:to-purple-50 hover:shadow-lg dark:bg-slate-700 dark:hover:from-slate-600 dark:hover:to-slate-700"
              >
                <div className="flex flex-col items-center text-center">
                  <div className="mb-4 rounded-xl bg-white p-3 shadow-sm transition-all group-hover:shadow-md dark:bg-slate-800">
                    <div className="text-slate-700 group-hover:text-indigo-600 dark:text-gray-300">
                      {category.icon}
                    </div>
                  </div>
                  <h3 className="mb-2 font-semibold text-slate-900 dark:text-white">
                    {category.name}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {category.jobs}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
       {/* Gateway to Success */}
      <section className="lg:px-[7%] bg-gray-50 py-20 dark:bg-slate-900">
        <div className="container mx-auto px-4">
          <div className="mb-16 text-center">
            <h2 className="mb-4 text-4xl font-bold text-slate-900 dark:text-white">
              Your Gateway to Success
            </h2>
            <p className="mx-auto max-w-2xl text-xl text-gray-600 dark:text-gray-300">
              Choose your path and start your journey with personalized
              opportunities that match your goals.
            </p>
          </div>

          <div className="grid gap-8 md:grid-cols-3">
            {opportunities.map((opportunity, index) => (
              <div
                key={index}
                className="group relative overflow-hidden rounded-3xl border border-gray-200 bg-white p-8 shadow-lg transition-all duration-500 hover:-translate-y-2 hover:shadow-2xl dark:border-slate-700 dark:bg-slate-800"
              >
                {/* Background Gradient */}
                <div
                  className={`absolute inset-0 bg-gradient-to-br ${opportunity.color} opacity-0 transition-opacity duration-500 group-hover:opacity-5`}
                ></div>

                <div className="relative z-10">
                  <div
                    className={`inline-flex rounded-2xl bg-gradient-to-r p-4 ${opportunity.color} mb-6 text-white transition-transform duration-300 group-hover:scale-110`}
                  >
                    {opportunity.icon}
                  </div>

                  <h3 className="mb-4 text-2xl font-bold text-slate-900 dark:text-white">
                    {opportunity.title}
                  </h3>

                  <p className="mb-6 leading-relaxed text-gray-600 dark:text-gray-300">
                    {opportunity.description}
                  </p>

                  <div className="mb-8 flex items-center justify-between">
                    <span className="text-sm font-semibold text-indigo-600 dark:text-indigo-400">
                      {opportunity.count}
                    </span>
                    <div className="flex text-yellow-400">
                      {[...Array(5)].map((_, i) => (
                        <HiOutlineStar key={i} className="h-4 w-4 fill-current" />
                      ))}
                    </div>
                  </div>

                  <a
                    href={opportunity.href}
                    className={`inline-flex w-full items-center justify-center gap-2 bg-gradient-to-r px-6 py-3 ${opportunity.color} rounded-xl font-semibold text-white transition-all duration-200 group-hover:shadow-lg hover:scale-105`}
                  >
                    Explore {opportunity.title}
                    <HiOutlineArrowRight className="h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </a>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Platform Features */}
      <section className="lg:px-[7%] bg-white py-20 dark:bg-slate-800">
        <div className="container mx-auto px-4">
          <div className="grid items-center gap-16 lg:grid-cols-2">
            <div>
              <h2 className="mb-6 text-4xl font-bold text-slate-900 dark:text-white">
                Reach Top Talent Worldwide
              </h2>
              <p className="mb-8 text-xl leading-relaxed text-gray-600 dark:text-gray-300">
                Expand your search and attract skilled professionals from around
                the globe. Our platform makes it easy to post job listings,
                review applications, and manage your recruitment process
                efficiently.
              </p>

              <div className="mb-8 grid grid-cols-2 gap-4">
                {features.map((feature, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <HiOutlineCheckCircle className="h-5 w-5 text-emerald-500" />
                    <span className="font-medium text-slate-700 dark:text-gray-300">
                      {feature.text}
                    </span>
                  </div>
                ))}
              </div>

              <Link href="/dashboard">
              <button className="rounded-xl bg-slate-900 px-8 py-4 font-semibold text-white transition-colors hover:bg-slate-800 dark:bg-indigo-600 dark:hover:bg-indigo-700">
                Post a Job
              </button>
              </Link>
            </div>

            <div className="relative">
              <img
                src="/images/opportunities3.jpeg"
                alt="Global talent reach"
                className="h-auto w-full rounded-2xl shadow-2xl"
              />

              {/* Floating Elements */}
              <div className="absolute -top-4 -right-4 animate-bounce rounded-full bg-emerald-500 p-3 text-white shadow-lg">
                <HiOutlineUserGroup className="h-6 w-6" />
              </div>
              <div className="absolute -bottom-4 -left-4 animate-pulse rounded-full bg-indigo-600 p-3 text-white shadow-lg">
                <HiOutlineTrendingUp className="h-6 w-6" />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Guidance Section */}
      <section className="lg:px-[7%] bg-gradient-to-br from-indigo-600 via-cyan to-cyan-100 py-20 text-gray-700 dark:text-white dark:from-slate-900 dark:via-slate-800 dark:to-black">
        <div className="container mx-auto px-4">
          <div className="mb-16 text-center">
            <h2 className="mb-4 text-4xl font-bold">
              Guidance from Industry Experts
            </h2>
            <p className="mx-auto max-w-3xl text-xl text-gray-700 dark:text-gray-300">
              Gain valuable insights and practical advice from seasoned
              professionals to help you navigate your career journey with
              confidence.
            </p>
          </div>

          <div className="grid gap-8 lg:grid-cols-3">
            {benefits.map((benefit, index) => (
              <div
                key={index}
                className="rounded-2xl border border-white/10 bg-white/5 p-8 text-center backdrop-blur-sm transition-all duration-300 hover:bg-white/10"
              >
                <div className="mb-6 flex justify-center">{benefit.icon}</div>
                <h3 className="mb-4 text-xl font-bold">{benefit.title}</h3>
                <p className="leading-relaxed text-gray-600 dark:text-gray-300">
                  {benefit.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
}
