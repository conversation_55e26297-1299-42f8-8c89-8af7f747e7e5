'use client';
import React, { useEffect, useState } from 'react';
import Orb from '@/components/backgrounds/Orb/Orb';
import Link from 'next/link';

const Home = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [hoveredStat, setHoveredStat] = useState<number | null>(null);
  const [hoveredBenefit, setHoveredBenefit] = useState<number | null>(null);
  const [hoveredOpportunity, setHoveredOpportunity] = useState<number | null>(
    null
  );
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [currentImageIndex, setCurrentImageIndex] = useState(0);


  const heroImages = [
    // 'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=600&h=600&fit=crop&crop=face',
    // 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=600&h=600&fit=crop',
    // 'https://images.unsplash.com/photo-1551836022-deb4988cc6c0?w=600&h=600&fit=crop'
    '/images/career.png',
  ];

  useEffect(() => {
    setIsVisible(true);

    const interval = setInterval(() => {
      setCurrentImageIndex(prev => (prev + 1) % heroImages.length);
    }, 4000);

    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };
    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  const stats = [
    {
      label: 'Active Job Openings',
      value: '50+',
      color: 'from-slate-100 to-slate-200 dark:from-slate-800 dark:to-slate-700',
    },
    {
      label: 'Companies Partnered',
      value: '25+',
      color: 'from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700',
    },
    {
      label: 'Webinars and Events',
      value: '35+',
      color: 'from-slate-50 to-slate-150 dark:from-slate-800 dark:to-slate-700',
    },
    {
      label: 'Training Programs',
      value: '15+',
      color: 'from-gray-50 to-gray-150 dark:from-gray-800 dark:to-gray-700',
    },
  ];

  const benefits = [
    {
      icon: '✅',
      title: 'Curated Opportunities',
      description:
        'We collaborate with top companies like Sudha Software Solutions Private Limited and Smart Dine Menu to bring you quality listings that matter.',
      gradient: 'from-blue-50 to-blue-100 dark:from-blue-900/30 dark:to-blue-800/30',
    },
    {
      icon: '🎯',
      title: 'Comprehensive Job Details',
      description:
        'Dive deep into job descriptions, company cultures, and career paths to find your perfect fit.',
      gradient: 'from-indigo-50 to-indigo-100 dark:from-indigo-900/30 dark:to-indigo-800/30',
    },
    {
      icon: '🚀',
      title: 'Seamless Navigation',
      description:
        'Our user-friendly interface with smart filtering options lets you quickly search by location, category, or specific interests.',
      gradient: 'from-slate-50 to-slate-100 dark:from-slate-900/30 dark:to-slate-800/30',
    },
  ];

  const opportunities = [
    {
      icon: '🔥',
      title: 'Hot Jobs',
      subtitle: 'In-demand roles updated daily',
      description:
        'Get instant access to the latest and most in-demand roles in the market.',
      gradient: 'from-orange-50 to-orange-100 dark:from-orange-900/30 dark:to-orange-800/30',
    },
    {
      icon: '💎',
      title: 'Exclusive Internships',
      subtitle: 'Gain hands-on experience',
      description:
        'Find internships that provide hands-on experience and valuable industry exposure.',
      gradient: 'from-purple-50 to-purple-100 dark:from-purple-900/30 dark:to-purple-800/30',
    },
    {
      icon: '⚡',
      title: 'Dynamic Training Programs',
      subtitle: 'Accelerate your career',
      description:
        'Enhance your skills with specialized training designed to accelerate your career.',
      gradient: 'from-yellow-50 to-yellow-100 dark:from-yellow-900/30 dark:to-yellow-800/30',
    },
  ];

  const FloatingElement = ({
    children,
    delay = 0,
  }: {
    children: React.ReactNode;
    delay?: number;
  }) => (
    <div
      className="animate-bounce"
      style={{
        animationDelay: `${delay}s`,
        animationDuration: '3s',
      }}
    >
      {children}
    </div>
  );

  return (
    <main className="font-urbanist relative min-h-screen overflow-x-hidden bg-white dark:bg-gray-900">
      {/* Animated Background Elements */}
      <div className="pointer-events-none fixed inset-0 overflow-hidden">
        <div
          className="absolute h-96 w-96 rounded-full bg-gradient-to-r from-blue-400/10 to-purple-400/10 dark:from-blue-600/20 dark:to-purple-600/20 blur-3xl transition-transform duration-1000 ease-out"
          style={{
            transform: `translate(${mousePosition.x * 0.02}px, ${mousePosition.y * 0.02}px)`,
          }}
        />
        <div
          className="absolute top-1/2 right-0 h-72 w-72 rounded-full bg-gradient-to-r from-pink-400/10 to-red-400/10 dark:from-pink-600/20 dark:to-red-600/20 blur-3xl transition-transform duration-1000 ease-out"
          style={{
            transform: `translate(${mousePosition.x * -0.01}px, ${mousePosition.y * -0.01}px)`,
          }}
        />
      </div>

      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-slate-900 py-12 sm:py-16 md:py-20 lg:px-24 lg:py-24 xl:py-32">
        {/* <div className="absolute inset-0 animate-pulse bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiM5QzkyQUMiIGZpbGwtb3BhY2l0eT0iMC4wMyI+PGNpcmNsZSBjeD0iMzAiIGN5PSIzMCIgcj0iMiIvPjwvZz48L2c+PC9zdmc+')] opacity-40"></div> */}
        <div className="relative mx-auto max-w-none px-4 sm:px-6 md:px-8 lg:px-12 xl:px-40 2xl:px-44">
          <div className="grid items-center gap-8 sm:gap-10 lg:grid-cols-2 lg:gap-12">
            <div
              className={`space-y-6 text-center transition-all duration-1000 sm:space-y-8 lg:text-left ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-12 opacity-0'}`}
            >
              <FloatingElement delay={0.5}>
                <div className="inline-flex cursor-pointer items-center rounded-full border border-blue-200 dark:border-blue-700 bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-900/50 dark:to-purple-900/50 px-3 py-1.5 text-xs font-medium text-blue-700 dark:text-blue-300 transition-transform hover:scale-105 sm:px-4 sm:py-2 sm:text-sm">
                  <span className="mr-1.5 animate-pulse sm:mr-2">🚀</span>
                  Career Opportunities Await
                </div>
              </FloatingElement>

              <div className="space-y-4 sm:space-y-6">
                <h1 className="font-nunito font-poppins text-3xl leading-tight font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl md:text-5xl lg:text-5xl xl:text-6xl">
                  Welcome to Our{' '}
                  <span className=" bg-gradient-to-r from-blue-600 to-purple-800 bg-clip-text text-transparent">
                    Career Portal
                  </span>
                </h1>
                <p className="mx-auto max-w-2xl text-base leading-7 text-gray-600 dark:text-gray-300 sm:text-lg sm:leading-8 lg:mx-0 lg:text-xl">
                  🌟 Unlock your potential and step into a future filled with
                  endless opportunities. Whether you are launching your career,
                  seeking the next exciting role, or exploring internships,
                  training, and development programs, our platform is your
                  gateway to success.
                </p>
              </div>

              <div className="flex flex-col justify-center gap-3 sm:flex-row sm:gap-4 lg:justify-start">
                <Link href="/sign-in">
                <button className="group font-poppins inline-flex items-center justify-center rounded-lg bg-gradient-to-r from-blue-600 to-purple-600 px-6 py-3 text-sm font-semibold text-white shadow-lg transition-all duration-300 hover:-translate-y-1 hover:scale-105 hover:from-blue-700 hover:to-purple-700 hover:shadow-xl sm:px-8 sm:py-4 sm:text-base">
                  Start Your Journey
                  <span className="ml-2 transition-transform duration-300 group-hover:translate-x-1">
                    →
                  </span>
                </button></Link>
                <Link href="/opportunities/jobs">
                <button className="group font-inter inline-flex items-center justify-center rounded-lg border-2 border-gray-300 dark:border-gray-600 px-6 py-3 text-sm font-semibold text-gray-700 dark:text-gray-300 transition-all duration-300 hover:-translate-y-1 hover:scale-105 hover:border-gray-400 dark:hover:border-gray-500 hover:bg-gray-50 dark:hover:bg-gray-700 sm:px-8 sm:py-4 sm:text-base">
                  Browse Jobs
                  <span className="ml-2 opacity-0 transition-opacity duration-300 group-hover:opacity-100">
                    👀
                  </span>
                </button>
                </Link>
              </div>
            </div>

            {/* <div
              className={`relative mt-8 transition-all delay-300 duration-1000 lg:mt-0 ${isVisible ? 'translate-x-0 opacity-100' : 'translate-x-12 opacity-0'}`}
            >
              <div className="absolute -inset-2 animate-pulse rounded-2xl bg-gradient-to-r from-blue-600 to-purple-600 opacity-20 blur-lg sm:-inset-4"></div>
              <div className="group relative mx-auto aspect-square w-full max-w-md sm:max-w-lg lg:max-w-xl xl:max-w-2xl">
                <div className="relative h-full w-full overflow-hidden rounded-2xl shadow-2xl">
                  {heroImages.map((src, index) => (
                    <img
                      key={index}
                      src={src}
                      className={`absolute inset-0 h-full w-full object-cover transition-opacity duration-1000 ${
                        index === currentImageIndex
                          ? 'opacity-100'
                          : 'opacity-0'
                      }`}
                      alt="Career opportunities"
                    />
                  ))}

                    

                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent transition-all duration-300 group-hover:from-black/30"></div>
                </div>
                <div className="absolute -right-4 -bottom-4 animate-bounce rounded-full bg-white dark:bg-gray-800 p-3 shadow-lg">
                  <span className="text-2xl">💼</span>
                </div>
              </div>
            </div> */}
            <Orb
    hoverIntensity={0.5}
    rotateOnHover={true}
    hue={0}
    forceHoverState={false}
  />
  

  

          </div>
        </div>
      </section>

      {/* Animated Stats Section */}
      <section className="bg-white dark:bg-gray-900 py-12 sm:py-16 lg:px-24">
        <div className="mx-auto max-w-none px-4 sm:px-6 md:px-8 lg:px-12 xl:px-40 2xl:px-44">
          <div className="grid grid-cols-2 gap-4 sm:gap-6 md:grid-cols-4 md:gap-8">
            {stats.map((stat, index) => (
              <div
                key={index}
                className="group cursor-pointer text-center"
                onMouseEnter={() => setHoveredStat(index)}
                onMouseLeave={() => setHoveredStat(null)}
              >
                <div
                  className={`relative rounded-xl p-6 transition-all duration-700 ease-in-out ${
                    hoveredStat === index
                      ? 'bg-gradient-to-r ' +
                        stat.color +
                        ' -translate-y-1 scale-105 shadow-lg'
                      : 'hover:bg-gray-50 dark:hover:bg-gray-800 hover:shadow-md'
                  }`}
                >
                  <div
                    className={`font-poppins text-2xl font-bold transition-all duration-500 ease-in-out sm:text-3xl lg:text-4xl ${
                      hoveredStat === index
                        ? 'scale-110 text-gray-800 dark:text-gray-200'
                        : 'text-blue-600 dark:text-blue-400'
                    }`}
                  >
                    {stat.value}
                  </div>
                  <div
                    className={`mt-1 text-xs leading-tight font-medium transition-all duration-500 ease-in-out sm:mt-2 sm:text-sm ${
                      hoveredStat === index ? 'text-gray-700 dark:text-gray-300' : 'text-gray-600 dark:text-gray-400'
                    }`}
                  >
                    {stat.label}
                  </div>
                  {hoveredStat === index && (
                    <div className="absolute inset-0 animate-pulse rounded-xl bg-white/10 dark:bg-gray-800/10"></div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Interactive Benefits Section */}
      <section className="bg-gray-50 dark:bg-gray-800 py-12 sm:py-16 lg:px-24 lg:py-20 xl:py-32">
        <div className="mx-auto max-w-none px-4 sm:px-6 md:px-8 lg:px-12 xl:px-40 2xl:px-44">
          <div className="grid items-center gap-8 sm:gap-10 lg:grid-cols-2 lg:gap-12">
            <div className="space-y-6 text-center sm:space-y-8 lg:text-left">
              <div className="flex items-center justify-center gap-3 sm:gap-4 lg:justify-start">
                <FloatingElement delay={0.2}>
                  <div className="cursor-pointer rounded-full bg-yellow-100 dark:bg-yellow-900 p-2 transition-transform hover:scale-110 sm:p-3">
                    <div className="flex h-6 w-6 items-center justify-center rounded-full bg-yellow-500 text-sm text-white sm:h-8 sm:w-8 sm:text-lg">
                      🎯
                    </div>
                  </div>
                </FloatingElement>
                <h2 className="font-nunito text-2xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-3xl lg:text-4xl">
                  Why Choose{' '}
                  <span className="bg-gradient-to-r from-blue-600 to-purple-800 bg-clip-text text-transparent">
                    Us
                  </span>
                  ? 🏆
                </h2>
              </div>

              <div className="space-y-4 sm:space-y-6">
                {benefits.map((benefit, index) => (
                  <div
                    key={index}
                    className="group cursor-pointer"
                    onMouseEnter={() => setHoveredBenefit(index)}
                    onMouseLeave={() => setHoveredBenefit(null)}
                  >
                    <div
                      className={`rounded-lg bg-white dark:bg-gray-700 p-4 shadow-sm transition-all duration-700 ease-in-out sm:p-6 ${
                        hoveredBenefit === index
                          ? '-translate-y-1 scale-102 bg-gradient-to-r shadow-lg ' +
                            benefit.gradient
                          : 'hover:scale-101 hover:shadow-md'
                      }`}
                    >
                      <div className="flex items-start gap-3 text-left sm:gap-4">
                        <span
                          className={`flex-shrink-0 text-xl transition-all duration-600 ease-in-out sm:text-2xl ${
                            hoveredBenefit === index ? 'scale-125' : ''
                          }`}
                        >
                          {benefit.icon}
                        </span>
                        <div className="min-w-0">
                          <h3
                            className={`text-sm font-semibold transition-colors duration-500 ease-in-out sm:text-base ${
                              hoveredBenefit === index
                                ? 'text-gray-800 dark:text-gray-200'
                                : 'text-gray-900 dark:text-white'
                            }`}
                          >
                            {benefit.title}
                          </h3>
                          <p
                            className={`mt-1 text-xs leading-relaxed transition-colors duration-500 ease-in-out sm:mt-2 sm:text-sm lg:text-base ${
                              hoveredBenefit === index
                                ? 'text-gray-700 dark:text-gray-300'
                                : 'text-gray-600 dark:text-gray-300'
                            }`}
                          >
                            {benefit.description}
                          </p>
                        </div>
                      </div>
                      {hoveredBenefit === index && (
                        <div className="pointer-events-none absolute inset-0 animate-pulse rounded-lg bg-white/10 dark:bg-gray-800/10"></div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="relative">
              <div className="absolute -inset-2 animate-pulse rounded-2xl bg-gradient-to-r from-yellow-400 to-orange-400 opacity-20 blur-lg sm:-inset-4"></div>
              <div className="group relative mx-auto aspect-square w-full max-w-md sm:max-w-lg lg:max-w-xl xl:max-w-2xl">
                <img
                  src="https://images.unsplash.com/photo-1600880292203-757bb62b4baf?w=600&h=600&fit=crop"
                  className="h-full w-full rounded-2xl object-cover shadow-xl transition-transform duration-500 group-hover:scale-105"
                  alt="Why choose us"
                />
                <div className="absolute inset-0 rounded-2xl bg-gradient-to-t from-black/20 to-transparent transition-all duration-300 group-hover:from-black/30"></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Interactive Opportunities Section */}
      <section className="bg-white dark:bg-gray-900 py-12 sm:py-16 lg:px-24 lg:py-20 xl:py-32">
        <div className="mx-auto max-w-none px-4 sm:px-6 md:px-8 lg:px-12 xl:px-40 2xl:px-44">
          <div className="grid items-center gap-8 sm:gap-10 lg:grid-cols-2 lg:gap-12">
            <div className="relative order-2 lg:order-1">
              <div className="absolute -inset-2 animate-pulse rounded-2xl bg-gradient-to-r from-green-400 to-blue-400 opacity-20 blur-lg sm:-inset-4"></div>
              <div className="group relative mx-auto aspect-square w-full max-w-md sm:max-w-lg lg:max-w-xl xl:max-w-2xl">
                <img
                  src="https://images.unsplash.com/photo-1521737604893-d14cc237f11d?w=600&h=600&fit=crop"
                  className="h-full w-full rounded-2xl object-cover shadow-xl transition-transform duration-500 group-hover:scale-105 group-hover:rotate-1"
                  alt="Featured opportunities"
                />
                <div className="absolute top-4 right-4 animate-bounce rounded-full bg-white dark:bg-gray-800 p-2 shadow-lg">
                  <span className="text-lg">🌟</span>
                </div>
              </div>
            </div>

            <div className="order-1 space-y-6 text-center sm:space-y-8 lg:order-2 lg:text-left">
              <div className="flex items-center justify-center gap-3 sm:gap-4 lg:justify-start">
                {/* <FloatingElement delay={0.3}>
                  <div className="cursor-pointer rounded-full bg-green-100 dark:bg-green-900 p-2 transition-transform hover:scale-110 sm:p-3">
                    <div className="flex h-6 w-6 items-center justify-center rounded-full bg-green-500 text-sm text-white sm:h-8 sm:w-8 sm:text-lg">
                      🚀
                    </div>
                  </div>
                </FloatingElement> */}
                <h2 className="font-nunito text-2xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-3xl lg:text-4xl">
                  🌈 Featured{' '}
                  <span className="bg-gradient-to-r from-blue-600 to-purple-800 bg-clip-text text-transparent">
                    Opportunities
                  </span>
                </h2>
              </div>

              <div className="space-y-4 sm:space-y-6">
                {opportunities.map((opportunity, index) => (
                  <div
                    key={index}
                    className="group cursor-pointer"
                    onMouseEnter={() => setHoveredOpportunity(index)}
                    onMouseLeave={() => setHoveredOpportunity(null)}
                  >
                    <div
                      className={`rounded-lg border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800 p-4 shadow-sm transition-all duration-700 ease-in-out sm:p-6 ${
                        hoveredOpportunity === index
                          ? '-translate-y-1 scale-102 bg-gradient-to-r shadow-lg ' +
                            opportunity.gradient +
                            ' border-transparent'
                          : 'hover:scale-101 hover:shadow-md'
                      }`}
                    >
                      <div className="flex items-start gap-3 text-left sm:gap-4">
                        <span
                          className={`flex-shrink-0 text-xl transition-all duration-600 ease-in-out sm:text-2xl ${
                            hoveredOpportunity === index ? 'scale-125' : ''
                          }`}
                        >
                          {opportunity.icon}
                        </span>
                        <div className="min-w-0">
                          <h3
                            className={`text-sm font-semibold transition-colors duration-500 ease-in-out sm:text-base ${
                              hoveredOpportunity === index
                                ? 'text-gray-800 dark:text-gray-200'
                                : 'text-gray-900 dark:text-white'
                            }`}
                          >
                            {opportunity.title}: {opportunity.subtitle}
                          </h3>
                          <p
                            className={`mt-1 text-xs leading-relaxed transition-colors duration-500 ease-in-out sm:mt-2 sm:text-sm lg:text-base ${
                              hoveredOpportunity === index
                                ? 'text-gray-700 dark:text-gray-300'
                                : 'text-gray-600 dark:text-gray-300'
                            }`}
                          >
                            {opportunity.description}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Animated CTA Section */}
      <section className="relative overflow-hidden bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 py-12 sm:py-16 lg:px-32 lg:py-20">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 h-32 w-32 animate-ping rounded-full bg-white/10"></div>
          <div
            className="absolute right-1/4 bottom-1/4 h-24 w-24 animate-ping rounded-full bg-white/10"
            style={{ animationDelay: '1s' }}
          ></div>
        </div>

        <div className="relative mx-auto max-w-none px-4 text-center sm:px-6 md:px-8 lg:px-12 xl:px-40 2xl:px-44">
          <div className="space-y-6 sm:space-y-8">
            <h2 className="font-nunito animate-pulse text-2xl font-bold text-white sm:text-3xl lg:text-4xl">
              Ready to Transform Your Career?
            </h2>
            <div className="space-y-3 text-sm text-blue-100 sm:space-y-4 sm:text-lg">
              <p>
                💥 Don't wait for the perfect moment—create it! Register now,
                explore our extensive listings, and take the first step towards
                transforming your ambition into achievement.
              </p>
              <p>
                Embrace the future with us, and let your career soar to new
                heights!
              </p>
            </div>
            <div className="flex flex-col justify-center gap-3 sm:flex-row sm:gap-4">
              <Link href="/sign-in">
              <button className="group inline-flex items-center justify-center rounded-lg bg-white px-6 py-3 text-sm font-semibold text-blue-600 shadow-lg transition-all duration-300 hover:-translate-y-1 hover:scale-105 hover:bg-gray-50 hover:shadow-xl sm:px-8 sm:py-4 sm:text-base">
                🚀 Start Your Journey Today
                <span className="ml-2 group-hover:animate-bounce">✨</span>
              </button>
              </Link>
              <Link href="/opportunities">
              <button className="group inline-flex items-center justify-center rounded-lg border-2 border-white px-6 py-3 text-sm font-semibold text-white transition-all duration-300 hover:-translate-y-1 hover:scale-105 hover:bg-white hover:text-blue-600 sm:px-8 sm:py-4 sm:text-base">
                Explore Opportunities
                <span className="ml-2 transition-transform duration-300 group-hover:translate-x-1">
                  →
                </span>
              </button>
              </Link>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
};

export default Home;