'use client';
import { CourseComponent } from '@/components/custom/courseComponent';
import { coursesData } from '@/constant/courses';
import NotFound from '@/components/custom/notFound';

type CourseKey = keyof typeof coursesData;

interface PageProps {
  params: Promise<{ course: string }>;
}

export default async function CertificationPage({ params }: PageProps) {
  const { course } = await params;
  const data = coursesData[course as CourseKey];
  if (!data) return <NotFound />;
  return <CourseComponent course={data} />;
}
