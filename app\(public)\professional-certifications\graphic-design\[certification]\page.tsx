'use client';
import { CertificationComponent } from '@/components/custom/certificationComponent';
import { certificationsData } from '@/constant/courses';
import NotFound from '@/components/custom/notFound';

type CertificationKey = keyof typeof certificationsData;

interface PageProps {
  params: Promise<{ certification: string }>;
}

export default async function CertificationPage({ params }: PageProps) {
  const { certification } = await params;
  const data = certificationsData[certification as CertificationKey];
  if (!data) return <NotFound />;
  return <CertificationComponent data={data} />;
}
