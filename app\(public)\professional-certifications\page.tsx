import React from 'react';
import { 
  HiOutlineCode,
  HiOutlineChartBar,
  HiOutlinePhotograph,
  HiOutlineDeviceMobile,
  HiOutlineSpeakerphone,
  HiOutlineAcademicCap,
  HiOutlineCheckCircle,
  HiOutlineTrendingUp,
  HiOutlineUsers,
  HiOutlineCurrencyDollar,
  HiOutlineBookOpen,
  HiOutlineArrowRight
} from "react-icons/hi";
import Link from 'next/link';
import Image from 'next/image';

const benefits = [
  { 
    title: 'Career Advancement', 
    description: 'Stand out to employers and qualify for promotions with industry-recognized credentials.',
    icon: <HiOutlineTrendingUp className="h-6 w-6" />,
    color: 'text-blue-600 dark:text-blue-400'
  },
  { 
    title: 'Skill Validation', 
    description: 'Showcase your expertise with certified credentials that prove your capabilities.',
    icon: <HiOutlineCheckCircle className="h-6 w-6" />,
    color: 'text-green-600 dark:text-green-400'
  },
  { 
    title: 'Networking Opportunities', 
    description: 'Connect with a community of certified professionals in your field.',
    icon: <HiOutlineUsers className="h-6 w-6" />,
    color: 'text-purple-600 dark:text-purple-400'
  },
  { 
    title: 'Higher Earning Potential', 
    description: 'Certified professionals typically earn 15-25% more than their non-certified peers.',
    icon: <HiOutlineCurrencyDollar className="h-6 w-6" />,
    color: 'text-yellow-600 dark:text-yellow-400'
  },
  { 
    title: 'Continuous Learning', 
    description: 'Stay current with evolving industry trends and emerging technologies.',
    icon: <HiOutlineBookOpen className="h-6 w-6" />,
    color: 'text-indigo-600 dark:text-indigo-400'
  },
];

const certifications = [
  {
    label: 'Web Development',
    href: '/professional-certifications/web-development',
    icon: <HiOutlineCode className="h-10 w-10" />,
    description: 'Build modern websites and web applications using cutting-edge technologies like React, Node.js, and more.',
    duration: '6-8 months',
    level: 'Beginner to Advanced',
    gradient: 'from-blue-500 to-blue-700',
    bgColor: 'bg-blue-50 dark:bg-blue-900',
    textColor: 'text-blue-700 dark:text-blue-200'
  },
  {
    label: 'Data Science',
    href: '/professional-certifications/data-science',
    icon: <HiOutlineChartBar className="h-10 w-10" />,
    description: 'Master data analysis, machine learning, and statistical modeling to drive business decisions.',
    duration: '8-10 months',
    level: 'Intermediate to Advanced',
    gradient: 'from-green-500 to-green-700',
    bgColor: 'bg-green-50 dark:bg-green-900',
    textColor: 'text-green-700 dark:text-green-200'
  },
  {
    label: 'Graphic Design',
    href: '/professional-certifications/graphic-design',
    icon: <HiOutlinePhotograph className="h-10 w-10" />,
    description: 'Create compelling visual content and master design principles for digital and print media.',
    duration: '4-6 months',
    level: 'Beginner to Intermediate',
    gradient: 'from-pink-500 to-pink-700',
    bgColor: 'bg-pink-50 dark:bg-pink-900',
    textColor: 'text-pink-700 dark:text-pink-200'
  },
  {
    label: 'Digital Marketing',
    href: '/professional-certifications/digital-marketing',
    icon: <HiOutlineSpeakerphone className="h-10 w-10" />,
    description: 'Build comprehensive marketing strategies using SEO, social media, and digital advertising.',
    duration: '5-7 months',
    level: 'Beginner to Advanced',
    gradient: 'from-orange-500 to-orange-700',
    bgColor: 'bg-orange-50 dark:bg-orange-900',
    textColor: 'text-orange-700 dark:text-orange-200'
  },
  {
    label: 'Mobile App Development',
    href: '/professional-certifications/mobile-app-development',
    icon: <HiOutlineDeviceMobile className="h-10 w-10" />,
    description: 'Develop native and cross-platform mobile applications for iOS and Android devices.',
    duration: '7-9 months',
    level: 'Intermediate to Advanced',
    gradient: 'from-purple-500 to-purple-700',
    bgColor: 'bg-purple-50 dark:bg-purple-900',
    textColor: 'text-purple-700 dark:text-purple-200'
  },
];

export default function ProfessionalCertificationsPage() {
  return (
    <div className="bg-white dark:bg-slate-900 min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-900 dark:to-blue-950 pt-16 pb-20 px-4 overflow-hidden">
        <div className="max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <div className="space-y-6">
                <h1 className="text-5xl lg:text-6xl font-bold text-slate-800 dark:text-white leading-tight">
                  Build Your Skills,
                  <span className="text-blue-600 dark:text-blue-400"> Get a Dream Job</span>
                </h1>
                <p className="text-xl text-slate-600 dark:text-slate-300 leading-relaxed">
                  We provide you various courses from scratch using the best mentors, advanced technology, and affordable prices
                </p>
              </div>
              <div className="flex flex-col sm:flex-row gap-4">
                <button className="bg-slate-800 dark:bg-slate-700 text-white px-8 py-4 rounded-lg font-semibold hover:bg-slate-900 dark:hover:bg-slate-800 transition-colors">
                  Talk to an Expert
                </button>
              </div>
            </div>
            
            {/* Illustration Section */}
            <div className="relative">
              <div className="relative z-10">
                <div className="bg-white dark:bg-slate-800 max-w-[90%] rounded-3xl p-8 shadow-2xl">
                  <div className="space-y-6">
                    {/* People illustrations would go here - using placeholder for now */}
                    <div className="h-84 bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900 dark:to-purple-900 rounded-2xl flex items-center justify-center">
                      <div className="text-center space-y-4">
                        <Image src="/images/certifications.jpg" width={350} height={350} alt="Certification image" className='h-64 w-md'/>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Floating elements */}
              <div className="absolute top-[-12] right-12 z-10 bg-blue-500 text-white p-3 rounded-full shadow-lg">
                <HiOutlineCheckCircle className="h-6 w-6" />
              </div>
              <div className="absolute bottom-[-12] left-[-12] z-10 bg-yellow-400 text-slate-800 p-3 rounded-full shadow-lg">
                <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 px-4 bg-slate-50 dark:bg-slate-800">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-slate-800 dark:text-white mb-4">Why Get Certified?</h2>
            <p className="text-xl text-slate-600 dark:text-slate-300 max-w-3xl mx-auto">
              Professional certifications provide tangible benefits that can transform your career trajectory and open new opportunities.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {benefits.map((benefit, index) => (
              <div key={benefit.title} className="group">
                <div className="bg-white dark:bg-slate-900 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-2 h-full">
                  <div className={`${benefit.color} mb-6`}>
                    {benefit.icon}
                  </div>
                  <h3 className="text-xl font-bold text-slate-800 dark:text-white mb-4">{benefit.title}</h3>
                  <p className="text-slate-600 dark:text-slate-300 leading-relaxed">{benefit.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Certifications Section */}
      <section className="py-20 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-slate-800 dark:text-white mb-4">Explore Our Certifications</h2>
            <p className="text-xl text-slate-600 dark:text-slate-300 max-w-3xl mx-auto">
              Choose from our comprehensive range of industry-focused certification programs designed to advance your career.
            </p>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {certifications.map((cert, index) => (
              <Link href={cert.href} key={cert.label}>
                <div className="group cursor-pointer">
                  <div className="bg-white dark:bg-slate-900 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 overflow-hidden border border-slate-100 dark:border-slate-800">
                    <div className={`h-2 bg-gradient-to-r ${cert.gradient}`}></div>
                
                    <div className="p-8">
                      <div className="flex items-start gap-6">
                        <div className={`${cert.bgColor} ${cert.textColor} p-4 rounded-xl flex-shrink-0`}>
                          {cert.icon}
                        </div>
                
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-3">
                            <h3 className="text-2xl font-bold text-slate-800 dark:text-white">{cert.label}</h3>
                            <HiOutlineArrowRight className="h-6 w-6 text-slate-400 dark:text-slate-300 group-hover:text-blue-600 dark:group-hover:text-blue-400 group-hover:translate-x-1 transition-all" />
                          </div>
                
                          <p className="text-slate-600 dark:text-slate-300 mb-6 leading-relaxed">{cert.description}</p>
                
                          <div className="flex flex-wrap gap-4 text-sm">
                            <div className="flex items-center gap-2 text-slate-500 dark:text-slate-300">
                              <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                              <span>{cert.duration}</span>
                            </div>
                            <div className="flex items-center gap-2 text-slate-500 dark:text-slate-300">
                              <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                              </svg>
                              <span>{cert.level}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 bg-gradient-to-r from-blue-600 to-purple-700 dark:from-blue-900 dark:to-purple-900">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl font-bold text-white mb-6">Ready to Start Your Journey?</h2>
          <p className="text-xl text-blue-100 mb-8 leading-relaxed">
            Join thousands of professionals who have advanced their careers through our certification programs.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/professional-certifications/benefits">
              <button className="bg-white dark:bg-slate-800 text-blue-600 dark:text-blue-300 px-8 py-4 rounded-lg font-semibold hover:bg-blue-50 dark:hover:bg-slate-700 transition-colors">
                View Benefits
              </button>
            </Link>
            <Link href="/contact">
              <button className="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-blue-600 dark:hover:bg-slate-800 dark:hover:text-blue-300 transition-colors">
                Talk to an Advisor
              </button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}