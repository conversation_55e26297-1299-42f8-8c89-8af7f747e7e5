'use client';
import React from 'react';
import Waves from '@/components/ui/Waves';



// Define an interface for the shape of each toolkit item
interface ToolCard {
  icon: string; // Emoji for the tool icon
  title: string;
  description: string;
  link: string; // Link to the specific tool or guide
  bgColor: string;
  iconBg: string; // Background for the icon circle
  hoverBg: string;
}

// Unified card colors
const UNIFIED_BG_COLOR = 'bg-white dark:bg-gray-800'; // Now using a solid background for inner cards
const UNIFIED_ICON_BG = 'bg-blue-100 dark:bg-blue-900/50';
const UNIFIED_HOVER_BG = 'hover:border-blue-400 dark:hover:border-blue-600'; // Border change on hover

const careerTools: ToolCard[] = [
  {
    icon: '📄',
    title: 'Resume Builder',
    description: 'Craft a professional, ATS-friendly resume that opens doors to new opportunities.',
    link: '/toolkit/resume-builder',
    bgColor: UNIFIED_BG_COLOR,
    iconBg: UNIFIED_ICON_BG,
    hoverBg: UNIFIED_HOVER_BG,
  },
  {
    icon: '✍️',
    title: 'Cover Letter Generator',
    description: 'Generate personalized cover letters tailored perfectly to each job application.',
    link: '/toolkit/cover-letter-generator',
    bgColor: UNIFIED_BG_COLOR,
    iconBg: UNIFIED_ICON_BG,
    hoverBg: UNIFIED_HOVER_BG,
  },
  {
    icon: '🎙️',
    title: 'Interview Practice Simulator',
    description: 'Refine your interview skills with realistic scenarios and AI-powered feedback.',
    link: '/toolkit/interview-practice',
    bgColor: UNIFIED_BG_COLOR,
    iconBg: UNIFIED_ICON_BG,
    hoverBg: UNIFIED_HOVER_BG,
  },
  {
    icon: '💵',
    title: 'Salary Negotiator',
    description: 'Get data-backed insights and strategies to maximize your earning potential confidently.',
    link: '/toolkit/salary-negotiator',
    bgColor: UNIFIED_BG_COLOR,
    iconBg: UNIFIED_ICON_BG,
    hoverBg: UNIFIED_HOVER_BG,
  },
  {
    icon: '🧠',
    title: 'Skill Assessment Quizzes',
    description: 'Identify your strengths and pinpoint areas for professional growth with targeted quizzes.',
    link: '/toolkit/skill-assessments',
    bgColor: UNIFIED_BG_COLOR,
    iconBg: UNIFIED_ICON_BG,
    hoverBg: UNIFIED_HOVER_BG,
  },
  {
    icon: '🌐',
    title: 'Networking Email Templates',
    description: 'Build valuable professional connections effortlessly with proven email scripts and tips.',
    link: '/toolkit/networking-templates',
    bgColor: UNIFIED_BG_COLOR,
    iconBg: UNIFIED_ICON_BG,
    hoverBg: UNIFIED_HOVER_BG,
  },
  {
    icon: '📅',
    title: 'Job Application Tracker',
    description: 'Organize and manage all your job applications, interviews, and follow-ups efficiently.',
    link: '/toolkit/job-tracker',
    bgColor: UNIFIED_BG_COLOR,
    iconBg: UNIFIED_ICON_BG,
    hoverBg: UNIFIED_HOVER_BG,
  },
  {
    icon: '🗺️',
    title: 'Career Goal Planner',
    description: 'Map out your long-term career path with clear, actionable steps and milestone tracking.',
    link: '/toolkit/goal-planner',
    bgColor: UNIFIED_BG_COLOR,
    iconBg: UNIFIED_ICON_BG,
    hoverBg: UNIFIED_HOVER_BG,
  },
];

const CareerToolkit = () => {
  const UNSPLASH_IMAGE_URL = 'https://images.unsplash.com/photo-1517245386807-bb43f82c33d3?q=80&w=1470&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'; // Professional workspace/collaboration

  return (
    <main className="font-urbanist min-h-screen bg-gradient-to-br from-slate-50 to-gray-100 dark:from-gray-900 dark:to-slate-900">
      {/* Hero Section - Redesigned with Image */}
      <section className="relative py-28 lg:py-48 flex items-center justify-center text-center overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0 z-0">
         

<Waves
  lineColor="#fff"
  backgroundColor="rgba(255, 255, 255, 0.2)"
  waveSpeedX={0.02}
  waveSpeedY={0.01}
  waveAmpX={40}
  waveAmpY={20}
  friction={0.9}
  tension={0.01}
  maxCursorMove={120}
  xGap={12}
  yGap={36}
/>

          {/* Dark Overlay with subtle blue tint */}
          <div className="absolute inset-0 bg-gray-50 opacity-80 dark:bg-gray-950 dark:opacity-85"></div>
          
        </div>

        {/* Content */}
        <div className="mx-auto max-w-5xl px-4 sm:px-6 md:px-8 lg:px-16 xl:px-40 relative z-10  text-gray-700">
          <h1 className="font-nunito text-4xl  text-blue-600 font-extrabold tracking-tight sm:text-6xl lg:text-7xl mb-8 leading-tight drop-shadow-lg">
            Elevate Your Journey with the{' '}
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-blue-400">
              Ultimate Career Toolkit
            </span>
          </h1>
          <p className="text-xl dark:text-gray-300 lg:text-2xl font-light leading-relaxed mb-12 max-w-3xl mx-auto opacity-95 drop-shadow">
            A comprehensive suite of **intelligent, actionable tools** designed to empower every step of your professional path, from job search to leadership.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-6">
            <a
              href="#toolkit-sections"
              className="inline-flex items-center justify-center px-10 py-5 rounded-full bg-blue-600 text-gray-50 text-xl font-bold shadow-2xl transition-all duration-300 hover:bg-gray-100 hover:scale-105 focus:outline-none focus:ring-4 focus:ring-white/50"
            >
              <span className="mr-3 text-2xl ">🚀</span> Explore Your Toolkit
            </a>
          </div>

          <div className="mt-20 flex flex-col sm:flex-row justify-center gap-12 backdrop-blur-sm bg-white/10 dark:bg-gray-900/20 p-6 rounded-xl shadow-lg border border-white/20 dark:border-gray-700/50">
            <div className="text-center">
              <div className="text-5xl font-bold text-blue-600 mb-2">10+</div>
              <div className="text-base text-gray-700 mt-2 dark:text-gray-300">✨ Powerful Tools</div>
            </div>
            <div className="text-center">
              <div className="text-5xl font-bold text-blue-600 mb-2">Guided</div>
              <div className="text-base text-gray-700 mt-2 dark:text-gray-300">🧭 Career Pathing</div>
            </div>
          </div>
        </div>
      </section>

      {/* --- */}

      {/* Toolkit Sections - Refined Grid Layout */}
      <section id="toolkit-sections" className="py-24 lg:py-32 bg-gray-50 dark:bg-gray-900 relative">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 md:px-8 lg:px-16 xl:px-40 relative">
          <div className="text-center mb-20">
            <div className="inline-flex items-center gap-2 rounded-full bg-gradient-to-r from-blue-100 to-blue-100 dark:from-blue-900/50 dark:to-blue-900/50 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 mb-6">
              <span className="text-lg">⚙️</span>
              Your Arsenal for Success
            </div>
            <h2 className="font-nunito text-3xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl lg:text-5xl mb-4">
              Access Our <span className="text-blue-600">Powerful Tools</span>
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Each tool is meticulously designed for immediate value and tangible results, propelling your career forward.
            </p>
          </div>

          <div className="grid gap-8 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
            {careerTools.map((tool, index) => (
              <a
                key={index}
                href={tool.link}
                className={`group relative flex flex-col items-center rounded-xl ${tool.bgColor} p-6 text-center transition-all duration-300 hover:shadow-xl ${tool.hoverBg} border-2 border-gray-200 dark:border-gray-700`}
              >
                {/* Visual border on hover for consistency */}
                <div className="relative z-10 flex flex-col items-center h-full w-full">
                  <div
                    className={`mx-auto mb-5 flex h-16 w-16 items-center justify-center rounded-full ${tool.iconBg} shadow-md group-hover:shadow-lg transition-all duration-300`}
                  >
                    <span className="text-3xl">{tool.icon}</span>
                  </div>
                  <h3 className="font-nunito mb-2 text-xl font-bold text-gray-900 dark:text-white transition-colors">
                    {tool.title}
                  </h3>
                  <p className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed flex-grow mb-4">
                    {tool.description}
                  </p>
                  <span className="inline-flex items-center text-blue-600 dark:text-blue-400 text-sm font-medium transition-colors group-hover:text-blue-700 dark:group-hover:text-blue-300">
                    Use Tool <span className="ml-1">→</span>
                  </span>
                </div>
              </a>
            ))}
          </div>
        </div>
      </section>

      {/* --- */}

      {/* Benefits Section - Refined Layout */}
      <section className="py-20 lg:py-32 bg-gradient-to-br from-blue-50 via-gray-100 to-purple-50 dark:from-blue-900/30 dark:via-gray-900 dark:to-purple-900/30">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 md:px-8 lg:px-16 xl:px-40">
          <div className="text-center mb-20">
            <h2 className="font-nunito text-3xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl mb-4">
              Why Our <span className="text-blue-600">Toolkit</span> Stands Out
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              We empower your career journey with features designed for real-world impact and ease of use.
            </p>
          </div>
          <div className="grid gap-12 lg:grid-cols-2 items-center">
            {/* Left Side: Benefit List - Icons are now part of the text for subtlety */}
            <div className="space-y-8">
              <div className="flex items-start gap-4">
                <div className="text-blue-600 dark:text-blue-400 text-2xl flex-shrink-0 mt-1">💡</div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">Actionable Insights</h3>
                  <p className="text-gray-700 dark:text-gray-300">
                    Our tools don't just provide data; they give you clear, actionable steps to improve your career prospects.
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-4">
                <div className="text-blue-600 dark:text-blue-400 text-2xl flex-shrink-0 mt-1">⏳</div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">Time-Saving Efficiency</h3>
                  <p className="text-gray-700 dark:text-gray-300">
                    Streamline tedious tasks, freeing up your valuable time for strategic career moves and personal growth.
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-4">
                <div className="text-blue-600 dark:text-blue-400 text-2xl flex-shrink-0 mt-1">🔄</div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">Continuously Evolving</h3>
                  <p className="text-gray-700 dark:text-gray-300">
                    We regularly update and introduce new tools based on the latest industry trends and user feedback.
                  </p>
                </div>
              </div>
            </div>
            {/* Right Side: Visual Element / Image Placeholder - More integrated */}
            <div className="relative rounded-2xl bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-800/50 dark:to-blue-900/50 p-8 shadow-xl border border-blue-200 dark:border-blue-700">
              <img
                src="https://placehold.co/600x400/93c5fd/ffffff?text=Professional+Toolkit+Visual"
                alt="Toolkit Benefits"
                className="w-full h-auto rounded-lg shadow-lg opacity-80"
                onError={(e) => { e.currentTarget.src = `https://placehold.co/600x400/cccccc/333333?text=Benefits+Visual`; }}
              />
              <div className="absolute -bottom-4 left-1/2 -translate-x-1/2 bg-blue-600 text-white text-base px-5 py-2.5 rounded-full shadow-lg">
                Your Career, Amplified.
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* --- */}

      {/* Call to Action Section - Refined */}
      <section className="py-24 lg:py-32 text-center bg-gray-100 dark:bg-gray-800">
        <div className="mx-auto max-w-3xl px-4 sm:px-6 md:px-8 relative z-10">
          <h2 className="font-nunito text-3xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl mb-4">
            Ready to <span className="text-blue-600">Transform Your Career?</span>
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed mb-10">
            Take the first step towards your dream job and accelerate your professional journey with our powerful toolkit.
          </p>
          <a
            href="/toolkit/all-tools"
            className="inline-flex items-center justify-center px-10 py-4 rounded-lg bg-blue-600 text-white text-lg font-medium shadow-lg transition-all duration-300 hover:bg-blue-700 hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
          >
            <span className="mr-2">🚀</span> Access the Full Toolkit
          </a>
        </div>
      </section>

      {/* --- */}

      {/* FAQ Section */}
      <section id="faq" className="py-20 lg:py-32">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 md:px-8 lg:px-16 xl:px-40">
          <div className="text-center mb-16">
            <h2 className="font-nunito text-3xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl mb-4">
              Frequently Asked <span className="text-blue-600">Questions</span>
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Find quick answers to common questions about our Career Toolkit.
            </p>
          </div>
          <div className="max-w-3xl mx-auto space-y-6">
            {/* FAQ Item 1 */}
            <div className="rounded-xl bg-white dark:bg-gray-800 p-6 shadow-md border border-gray-200 dark:border-gray-700">
              <h3 className="font-semibold text-lg text-gray-900 dark:text-white mb-2">❓ Are the tools free to use?</h3>
              <p className="text-gray-700 dark:text-gray-300">
                Many of our core toolkit features are available for free. Premium features may require a subscription for full access.
              </p>
            </div>
            {/* FAQ Item 2 */}
            <div className="rounded-xl bg-white dark:bg-gray-800 p-6 shadow-md border border-gray-200 dark:border-gray-700">
              <h3 className="font-semibold text-lg text-gray-900 dark:text-white mb-2">🔒 Do I need to sign up to use the tools?</h3>
              <p className="text-gray-700 dark:text-gray-300">
                Some tools offer guest access, while others, especially those that save your progress (like the Job Application Tracker), require a quick sign-up for personalized experience.
              </p>
            </div>
            {/* FAQ Item 3 */}
            <div className="rounded-xl bg-white dark:bg-gray-800 p-6 shadow-md border border-gray-200 dark:border-gray-700">
              <h3 className="font-semibold text-lg text-gray-900 dark:text-white mb-2">📈 How do these tools benefit my career?</h3>
              <p className="text-gray-700 dark:text-gray-300">
                Our tools are designed to streamline your career process by providing instant feedback, organized workflows, and data-backed insights, helping you stand out and achieve your goals faster.
              </p>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
};

export default CareerToolkit;
