'use client';
import React from 'react';
import DocumentResource from '@/components/custom/documentResource';
// Define an interface for the shape of each resource card object
// interface ResourceCard {
//   icon: string; // Can be emoji (e.g., '📚') or image URL (e.g., for publisher logos)
//   title: string;
//   description: string;
//   link: string; // Direct download link or page link
//   bgColor: string;
//   iconBg: string; // Background for the icon circle
//   hoverBg: string;
//   resourceType: 'ebook' | 'pdf'; // Helps categorize
//   coverImage?: string; // Optional: for displaying a book cover thumbnail
// }

// Unified card colors
// const UNIFIED_BG_COLOR = 'from-blue-50 to-blue-100 dark:bg-gradient-to-r dark:from-indigo-100/10 dark:to-gray-200/10';
// const UNIFIED_ICON_BG = 'bg-blue-100 dark:bg-blue-900';
// const UNIFIED_HOVER_BG = 'hover:from-blue-100 hover:to-blue-200 dark:hover:from-blue-200/40 dark:hover:to-blue-400/40';


// const featuredEbooks: ResourceCard[] = [
//   {
//     icon: '📚', // Ebook icon
//     title: 'The Ultimate Job Search Handbook',
//     description: 'A comprehensive guide with strategies for a successful job hunt, resume optimization, and interview mastery. Download now to get started!',
//     link: '/downloads/job-search-handbook.pdf', // Dummy link
//     bgColor: UNIFIED_BG_COLOR,
//     iconBg: UNIFIED_ICON_BG,
//     hoverBg: UNIFIED_HOVER_BG,
//     resourceType: 'ebook',
//     coverImage: '/images/job search.png', // Placeholder for a book cover
//   },
//   {
//     icon: '📊', // Report icon
//     title: 'Industry Trends Report 2025',
//     description: 'Deep dive into emerging trends impacting various industries, including AI, remote work, and green tech. Essential reading for strategic career planning.',
//     link: '/downloads/industry-report-2025.pdf', // Dummy link
//     bgColor: UNIFIED_BG_COLOR,
//     iconBg: UNIFIED_ICON_BG,
//     hoverBg: UNIFIED_HOVER_BG,
//     resourceType: 'pdf',
//     coverImage: '/images/industry trend.png', // Placeholder for a book cover
//   },
//   {
//     icon: '⚙️', // Gears for skills
//     title: 'Advanced Interview Techniques',
//     description: 'Master negotiation, behavioral questions, and follow-up strategies to land your dream job.',
//     link: '/downloads/advanced-interview.pdf', // Dummy link
//     bgColor: UNIFIED_BG_COLOR,
//     iconBg: UNIFIED_ICON_BG,
//     hoverBg: UNIFIED_HOVER_BG,
//     resourceType: 'ebook',
//     coverImage: '/images/interview-techniques.png'
//   },
//   {
//     icon: '💡', // Lightbulb for tips
//     title: 'Personal Branding for Professionals',
//     description: 'Build a strong professional brand online and offline, leveraging social media and networking.',
//     link: '/downloads/personal-branding.pdf', // Dummy link
//     bgColor: UNIFIED_BG_COLOR,
//     iconBg: UNIFIED_ICON_BG,
//     hoverBg: UNIFIED_HOVER_BG,
//     resourceType: 'pdf',
//     coverImage: '/images/personal branding.png', // Placeholder for a book cover
//   },
// ];

// const allEbooksAndPdfs: ResourceCard[] = [
//   {
//     icon: '📈',
//     title: 'Career Pivoting Guide',
//     description: 'Navigate career changes successfully with our step-by-step guide.',
//     link: '/downloads/career-pivoting-guide.pdf',
//     bgColor: UNIFIED_BG_COLOR,
//     iconBg: UNIFIED_ICON_BG,
//     hoverBg: UNIFIED_HOVER_BG,
//     resourceType: 'ebook',
//   },
//   {
//     icon: '🧑‍💻',
//     title: 'The Developer\'s Career Path',
//     description: 'Roadmaps and advice for tech professionals across various specializations.',
//     link: '/downloads/developer-career-path.pdf',
//     bgColor: UNIFIED_BG_COLOR,
//     iconBg: UNIFIED_ICON_BG,
//     hoverBg: UNIFIED_HOVER_BG,
//     resourceType: 'ebook',
//   },
//   {
//     icon: '🗣️',
//     title: 'Effective Communication in the Workplace',
//     description: 'Improve your verbal and written communication for better teamwork.',
//     link: '/downloads/effective-communication.pdf',
//     bgColor: UNIFIED_BG_COLOR,
//     iconBg: UNIFIED_ICON_BG,
//     hoverBg: UNIFIED_HOVER_BG,
//     resourceType: 'pdf',
//   },
//   {
//     icon: '🧠',
//     title: 'Emotional Intelligence at Work',
//     description: 'Develop your EQ to enhance leadership and team collaboration.',
//     link: '/downloads/emotional-intelligence.pdf',
//     bgColor: UNIFIED_BG_COLOR,
//     iconBg: UNIFIED_ICON_BG,
//     hoverBg: UNIFIED_HOVER_BG,
//     resourceType: 'pdf',
//   },
//   {
//     icon: '🤝',
//     title: 'Negotiation Skills for Salary & Beyond',
//     description: 'Learn to negotiate effectively for better compensation and opportunities.',
//     link: '/downloads/negotiation-skills.pdf',
//     bgColor: UNIFIED_BG_COLOR,
//     iconBg: UNIFIED_ICON_BG,
//     hoverBg: UNIFIED_HOVER_BG,
//     resourceType: 'pdf',
//   },
//   {
//     icon: '🕰️',
//     title: 'Time Management for Professionals',
//     description: 'Strategies to boost productivity and manage your time effectively in a busy work environment.',
//     link: '/downloads/time-management.pdf',
//     bgColor: UNIFIED_BG_COLOR,
//     iconBg: UNIFIED_ICON_BG,
//     hoverBg: UNIFIED_HOVER_BG,
//     resourceType: 'ebook',
//   },
//   {
//     icon: '✨',
//     title: 'Building a Powerful LinkedIn Profile',
//     description: 'Optimize your LinkedIn profile to attract recruiters and expand your professional network.',
//     link: '/downloads/linkedin-profile.pdf',
//     bgColor: UNIFIED_BG_COLOR,
//     iconBg: UNIFIED_ICON_BG,
//     hoverBg: UNIFIED_HOVER_BG,
//     resourceType: 'pdf',
//   },
//   {
//     icon: '💼',
//     title: 'Remote Work Success Guide',
//     description: 'Tips and best practices for thriving in a remote or hybrid work setup.',
//     link: '/downloads/remote-work-guide.pdf',
//     bgColor: UNIFIED_BG_COLOR,
//     iconBg: UNIFIED_ICON_BG,
//     hoverBg: UNIFIED_HOVER_BG,
//     resourceType: 'ebook',
//   },
// ];


const EbooksPdfs = () => {
  return (
    <main className="font-urbanist min-h-screen bg-gradient-to-br from-slate-50 to-gray-100 dark:from-gray-900 dark:to-slate-900">
      {/* Hero Section - New Design */}
      <section className="relative py-24 lg:py-40 flex items-center justify-center text-center">
        <div className="absolute inset-0 bg-gradient-to-b from-blue-500/10 to-blue-700/10 dark:from-blue-900/20 dark:to-blue-950/20 opacity-70"></div>
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_top_right,rgba(59,130,246,0.1),transparent_50%)] dark:bg-[radial-gradient(circle_at_top_right,rgba(59,130,246,0.05),transparent_50%)] pointer-events-none"></div>
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_bottom_left,rgba(147,51,234,0.05),transparent_50%)] dark:bg-[radial-gradient(circle_at_bottom_left,rgba(147,51,234,0.03),transparent_50%)] pointer-events-none"></div>

        <div className="mx-auto max-w-4xl px-4 sm:px-6 md:px-8 lg:px-16 xl:px-40 relative z-10">
          <h1 className="font-nunito text-4xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-5xl lg:text-6xl mb-6">
            Unlock Your Potential with <span className="text-blue-600">Ebooks & PDFs</span>
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 leading-relaxed mb-10">
            Dive into our expertly crafted, downloadable resources designed to accelerate your career growth.
            From in-depth guides to essential tools, **everything you need is just a click away.**
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <a
              href="#featured-ebooks"
              className="inline-flex items-center justify-center px-8 py-4 rounded-lg bg-blue-600 text-white text-lg font-medium shadow-lg transition-all duration-300 hover:bg-blue-700 hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900"
            >
              <span className="mr-2">📚</span> Browse Ebooks
            </a>
            <a
              href="#faq"
              className="inline-flex items-center justify-center px-8 py-4 rounded-lg border border-gray-300 dark:border-gray-700 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 text-lg font-medium shadow-lg transition-all duration-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-gray-200 focus:ring-offset-2 dark:focus:ring-offset-gray-900"
            >
              Have Questions?
            </a>
          </div>

          <div className="mt-20 flex justify-center gap-8">
            <div className="text-center">
              <div className="text-4xl font-bold text-blue-600 dark:text-blue-400">75+</div>
              <div className="text-base text-gray-600 dark:text-gray-400 mt-2">Premium Resources</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-blue-600 dark:text-blue-400">10k+</div>
              <div className="text-base text-gray-600 dark:text-gray-400 mt-2">Downloads Monthly</div>
            </div>
          </div>
        </div>
      </section>

      {/* --- */}

      {/* Featured Ebooks Section - Updated Layout */}
      <section id="featured-ebooks" className="py-24 lg:py-32 bg-white dark:bg-gray-800 relative">
        <div className="absolute inset-0 bg-gradient-to-br from-slate-50 to-gray-100 dark:from-gray-900 dark:to-slate-900"></div>
        <div className="mx-auto max-w-7xl px-4 sm:px-6 md:px-8 lg:px-16 xl:px-40 relative">
          <div className="text-center mb-20">
            <div className="inline-flex items-center gap-2 rounded-full bg-gradient-to-r from-blue-100 to-blue-100 dark:from-blue-900/50 dark:to-blue-900/50 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 mb-6">
              <span className="text-lg">✨</span>
              Popular Downloads
            </div>
            <h2 className="font-nunito text-3xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl lg:text-5xl mb-4">
              Our <span className="text-blue-600">Featured Ebooks</span>
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Discover our most popular and impactful downloadable resources, designed to give you an edge.
            </p>
          </div>

          {/* 2x2 Grid for Featured Ebooks */}
          < DocumentResource showFeaturedOnly = {true}/>
          {/* <div className="grid gap-8  sm:grid-cols-2 lg:grid-cols-3">
            {featuredEbooks.map((resource, index) => (
              <a
                key={index}
                href={resource.link}
                className={`group relative flex flex-col items-center justify-center rounded-2xl bg-gradient-to-br ${resource.bgColor} p-6 text-center transition-all duration-500 hover:shadow-xl hover:scale-105 ${resource.hoverBg} border border-white/50 dark:border-gray-700/50 backdrop-blur-sm`}
                target={resource.link.startsWith('/') ? '_self' : '_blank'}
                rel={resource.link.startsWith('/') ? '' : 'noopener noreferrer'}
                download={resource.link.endsWith('.pdf')}
              >
                <div className="absolute  inset-0 rounded-2xl bg-gradient-to-br from-white/20 to-transparent dark:from-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="relative z-10">
                  {resource.coverImage && (
                    <div className="mx-auto mb-4 flex h-40 w-32 items-center justify-center rounded-lg overflow-hidden shadow-lg group-hover:shadow-xl transition-all duration-500 group-hover:scale-110">
                      <img
                        src={resource.coverImage}
                        alt={`${resource.title} Cover`}
                        className="w-full h-full object-cover"
                        onError={(e) => { e.currentTarget.src = `https://placehold.co/128x160/cccccc/333333?text=Cover+Not+Found`; }}
                      />
                    </div>
                  )}
                  {!resource.coverImage && ( // Fallback for if no cover image
                    <div
                      className={`mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-2xl ${resource.iconBg} shadow-lg group-hover:shadow-xl transition-all duration-500 group-hover:scale-110`}
                    >
                      <span className="text-2xl">{resource.icon}</span>
                    </div>
                  )}
                  <h3 className="font-nunito mb-3 text-lg font-bold text-gray-900 dark:text-white group-hover:text-gray-800 dark:group-hover:text-gray-200 transition-colors">
                    {resource.title}
                  </h3>
                  <p className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed truncate-3-lines">
                    {resource.description}
                  </p>
                  <span className="mt-4 inline-flex items-center text-blue-600 dark:text-blue-400 text-sm font-medium group-hover:text-blue-700 dark:group-hover:text-blue-300 transition-colors">
                    Download <span className="ml-1">↓</span>
                  </span>
                </div>
              </a>
            ))}
          </div> */}
        </div>
      </section>

      {/* --- */}

      {/* All Ebooks & PDFs Section */}
      <section className="py-24 lg:py-32 bg-gray-50 dark:bg-gray-900 relative">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(59,130,246,0.05),transparent_50%)] dark:bg-[radial-gradient(circle_at_30%_20%,rgba(59,130,246,0.03),transparent_50%)] pointer-events-none"></div>
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(147,51,234,0.05),transparent_50%)] dark:bg-[radial-gradient(circle_at_70%_80%,rgba(147,51,234,0.03),transparent_50%)] pointer-events-none"></div>

        <div className="mx-auto max-w-7xl px-4 sm:px-6 md:px-8 lg:px-16 xl:px-40 relative text-center">
          <div className="text-center mb-20">
            <div className="inline-flex items-center gap-2 rounded-full bg-gradient-to-r from-blue-100 to-green-100 dark:from-blue-900/50 dark:to-green-900/50 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 mb-6">
              <span className="text-lg">📖</span>
              Comprehensive Library
            </div>
            <h2 className="font-nunito text-3xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl lg:text-5xl mb-4">
              Explore All <span className="text-blue-600">Ebooks & PDFs</span>
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Browse our full collection of downloadable resources to find exactly what you need for your career growth.
            </p>
          </div>
          <DocumentResource />
          {/* <div className="grid gap-8 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
            {allEbooksAndPdfs.map((resource, index) => (
              <a
                key={index}
                href={resource.link}
                className={`group relative flex flex-col items-center  rounded-2xl bg-gradient-to-br ${resource.bgColor} p-6 text-center transition-all duration-500 hover:shadow-xl hover:scale-105 ${resource.hoverBg} border border-white/50 dark:border-gray-700/50 backdrop-blur-sm`}
                target={resource.link.startsWith('/') ? '_self' : '_blank'}
                rel={resource.link.startsWith('/') ? '' : 'noopener noreferrer'}
                download={resource.link.endsWith('.pdf')}
              >
                <div className="absolute  inset-0 rounded-2xl bg-gradient-to-br from-white/20 to-transparent dark:from-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="relative z-10">
                  <div
                    className={`mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-2xl ${resource.iconBg} shadow-lg group-hover:shadow-xl transition-all duration-500 group-hover:scale-110`}
                  >
                    {resource.coverImage ? (
                       <img
                         src={resource.coverImage}
                         alt={`${resource.title} Cover`}
                         className="w-full h-full object-cover rounded-lg"
                         onError={(e) => { e.currentTarget.src = `https://placehold.co/64x64/cccccc/333333?text=Cover`; }}
                       />
                    ) : (
                      <span className="text-2xl">{resource.icon}</span>
                    )}
                  </div>
                  <h3 className="font-nunito mb-3 text-lg font-bold text-gray-900 dark:text-white group-hover:text-gray-800 dark:group-hover:text-gray-200 transition-colors">
                    {resource.title}
                  </h3>
                  <p className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed truncate-3-lines">
                    {resource.description}
                  </p>
                  <span className="mt-4 inline-flex items-center text-blue-600 dark:text-blue-400 text-sm font-medium group-hover:text-blue-700 dark:group-hover:text-blue-300 transition-colors">
                    Download <span className="ml-1">↓</span>
                  </span>
                </div>
              </a>
            ))}
          </div> */}
        </div>
      </section>

      {/* --- */}

      {/* Benefits Section */}
      <section className="py-20 lg:py-32">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 md:px-8 lg:px-16 xl:px-40">
          <div className="text-center mb-16">
            <h2 className="font-nunito text-3xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl mb-4">
              Why Download Our <span className="text-blue-600">Resources?</span>
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Our downloadable resources are designed to provide unparalleled convenience and depth for your learning.
            </p>
          </div>
          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            <div className="rounded-2xl bg-gradient-to-br from-indigo-50 to-purple-100 dark:from-indigo-900/30 dark:to-purple-900/30 p-8 shadow-lg text-center">
              <div className="text-4xl mb-4">✈️</div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">Offline Access</h3>
              <p className="text-gray-700 dark:text-gray-300">
                Download and read anytime, anywhere, without needing an internet connection.
              </p>
            </div>
            <div className="rounded-2xl bg-gradient-to-br from-yellow-50 to-amber-100 dark:from-yellow-900/30 dark:to-amber-900/30 p-8 shadow-lg text-center">
              <div className="text-4xl mb-4">📖</div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">In-Depth Content</h3>
              <p className="text-gray-700 dark:text-gray-300">
                Explore topics thoroughly with detailed explanations and actionable insights.
              </p>
            </div>
            <div className="rounded-2xl bg-gradient-to-br from-pink-50 to-red-100 dark:from-pink-900/30 dark:to-red-800/30 p-8 shadow-lg text-center">
              <div className="text-4xl mb-4">✅</div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">Expert-Curated</h3>
              <p className="text-gray-700 dark:text-gray-300">
                Content is vetted and developed by experienced professionals in their fields.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* --- */}

      {/* FAQ Section */}
      <section id="faq" className="py-20 lg:py-32">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 md:px-8 lg:px-16 xl:px-40">
          <div className="text-center mb-16">
            <h2 className="font-nunito text-3xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl mb-4">
              Frequently Asked <span className="text-blue-600">Questions</span>
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Find quick answers to common questions about our downloadable resources.
            </p>
          </div>
          <div className="max-w-3xl mx-auto space-y-6">
            {/* FAQ Item 1 */}
            <div className="rounded-xl bg-white dark:bg-gray-800 p-6 shadow-md border border-gray-200 dark:border-gray-700">
              <h3 className="font-semibold text-lg text-gray-900 dark:text-white mb-2">How do I download an ebook?</h3>
              <p className="text-gray-700 dark:text-gray-300">
                Simply click on the "Download Ebook" or "Download PDF" button on any resource card, and the download will begin automatically.
              </p>
            </div>
            {/* FAQ Item 2 */}
            <div className="rounded-xl bg-white dark:bg-gray-800 p-6 shadow-md border border-gray-200 dark:border-gray-700">
              <h3 className="font-semibold text-lg text-gray-900 dark:text-white mb-2">Are the ebooks free?</h3>
              <p className="text-gray-700 dark:text-gray-300">
                Yes, all ebooks and PDF resources provided on this page are completely free to download and use for your personal career development.
              </p>
            </div>
            {/* FAQ Item 3 */}
            <div className="rounded-xl bg-white dark:bg-gray-800 p-6 shadow-md border border-gray-200 dark:border-gray-700">
              <h3 className="font-semibold text-lg text-gray-900 dark:text-white mb-2">Can I share these resources?</h3>
              <p className="text-gray-700 dark:text-gray-300">
                You are welcome to share these resources for personal and non-commercial use. Please refer to each document's specific licensing for details.
              </p>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
};

export default EbooksPdfs;