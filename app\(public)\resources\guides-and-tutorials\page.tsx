'use client';
import React from 'react';
import ArticleResource from '@/components/custom/articlesResource';
import VideoResource from '@/components/custom/videoResource';
import CourseResources from '@/components/custom/courseResource';

// Define an interface for the shape of each resource card object
interface ResourceCard {
  icon: string; // Can be emoji (e.g., '📝') or image URL (e.g., 'https://...')
  title: string;
  description: string;
  link: string; // Main link for the resource (article URL, video URL, website URL)
  bgColor: string;
  iconBg: string; // Background for the icon circle
  hoverBg: string;
  thumbnail?: string; // Optional: for video previews
  resourceType: 'guide' | 'video' | 'website'; // Helps categorize and render
}

const featuredResources: ResourceCard[] = [
  {
    icon: '📝',
    title: 'Mastering Your Resume',
    description: 'Transform your CV into a powerful tool that gets you noticed.',
    link: '/guides/resume-mastery',
    bgColor: 'from-orange-50 to-orange-100 dark:from-orange-900/30 dark:to-orange-800/30',
    iconBg: 'bg-orange-100 dark:bg-orange-900',
    hoverBg: 'hover:from-orange-100 hover:to-orange-200 dark:hover:from-orange-800/40 dark:hover:to-orange-700/40',
    resourceType: 'guide',
  },
  {
    icon: '💬',
    title: 'Ace Your Next Interview',
    description: 'Strategies and tips for confident and impactful interviews.',
    link: '/guides/interview-success',
    bgColor: 'from-blue-50 to-blue-100 dark:from-blue-900/30 dark:to-blue-800/30',
    iconBg: 'bg-blue-100 dark:bg-blue-900',
    hoverBg: 'hover:from-blue-100 hover:to-blue-200 dark:hover:from-blue-800/40 dark:hover:to-blue-700/40',
    resourceType: 'guide',
  },
  {
    icon: '🚀',
    title: 'Unlocking Career Growth',
    description: 'Pathways and insights for long-term professional development.',
    link: '/guides/career-growth',
    bgColor: 'from-green-50 to-green-100 dark:from-green-900/30 dark:to-green-800/30',
    iconBg: 'bg-green-100 dark:bg-green-900',
    hoverBg: 'hover:from-green-100 hover:to-green-200 dark:hover:from-green-800/40 dark:hover:to-green-700/40',
    resourceType: 'guide',
  },
  {
    icon: '💡',
    title: 'Essential Tech Skills',
    description: 'Dive into foundational tech knowledge and practical skills.',
    link: '/guides/tech-essentials',
    bgColor: 'from-purple-50 to-purple-100 dark:from-purple-900/30 dark:to-purple-800/30',
    iconBg: 'bg-purple-100 dark:bg-purple-900',
    hoverBg: 'hover:from-purple-100 hover:to-purple-200 dark:hover:from-purple-800/40 dark:hover:to-purple-700/40',
    resourceType: 'guide',
  },
];

const YOUTUBE_LOGO_URL = 'https://upload.wikimedia.org/wikipedia/commons/thumb/0/09/YouTube_full-color_icon_%282017%29.svg/256px-YouTube_full-color_icon_%282017%29.svg.png';

const youtubeVideos: ResourceCard[] = [
  {
    icon: YOUTUBE_LOGO_URL,
    title: 'How to Write a Perfect Resume (Video)',
    description: 'A step-by-step video guide to creating a resume that lands interviews.',
    thumbnail: 'https://i.ytimg.com/vi/dQw4w9WgXcQ/hqdefault.jpg', // Dummy thumbnail
    link: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', // Dummy link (Rick Astley - Never Gonna Give You Up)
    bgColor: 'from-red-50 to-red-100 dark:from-red-900/30 dark:to-red-800/30',
    iconBg: 'bg-red-100 dark:bg-red-900', // YouTube red background
    hoverBg: 'hover:from-red-100 hover:to-red-200 dark:hover:from-red-800/40 dark:hover:to-red-700/40',
    resourceType: 'video',
  },
  {
    icon: YOUTUBE_LOGO_URL,
    title: 'Interview Skills: Top 10 Questions (Video)',
    description: 'Video tutorial on preparing for common interview questions and impressing recruiters.',
    thumbnail: 'https://i.ytimg.com/vi/QxP1-ggeF1Y/hqdefault.jpg', // Dummy thumbnail (another random ID)
    link: 'https://www.youtube.com/watch?v=QxP1-ggeF1Y', // Dummy link
    bgColor: 'from-yellow-50 to-yellow-100 dark:from-yellow-900/30 dark:to-yellow-800/30',
    iconBg: 'bg-yellow-100 dark:bg-yellow-900', // Different background for variety
    hoverBg: 'hover:from-yellow-100 hover:to-yellow-200 dark:hover:from-yellow-800/40 dark:hover:to-yellow-700/40',
    resourceType: 'video',
  },
  {
    icon: YOUTUBE_LOGO_URL,
    title: 'Networking for Career Success (Video)',
    description: 'Visual guide to building valuable connections and opening new career opportunities.',
    thumbnail: 'https://i.ytimg.com/vi/t8b1Bw00l0M/hqdefault.jpg', // Dummy thumbnail
    link: 'https://www.youtube.com/watch?v=t8b1Bw00l0M', // Dummy link
    bgColor: 'from-pink-50 to-pink-100 dark:from-pink-900/30 dark:to-pink-800/30',
    iconBg: 'bg-pink-100 dark:bg-pink-900', // Different background for variety
    hoverBg: 'hover:from-pink-100 hover:to-pink-200 dark:hover:from-pink-800/40 dark:hover:to-pink-700/40',
    resourceType: 'video',
  },
  {
    icon: YOUTUBE_LOGO_URL,
    title: 'Landing Your First Tech Job (Video)',
    description: 'Video: Essential tips for aspiring developers and tech professionals.',
    thumbnail: 'https://i.ytimg.com/vi/t8b1Bw00l0M/hqdefault.jpg', // Dummy thumbnail (using same for simplicity)
    link: 'https://www.youtube.com/watch?v=t8b1Bw00l0M', // Dummy link
    bgColor: 'from-teal-50 to-teal-100 dark:from-teal-900/30 dark:to-teal-800/30',
    iconBg: 'bg-teal-100 dark:bg-teal-900', // Different background for variety
    hoverBg: 'hover:from-teal-100 hover:to-teal-200 dark:hover:from-teal-800/40 dark:hover:to-teal-700/40',
    resourceType: 'video',
  },
];

const websiteTutorials: ResourceCard[] = [
  {
    icon: '💻', // Web icon
    title: 'The Modern Frontend Web Dev Handbook',
    description: 'A comprehensive guide to building responsive and interactive web interfaces.',
    link: 'https://www.dummywebsite.com/frontend-handbook',
    bgColor: 'from-sky-50 to-cyan-100 dark:from-sky-900/30 dark:to-cyan-800/30',
    iconBg: 'bg-sky-100 dark:bg-sky-900',
    hoverBg: 'hover:from-sky-100 hover:to-cyan-200 dark:hover:from-sky-800/40 dark:hover:to-cyan-700/40',
    resourceType: 'website',
  },
  {
    icon: '📊', // Data analytics icon
    title: 'Introduction to Data Analytics',
    description: 'Learn the basics of data collection, analysis, and visualization.',
    link: 'https://www.dummywebsite.com/data-analytics-intro',
    bgColor: 'from-lime-50 to-emerald-100 dark:from-lime-900/30 dark:to-emerald-800/30',
    iconBg: 'bg-lime-100 dark:bg-lime-900',
    hoverBg: 'hover:from-lime-100 hover:to-emerald-200 dark:hover:from-lime-800/40 dark:hover:to-emerald-700/40',
    resourceType: 'website',
  },
  {
    icon: '☁️', // Cloud icon
    title: 'Cloud Career Pathways Explained',
    description: 'Understand different roles and certifications in cloud computing.',
    link: 'https://www.dummywebsite.com/cloud-careers',
    bgColor: 'from-fuchsia-50 to-rose-100 dark:from-fuchsia-900/30 dark:to-rose-800/30',
    iconBg: 'bg-fuchsia-100 dark:bg-fuchsia-900',
    hoverBg: 'hover:from-fuchsia-100 hover:to-rose-200 dark:hover:from-fuchsia-800/40 dark:hover:to-rose-700/40',
    resourceType: 'website',
  },
  {
    icon: '✍️', // Writing icon
    title: 'Crafting Professional Emails',
    description: 'Master the art of clear, concise, and impactful professional communication.',
    link: 'https://www.dummywebsite.com/professional-emails',
    bgColor: 'from-gray-50 to-gray-100 dark:from-gray-700/30 dark:to-gray-600/30',
    iconBg: 'bg-gray-100 dark:bg-gray-700',
    hoverBg: 'hover:from-gray-100 hover:to-gray-200 dark:hover:from-gray-600/40 dark:hover:to-gray-500/40',
    resourceType: 'website',
  },
];

const GuidesTutorials = () => {
  return (
    <main className="font-urbanist min-h-screen bg-gradient-to-br from-slate-50 to-gray-100 dark:from-gray-900 dark:to-slate-900">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 md:px-8 lg:px-16 xl:px-40">
          <div className="grid items-center gap-12 lg:grid-cols-2">
            <div className="space-y-8">
              <div className="space-y-6">
                <h1 className="font-nunito text-4xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-5xl lg:text-6xl">
                  Your <span className="text-blue-600">Career Playbook</span>
                </h1>
                <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed">
                  Unlock your full potential with our curated collection of **guides, tutorials, and expert advice**. From crafting a standout resume to mastering interview techniques, we provide the **essential resources** you need to navigate your career journey with confidence.
                </p>
                <div className="flex gap-4">
                  <a
                    href="#featured-guides"
                    className="inline-flex items-center justify-center px-6 py-3 rounded-lg bg-blue-600 text-white font-medium shadow-sm transition-all duration-300 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900"
                  >
                    Explore Guides
                  </a>
                  <a
                    href="#faq"
                    className="inline-flex items-center justify-center px-6 py-3 rounded-lg border border-gray-300 dark:border-gray-700 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 font-medium shadow-sm transition-all duration-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-200 focus:ring-offset-2 dark:focus:ring-offset-gray-900"
                  >
                    Have Questions?
                  </a>
                </div>
              </div>
            </div>
            <div className="relative">
              <div className="absolute -inset-4 rounded-2xl bg-gradient-to-r from-blue-400 to-blue-600 opacity-20 blur-lg"></div>
              <div className="relative rounded-2xl bg-white dark:bg-gray-800 p-8 shadow-xl">
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-blue-600 dark:text-blue-400">100+</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">In-depth Articles</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-blue-600 dark:text-blue-400">50+</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">Practical Tutorials</div>
                  </div>
                </div>
                <div className="text-center mt-6">
                  <div className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-100 to-green-100 dark:from-blue-900/50 dark:to-green-900/50 rounded-full px-4 py-2">
                    <span className="text-lg">📚</span>
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Your Go-To Career Resource
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* --- */}

      {/* Featured Guides Section */}
      <ArticleResource />
      {/* <section id="featured-guides" className="py-24 lg:py-32 bg-white dark:bg-gray-800 relative">
        <div className="absolute inset-0 bg-gradient-to-b from-gray-50/50 to-transparent dark:from-gray-700/50"></div>
        <div className="mx-auto max-w-7xl px-4 sm:px-6 md:px-8 lg:px-16 xl:px-40 relative">
          <div className="text-center mb-20">
            <div className="inline-flex items-center gap-2 rounded-full bg-gradient-to-r from-blue-100 to-blue-100 dark:from-blue-900/50 dark:to-blue-900/50 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 mb-6">
              <span className="text-lg">✨</span>
              Start Here!
            </div>
            <h2 className="font-nunito text-3xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl lg:text-5xl mb-4">
              Our <span className="text-blue-600">Most Popular</span> Guides
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Dive into our top-rated resources designed to give you an immediate advantage in your job search and career development.
            </p>
          </div>
          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
            {featuredResources.map((resource, index) => (
              <a
                key={index}
                href={resource.link}
                className={`group relative flex flex-col items-center rounded-2xl bg-gradient-to-br ${resource.bgColor} p-6 text-center transition-all duration-500 hover:shadow-xl hover:scale-105 ${resource.hoverBg} border border-white/50 dark:border-gray-700/50 backdrop-blur-sm`}
                target={resource.link.startsWith('/') ? '_self' : '_blank'} // Open external links in new tab
                rel={resource.link.startsWith('/') ? '' : 'noopener noreferrer'}
              >
                <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-white/20 to-transparent dark:from-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="relative z-10">
                  <div
                    className={`mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-2xl ${resource.iconBg} shadow-lg group-hover:shadow-xl transition-all duration-500 group-hover:scale-110`}
                  >
                    <span className="text-2xl">{resource.icon}</span>
                  </div>
                  <h3 className="font-nunito mb-3 text-lg font-bold text-gray-900 dark:text-white group-hover:text-gray-800 dark:group-hover:text-gray-200 transition-colors">
                    {resource.title}
                  </h3>
                  <p className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
                    {resource.description}
                  </p>
                  <span className="mt-4 inline-flex items-center text-blue-600 dark:text-blue-400 text-sm font-medium group-hover:text-blue-700 dark:group-hover:text-blue-300 transition-colors">
                    Read More <span className="ml-1">→</span>
                  </span>
                </div>
              </a>
            ))}
          </div>
        </div>
      </section> */}

      {/* --- */}

      {/* YouTube Resources Section */}
      <VideoResource />
      {/* <section className="py-24 lg:py-32 bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30 dark:from-gray-900 dark:via-blue-900/10 dark:to-purple-900/10 relative overflow-hidden">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(59,130,246,0.1),transparent_50%)] dark:bg-[radial-gradient(circle_at_30%_20%,rgba(59,130,246,0.05),transparent_50%)] pointer-events-none"></div>
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(147,51,234,0.1),transparent_50%)] dark:bg-[radial-gradient(circle_at_70%_80%,rgba(147,51,234,0.05),transparent_50%)] pointer-events-none"></div>

        <div className="mx-auto max-w-7xl px-4 sm:px-6 md:px-8 lg:px-16 xl:px-40 relative">
          <div className="text-center mb-20">
            <div className="inline-flex items-center gap-2 rounded-full bg-gradient-to-r from-red-100 to-orange-100 dark:from-red-900/50 dark:to-orange-900/50 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 mb-6">
              <img src={YOUTUBE_LOGO_URL} alt="YouTube Logo" className="w-5 h-5 object-contain" />
              Watch & Learn
            </div>
            <h2 className="font-nunito text-3xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl lg:text-5xl mb-4">
              Explore <span className="text-blue-600">Video Tutorials</span>
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Prefer visual learning? Explore our video tutorials covering essential career topics from our YouTube channel.
            </p>
          </div>
          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
            {youtubeVideos.map((video, index) => (
              <a
                key={index}
                href={video.link} // Use video.link directly
                target="_blank"
                rel="noopener noreferrer"
                className={`group relative flex flex-col rounded-2xl bg-gradient-to-br ${video.bgColor} p-6 text-center transition-all duration-500 hover:shadow-xl hover:scale-105 ${video.hoverBg} border border-white/50 dark:border-gray-700/50 backdrop-blur-sm`}
              >
                <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-white/20 to-transparent dark:from-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="relative z-10">
                  <div
                    className={`mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-2xl ${video.iconBg} shadow-lg group-hover:shadow-xl transition-all duration-500 group-hover:scale-110`}
                  >
                    <img src={video.icon} alt="YouTube icon" className="w-8 h-8 object-contain" />
                  </div>
                  <h3 className="font-nunito mb-3 text-lg font-bold text-gray-900 dark:text-white group-hover:text-gray-800 dark:group-hover:text-gray-200 transition-colors">
                    {video.title}
                  </h3>
                  <p className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed flex-grow">
                    {video.description}
                  </p>
                 
                  <span className="mt-4 inline-flex items-center text-blue-600 dark:text-blue-400 text-sm font-medium group-hover:text-blue-700 dark:group-hover:text-blue-300 transition-colors">
                    Watch Now <span className="ml-1">→</span>
                  </span>
                </div>
              </a>
            ))}
          </div>
        </div>
      </section> */}

      {/* --- */}
      <CourseResources />
      {/* Website Tutorial Resources Section */}
      {/* <section className="py-24 lg:py-32 bg-white dark:bg-gray-800 relative">
        <div className="absolute inset-0 bg-gradient-to-b from-gray-50/50 to-transparent dark:from-gray-700/50"></div>
        <div className="mx-auto max-w-7xl px-4 sm:px-6 md:px-8 lg:px-16 xl:px-40 relative">
          <div className="text-center mb-20">
            <div className="inline-flex items-center gap-2 rounded-full bg-gradient-to-r from-teal-100 to-emerald-100 dark:from-teal-900/50 dark:to-emerald-900/50 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 mb-6">
              <span className="text-lg">🌐</span>
              External Learning
            </div>
            <h2 className="font-nunito text-3xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl lg:text-5xl mb-4">
              Explore <span className="text-blue-600">Website Tutorials</span>
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Access comprehensive guides and articles from leading external resources to broaden your knowledge.
            </p>
          </div>
          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
            {websiteTutorials.map((resource, index) => (
              <a
                key={index}
                href={resource.link}
                target="_blank" // Open in new tab
                rel="noopener noreferrer" // Security best practice
                className={`group relative flex flex-col items-center rounded-2xl bg-gradient-to-br ${resource.bgColor} p-6 text-center transition-all duration-500 hover:shadow-xl hover:scale-105 ${resource.hoverBg} border border-white/50 dark:border-gray-700/50 backdrop-blur-sm`}
              >
                <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-white/20 to-transparent dark:from-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="relative z-10">
                  <div
                    className={`mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-2xl ${resource.iconBg} shadow-lg group-hover:shadow-xl transition-all duration-500 group-hover:scale-110`}
                  >
                    <span className="text-2xl">{resource.icon}</span>
                  </div>
                  <h3 className="font-nunito mb-3 text-lg font-bold text-gray-900 dark:text-white group-hover:text-gray-800 dark:group-hover:text-gray-200 transition-colors">
                    {resource.title}
                  </h3>
                  <p className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
                    {resource.description}
                  </p>
                  <span className="mt-4 inline-flex items-center text-blue-600 dark:text-blue-400 text-sm font-medium group-hover:text-blue-700 dark:group-hover:text-blue-300 transition-colors">
                    Visit Site <span className="ml-1">→</span>
                  </span>
                </div>
              </a>
            ))}
          </div>
        </div>
      </section> */}

      {/* --- */}

      {/* Benefits Section */}
      <section className="py-20 lg:py-32">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 md:px-8 lg:px-16 xl:px-40">
          <div className="text-center mb-16">
            <h2 className="font-nunito text-3xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl mb-4">
              Why Our <span className="text-blue-600">Resources</span> Stand Out
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              We're committed to providing actionable, up-to-date, and easy-to-understand content that genuinely helps you succeed.
            </p>
          </div>
          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            <div className="rounded-2xl bg-gradient-to-br from-indigo-50 to-purple-100 dark:from-indigo-900/30 dark:to-purple-900/30 p-8 shadow-lg text-center">
              <div className="text-4xl mb-4">✅</div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">Actionable Insights</h3>
              <p className="text-gray-700 dark:text-gray-300">
                Our guides are packed with practical steps and real-world examples you can apply immediately.
              </p>
            </div>
            <div className="rounded-2xl bg-gradient-to-br from-yellow-50 to-amber-100 dark:from-yellow-900/30 dark:to-amber-900/30 p-8 shadow-lg text-center">
              <div className="text-4xl mb-4">🔄</div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">Always Up-to-Date</h3>
              <p className="text-gray-700 dark:text-gray-300">
                We regularly update our content to reflect the latest industry trends and hiring practices.
              </p>
            </div>
            <div className="rounded-2xl bg-gradient-to-br from-pink-50 to-red-100 dark:from-pink-900/30 dark:to-red-900/30 p-8 shadow-lg text-center">
              <div className="text-4xl mb-4">🤝</div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">Expert-Backed Advice</h3>
              <p className="text-gray-700 dark:text-gray-300">
                Benefit from strategies developed by experienced career coaches and industry professionals.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* --- */}

      {/* Explore All Resources CTA Section */}
      <section className="py-24 lg:py-32 bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30 dark:from-gray-900 dark:via-blue-900/10 dark:to-purple-900/10 relative overflow-hidden">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(59,130,246,0.1),transparent_50%)] dark:bg-[radial-gradient(circle_at_30%_20%,rgba(59,130,246,0.05),transparent_50%)] pointer-events-none"></div>
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(147,51,234,0.1),transparent_50%)] dark:bg-[radial-gradient(circle_at_70%_80%,rgba(147,51,234,0.05),transparent_50%)] pointer-events-none"></div>

        <div className="mx-auto max-w-7xl px-4 sm:px-6 md:px-8 lg:px-16 xl:px-40 relative text-center">
          <div className="mb-16">
            <h2 className="font-nunito text-3xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl lg:text-5xl mb-4">
              Ready to <span className="text-blue-600">Deep Dive?</span>
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Our full library of guides and tutorials is waiting for you. Explore every aspect of career development, from job search basics to advanced professional strategies.
            </p>
          </div>

          <a
            href="/resources" // Link to a page listing all guides
            className="inline-flex items-center justify-center px-8 py-4 rounded-lg bg-gray-900 dark:bg-gray-100 text-white dark:text-gray-900 font-medium shadow-sm transition-all duration-300 hover:bg-gray-800 dark:hover:bg-gray-200 hover:shadow-md focus:outline-none focus:ring-1 focus:ring-gray-500/20 dark:focus:ring-gray-400/20"
          >
            <span className="mr-2">📚</span> View All Resources
          </a>
        </div>
      </section>

      {/* --- */}

      {/* FAQ Section */}
      <section id="faq" className="py-20 lg:py-32">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 md:px-8 lg:px-16 xl:px-40">
          <div className="text-center mb-16">
            <h2 className="font-nunito text-3xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl mb-4">
              Frequently Asked <span className="text-blue-600">Questions</span>
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Find quick answers to common questions about our guides and how to best use them.
            </p>
          </div>
          <div className="max-w-3xl mx-auto space-y-6">
            {/* FAQ Item 1 */}
            <div className="rounded-xl bg-white dark:bg-gray-800 p-6 shadow-md border border-gray-200 dark:border-gray-700">
              <h3 className="font-semibold text-lg text-gray-900 dark:text-white mb-2">How often are new guides published?</h3>
              <p className="text-gray-700 dark:text-gray-300">
                We strive to add new guides and tutorials regularly, typically on a bi-weekly basis, to keep our content fresh and relevant.
              </p>
            </div>
            {/* FAQ Item 2 */}
            <div className="rounded-xl bg-white dark:bg-gray-800 p-6 shadow-md border border-gray-200 dark:border-gray-700">
              <h3 className="font-semibold text-lg text-gray-900 dark:text-white mb-2">Are these guides suitable for all career levels?</h3>
              <p className="text-gray-700 dark:text-gray-300">
                Yes, our guides cover a wide range of topics, from entry-level job search strategies to advanced leadership development, catering to all career stages.
              </p>
            </div>
            {/* FAQ Item 3 */}
            <div className="rounded-xl bg-white dark:bg-gray-800 p-6 shadow-md border border-gray-200 dark:border-gray-700">
              <h3 className="font-semibold text-lg text-gray-900 dark:text-white mb-2">Can I suggest a topic for a new guide?</h3>
              <p className="text-gray-700 dark:text-gray-300">
                Absolutely! We welcome your suggestions. Please use the "Suggest a Topic" button in the section above, or email us directly.
              </p>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
};

export default GuidesTutorials;
