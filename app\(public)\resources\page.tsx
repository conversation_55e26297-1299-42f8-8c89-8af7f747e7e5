'use client';

import React, { useState, useEffect } from 'react';
import Beams from '@/components/react-bits/Backgrounds/Beams/Beams';

// All icons from Heroicons v2 (24/outline)
import {
  BookOpenIcon,
  LightBulbIcon,
  BriefcaseIcon,
  ClipboardDocumentListIcon,
  EnvelopeIcon,
  PhoneIcon,
  MapPinIcon,
  MagnifyingGlassIcon,
  ArrowRightIcon,
  CheckCircleIcon,
  AcademicCapIcon,
  Cog6ToothIcon,
  ChartBarIcon,
  BuildingOffice2Icon,
  UserGroupIcon,
  ChartPieIcon,
  MegaphoneIcon, // New icon for communication
  DocumentTextIcon, // New icon for templates
  PencilSquareIcon, // New icon for writing/editing
  CurrencyDollarIcon // New icon for salary
} from '@heroicons/react/24/outline';
import { TbBackground } from 'react-icons/tb';

// --- SECTION CONTAINER COMPONENT ---
interface SectionContainerProps {
  children: React.ReactNode;
  className?: string;
  id?: string;
}

const SectionContainer: React.FC<SectionContainerProps> = ({ children, className, id }) => {
  return (
    <section
      id={id}
      // Using bg-blue-700/10 as a base, adjusted for specific section colors
      className={`py-16 md:py-24 px-4 sm:px-6 lg:px-8 bg-blue-700/10 transition-colors duration-300 ${className || ''}`}
    >
      <div className="max-w-6xl mx-auto">
        {children}
      </div>
    </section>
  );
};

// --- HEADING COMPONENT ---
interface HeadingProps {
  children: React.ReactNode;
  highlightText?: string;
  level: 'h1' | 'h2' | 'h3' | 'h4';
  className?: string;
}

const Heading: React.FC<HeadingProps> = ({ children, highlightText, level, className }) => {
  const renderContent = () => {
    if (highlightText) {
      const parts = children?.toString().split(new RegExp(`(${highlightText})`, 'gi'));
      return (
        <>
          {parts?.map((part, index) =>
            part.toLowerCase() === highlightText.toLowerCase() ? (
              <span key={index} className="text-indigo-600">
                {part}
              </span>
            ) : (
              part
            )
          )}
        </>
      );
    }
    return children;
  };

  const baseClasses = 'font-extrabold mb-4';
  const levelClasses = {
    h1: 'text-4xl md:text-5xl lg:text-6xl text-slate-900 dark:text-gray-50',
    h2: 'text-3xl md:text-4xl lg:text-5xl text-slate-900 dark:text-gray-50',
    h3: 'text-2xl md:text-3xl lg:text-4xl text-slate-900 dark:text-gray-50',
    h4: 'text-xl md:text-2xl lg:text-3xl text-slate-900 dark:text-gray-50',
  };

  const Htag = level;

  return (
    <Htag className={`${baseClasses} ${levelClasses[level]} ${className || ''}`}>
      {renderContent()}
    </Htag>
  );
};

// --- PARAGRAPH COMPONENT ---
interface ParagraphProps {
  children: React.ReactNode;
  className?: string;
}

const Paragraph: React.FC<ParagraphProps> = ({ children, className }) => {
  return (
    <p className={`text-slate-700 dark:text-gray-300 leading-relaxed ${className || ''}`}>
      {children}
    </p>
  );
};

// --- PRIMARY BUTTON COMPONENT ---
interface PrimaryButtonProps {
  children: React.ReactNode;
  href: string;
  className?: string;
  isExternal?: boolean;
}

const PrimaryButton: React.FC<PrimaryButtonProps> = ({ children, href, className, isExternal = false }) => {
  const commonClasses = `inline-flex items-center justify-center gap-2 px-8 py-3 rounded-lg text-base font-medium transition-colors duration-200 cursor-pointer border-[1px] border-indigo-400
                       dark:text-white text-indigo-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500
                       dark:text-white dark:focus:ring-indigo-600 ${className || ''}`;
  return isExternal ? (
    <a href={href} target="_blank" rel="noopener noreferrer" className={commonClasses}>
      {children}
    </a>
  ) : (
    <a href={href} className={commonClasses}>
      {children}
    </a>
  );
};

// --- RESOURCE ITEM CARD COMPONENT (Smaller and Improved UI) ---
interface ResourceItemCardProps {
  title: string;
  description: string;
  href: string;
  // Explicitly typing icon component to accept className as SVGProps
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
}

const ResourceItemCard: React.FC<ResourceItemCardProps> = ({ title, description, href, icon: Icon }) => {
  return (
    <div
      className="group relative overflow-hidden rounded-2xl border border-gray-200 bg-blue-50 p-6 shadow-md transition-all duration-300 hover:-translate-y-1 hover:shadow-lg
                  dark:border-slate-700 dark:bg-slate-800"
    >
      {/* Subtle overlay on hover */}
      <div
        className="absolute inset-0 bg-indigo-600 opacity-0 transition-opacity duration-300 group-hover:opacity-[0.03]"
      ></div>

      <div className="relative z-10 flex flex-col h-full">
        {/* Icon with solid primary color background */}
        <div
          className="inline-flex rounded-xl border-[1px] border-indigo-400 w-12 p-3 mb-4 text-gray-800 dark:text-white transition-transform duration-300 group-hover:scale-105"
        >
          <Icon className="h-6 w-6" />
        </div>

        <h3 className="mb-2 text-lg font-semibold text-slate-900 dark:text-gray-50">
          {title}
        </h3>

        <p className="mb-4 text-sm leading-relaxed text-slate-700 dark:text-gray-300 flex-grow">
          {description}
        </p>

        {/* Button with solid primary colors */}
        <PrimaryButton href={href} className="w-full !rounded-lg !px-4 !py-2 text-sm">
          View Guide
          <ArrowRightIcon className="h-3 w-3 transition-transform group-hover:translate-x-1" />
        </PrimaryButton>
      </div>
    </div>
  );
};

// --- CATEGORY PILL COMPONENT ---
interface CategoryPillProps {
  title: string;
  count: string;
  href: string;
  // Explicitly typing icon component to accept className as SVGProps
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
}

const CategoryPill: React.FC<CategoryPillProps> = ({ title, count, href, icon: Icon }) => {
  return (
    <a href={href} className="group cursor-pointer rounded-2xl bg-gray-50 p-6 transition-all duration-300 hover:-translate-y-1 hover:bg-gradient-to-br hover:from-indigo-50 hover:to-purple-50 hover:shadow-lg
                         dark:bg-slate-700 dark:hover:from-slate-600 dark:hover:to-slate-700">
      <div className="flex flex-col items-center text-center">
        <div className="mb-4 rounded-xl bg-white p-3 shadow-sm transition-all group-hover:shadow-md dark:bg-slate-800">
          <div className="text-slate-700 group-hover:text-indigo-600 dark:text-gray-300">
            <Icon className="h-6 w-6" />
          </div>
        </div>
        <h3 className="mb-2 font-semibold text-slate-900 dark:text-gray-50">
          {title}
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          {count}
        </p>
      </div>
    </a>
  );
};

// --- FEATURE CHECKLIST ITEM COMPONENT ---
interface FeatureChecklistItemProps {
  text: string;
  // Explicitly typing icon component to accept className as SVGProps
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
}

const FeatureChecklistItem: React.FC<FeatureChecklistItemProps> = ({ text, icon: Icon }) => {
  return (
    <div className="flex items-center gap-3">
      <Icon className="h-6 w-6 text-indigo-600 dark:text-indigo-400" />
      <span className="font-medium text-slate-700 dark:text-gray-300 text-lg">
        {text}
      </span>
    </div>
  );
};

// --- CONTACT INFO CARD COMPONENT ---
interface ContactInfoCardProps {
  title: string;
  description: string;
  // Explicitly typing icon component to accept className as SVGProps
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  contactDetail: string;
  linkHref?: string;
  linkText?: string;
}

const ContactInfoCard: React.FC<ContactInfoCardProps> = ({ title, description, icon: Icon, contactDetail, linkHref, linkText }) => {
  return (
    <div className="flex items-start space-x-4 p-6 rounded-xl bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 shadow-sm">
      <div className="flex-shrink-0 p-3 rounded-full bg-indigo-100 text-indigo-600 dark:bg-indigo-900/50 dark:text-indigo-400">
        <Icon className="h-6 w-6" />
      </div>
      <div>
        <h4 className="text-lg font-semibold text-slate-900 dark:text-gray-50">{title}</h4>
        <Paragraph className="text-sm !mb-1">{description}</Paragraph>
        {linkHref ? (
          <a href={linkHref} className="text-indigo-600 hover:underline dark:text-indigo-500">
            {linkText || contactDetail}
          </a>
        ) : (
          <p className="text-slate-700 dark:text-gray-300">{contactDetail}</p>
        )}
      </div>
    </div>
  );
};

// --- MAIN RESOURCES PAGE COMPONENT ---
const ResourcesPage: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const featuredResources = [
    {
      icon: ClipboardDocumentListIcon,
      title: 'Resume Building Guide',
      description: 'Craft a compelling resume that truly highlights your skills and stands out to recruiters.',
      href: '/resources/resume-guide',
    },
    {
      icon: LightBulbIcon,
      title: 'Interview Preparation Blueprint',
      description: 'Equip yourself with essential tips and strategies for successful interviews, from common questions to follow-up etiquette.',
      href: '/resources/interview-blueprint',
    },
    {
      icon: BriefcaseIcon,
      title: 'Job Search Strategies 2.0',
      description: 'Discover advanced methods for identifying hidden job openings, strategic networking, and tailoring your applications for maximum impact.',
      href: '/resources/job-search-strategies',
    },
    {
      icon: AcademicCapIcon,
      title: 'Mastering Online Courses',
      description: 'Leverage online learning platforms to upskill and reskill for in-demand career paths.',
      href: '/resources/online-courses',
    },
    {
      icon: MegaphoneIcon,
      title: 'Effective Communication',
      description: 'Improve your verbal and written communication skills for workplace success and interviews.',
      href: '/resources/communication',
    },
    {
      icon: DocumentTextIcon,
      title: 'Cover Letter Templates',
      description: 'Access professional cover letter templates to easily tailor your applications to specific roles.',
      href: '/resources/cover-letters',
    },
    {
      icon: PencilSquareIcon,
      title: 'Portfolio Development',
      description: 'Learn how to create a compelling professional portfolio that showcases your best work.',
      href: '/resources/portfolio',
    },
    {
      icon: CurrencyDollarIcon,
      title: 'Salary Negotiation Tactics',
      description: 'Gain confidence and strategies to negotiate your compensation effectively and secure the pay you deserve.',
      href: '/resources/salary-negotiations',
    },
  ];

  const categories = [
    { icon: ChartBarIcon, title: 'Marketing', count: '140 guides', href: '/categories/marketing' },
    { icon: ChartPieIcon, title: 'Finance', count: '95 guides', href: '/categories/finance' },
    { icon: Cog6ToothIcon, title: 'Technology', count: '434 guides', href: '/categories/technology' },
    { icon: BuildingOffice2Icon, title: 'Business', count: '211 guides', href: '/categories/business' },
    { icon: UserGroupIcon, title: 'Human Resources', count: '348 guides', href: '/categories/hr' },
    { icon: AcademicCapIcon, title: 'Education', count: '112 guides', href: '/categories/education' },
  ];

  const whyChooseUsFeatures = [
    { icon: CheckCircleIcon, text: 'Comprehensive Guides' },
    { icon: CheckCircleIcon, text: 'Expert Advice' },
    { icon: CheckCircleIcon, text: 'Regularly Updated Content' },
    { icon: CheckCircleIcon, text: 'Interactive Tools' },
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-slate-900 transition-colors duration-300">

      {/* Hero Section */}
      <section className="relative overflow-hidden py-24 md:py-36 bg-slate-200 dark:bg-slate-800 dark:text-white">
        {/* Beams Background */}
      

        <div className="relative container mx-auto px-4 text-center z-10"> {/* z-10 ensures content is above background */}
          <div className={`transition-all duration-1000 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
            <Heading level="h1" highlightText="Career Growth" className="max-w-4xl mx-auto leading-tight text-slate-900 dark:!text-white !mb-6">
              Empowering Your Journey: Essential Resources for Career Growth
            </Heading>
            <Paragraph className="max-w-3xl mx-auto mt-6 mb-12 text-xl dark:!text-gray-300 text-slate-900">
              Access expertly curated guides, powerful tools, and insightful articles designed to help you land your dream job and advance your career with confidence.
            </Paragraph>

            {/* <div className="relative max-w-2xl mx-auto mb-8 flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <MagnifyingGlassIcon className="absolute left-4 top-1/2 -translate-y-1/2 h-6 w-6 text-indigo-600 dark:text-indigo-400" />
                <input
                  type="text"
                  placeholder="Search for guides, tips, or topics..."
                  className="w-full pl-12 pr-6 py-4 rounded-full border-2 border-transparent bg-slate-900/20 dark:bg-white/10 dark:text-white text-slate-800 placeholder-slate-700 dark:placeholder-gray-400 backdrop-blur-sm transition-all focus:border-indigo-400 focus:outline-none"
                />
              </div>
              <PrimaryButton href="/resources/search" className="rounded-full px-8 py-4 text-slate-50 bg-blue-800">
                Search Resources
              </PrimaryButton>
            </div> */}
          </div>
        </div>
      </section>

      {/* Featured Resources Section */}
      {/* <SectionContainer id="featured-resources" className="dark:bg-slate-900 bg-slate-100">
        <Heading level="h2" className="text-center mb-6">
          Top<span className="text-indigo-600"> Resources & Expert Guides</span>
        </Heading>
        <Paragraph className="max-w-3xl mx-auto text-center mb-16 text-lg">
          Dive into our collection of handpicked articles and tools, updated regularly to keep you ahead in your job search and professional development.
        </Paragraph>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"> 
          {featuredResources.map((resource, index) => (
            <ResourceItemCard
              key={index}
              title={resource.title}
              description={resource.description}
              href={resource.href}
              icon={resource.icon}
            />
          ))}
        </div>
      </SectionContainer> */}

      {/* Categories Section */}
      {/* <SectionContainer id="categories" className="bg-slate-200 dark:bg-slate-800">
        <Heading level="h2" className="text-center mb-6">
          Explore by <span className="text-indigo-600">Key Career Topics</span>
        </Heading>
        <Paragraph className="max-w-3xl mx-auto text-center mb-16 text-lg">
          Navigate our extensive library by topic. Whether it's job search, professional development, or industry insights, find exactly what you need.
        </Paragraph>
        <div className="grid grid-cols-2 gap-6 md:grid-cols-3">
          {categories.map((category, index) => (
            <CategoryPill
              key={index}
              title={category.title}
              count={category.count}
              href={category.href}
              icon={category.icon}
            />
          ))}
        </div>
      </SectionContainer> */}

      {/* Why Choose Us Section */}
      <SectionContainer className="bg-white dark:bg-slate-900">
        <div className="grid items-center gap-16 lg:grid-cols-2">
          <div>
            <Heading level="h2" className="mb-6">
              Why Choose Our <span className="text-indigo-600">Resource Hub?</span>
            </Heading>
            <Paragraph className="mb-8 text-xl leading-relaxed">
              We're committed to providing the most relevant and actionable career resources. Our platform is designed to empower you at every stage, from job searching to professional growth.
            </Paragraph>
            <div className="mb-8 grid grid-cols-1 sm:grid-cols-2 gap-4">
              {whyChooseUsFeatures.map((feature, index) => (
                <FeatureChecklistItem key={index} text={feature.text} icon={feature.icon} />
              ))}
            </div>
            <PrimaryButton href="/about">
              Learn More About Us
            </PrimaryButton>
          </div>
          <div className={`relative transition-all delay-300 duration-1000 ${isVisible ? 'translate-x-0 opacity-100' : 'translate-x-10 opacity-0'}`}>
            <img
              src="/images/career-resources.png" // Placeholder image
              alt="Why choose our resources"
              className="h-auto w-full rounded-2xl shadow-2xl"
            />
            <div className="absolute -top-4 -right-4 animate-bounce rounded-full bg-indigo-600 p-3 text-white shadow-lg">
              <LightBulbIcon className="h-6 w-6" />
            </div>
            <div className="absolute -bottom-4 -left-4 animate-pulse rounded-full bg-indigo-600 p-3 text-white shadow-lg">
              <BookOpenIcon className="h-6 w-6" />
            </div>
          </div>
        </div>
      </SectionContainer>

      {/* Assistance/Contact Section */}
      <SectionContainer id="assistance" className="bg-slate-100 dark:bg-gray-900">
        <div className="text-center mb-16">
          <Heading level="h2" className="mb-6">
            Need <span className="text-indigo-600">Personalized</span> Assistance?
          </Heading>
          <Paragraph className="max-w-3xl mx-auto text-center mb-16 text-lg">
            If our extensive resources don't fully answer your questions, our dedicated support team is here to provide personalized guidance.
          </Paragraph>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          <ContactInfoCard
            title="Email Support"
            description="Send us your detailed queries, and we'll respond within 24 hours."
            icon={EnvelopeIcon}
            contactDetail="<EMAIL>"
            linkHref="mailto:<EMAIL>"
            linkText="Email Us Directly"
          />
          <ContactInfoCard
            title="Call Us"
            description="Speak directly with a career advisor for immediate support."
            icon={PhoneIcon}
            contactDetail="+91 6204351245"
            linkHref="tel:+************"
            linkText="Call Now"
          />
          <ContactInfoCard
            title="Office Hours"
            description="Our team is available during these hours for your convenience."
            icon={MapPinIcon}
            contactDetail="Mon-Fri, 9:00 AM - 5:00 PM IST"
          />
        </div>
      </SectionContainer>
    </div>
  );
};

export default ResourcesPage;