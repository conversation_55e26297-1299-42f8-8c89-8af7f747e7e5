'use client';

import { ReactNode } from 'react';

import { ThemeProvider } from '@/components/ui/theme-provider';
import ClientAuthLoader from '@/components/ui/ClientAuthLoader';
import { Toaster } from 'sonner';
import ReduxProvider from '@/components/ui/redux-provider';

export default function AppProviders({ children }: { children: ReactNode }) {
  return (
    <ReduxProvider>
      <ClientAuthLoader />
      <ThemeProvider
        attribute="class"
        defaultTheme="system"
        enableSystem
        disableTransitionOnChange
      >
        <Toaster position="top-right" richColors closeButton theme="light" />
        {children}
      </ThemeProvider>
    </ReduxProvider>
  );
}
