'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { CirclePlus, CircleMinus } from 'lucide-react';
import { cn } from '@/lib/utils';

export type FAQType = 'General' | 'Legal' | 'Application' | 'Interview';

export type FAQItem = {
  category: FAQType;
  faqs: {
    question: string;
    answer: React.ReactNode;
  }[];
};

type FAQProps = {
  faqs: FAQItem[];
};

const typeLabels: Record<FAQType, string> = {
  General: 'General',
  Legal: 'Legal',
  Application: 'Application Process',
  Interview: 'Interview Tips',
};

const FAQ: React.FC<FAQProps> = ({ faqs }) => {
  const [activeType, setActiveType] = useState<FAQType>('General');
  const [openIndex, setOpenIndex] = useState<number | null>(null);

  const types = faqs.map(faq => faq.category);
  const filteredFaq = faqs.find(faq => faq.category === activeType);

  return (
    <div className="mx-auto flex flex-col sm:flex-row max-w-6xl gap-12 px-4 py-12">
      <div className="w-1/4">
        <ul className="space-y-2 text-sm flex sm:flex-col [@media(max-width:350px)]:ml-[-40px] font-medium text-gray-700">
          {types.map(type => (
            <li key={type}>
              <button
                onClick={() => {
                  setActiveType(type);
                  setOpenIndex(null);
                }}
                className={cn(
                  'w-full rounded-md px-3 py-2 text-left transition',
                  activeType === type
                    ? 'bg-gray-900 font-semibold text-white dark:bg-gray-500'
                    : 'text-gray-800 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700'
                )}
              >
                {typeLabels[type]}
              </button>
            </li>
          ))}
        </ul>
      </div>

      <div className="flex-1">
        <h2 className="mb-6 text-3xl font-bold text-gray-900 dark:text-gray-300">
          {typeLabels[activeType]} Questions
        </h2>

        <div className="custom-scrollbar max-h-100 space-y-2 overflow-y-auto pr-2">
          {filteredFaq?.faqs.map((faq, idx) => (
            <motion.div
              key={faq.question + idx}
              layout
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 10 }}
              transition={{ duration: 0.3 }}
              className="border-b border-gray-200"
            >
              <button
                className={cn(
                  'flex w-full items-center justify-between py-4 text-left text-base font-medium transition-colors',
                  openIndex === idx
                    ? 'text-blue-600'
                    : 'text-gray-800 dark:text-gray-300'
                )}
                onClick={() => setOpenIndex(openIndex === idx ? null : idx)}
              >
                <span>{faq.question}</span>
                {openIndex === idx ? (
                  <CircleMinus className="h-5 w-5" />
                ) : (
                  <CirclePlus className="h-5 w-5" />
                )}
              </button>
              <AnimatePresence initial={false}>
                {openIndex === idx && (
                  <motion.div
                    layout
                    key="content"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.25 }}
                    className="pr-2 pb-4 text-gray-600 dark:text-gray-400"
                  >
                    {faq.answer}
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default FAQ;
