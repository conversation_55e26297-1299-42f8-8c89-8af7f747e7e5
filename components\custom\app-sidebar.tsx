'use client';
import {
  Bell,
  ChevronRight,
  ChevronsUpDown,
  CreditCard,
  LogOut,
  LayoutDashboard,
  CircleUserRound,
  HeartPlus,
  BriefcaseBusiness,
  GraduationCap,
  CalendarCheck,
  Award,
  HandHelping,
  Paperclip,
  Sparkles,
  BadgeCheck,
} from 'lucide-react';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from '@/components/ui/sidebar';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '../ui/collapsible';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuSubContent,
  DropdownMenuTrigger,
} from '../ui/dropdown-menu';

import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';

import { useAppSelector } from '@/store';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '../ui/dialog';

import { Button } from '../ui/button';
import { useLogoutMutation } from '@/store/features/authApi';
import Link from 'next/link';
import Image from 'next/image';
import { useIsMobile } from '@/hooks/use-mobile';

// Menu items.

const navMain = [
  {
    title: 'Account',
    url: '/account',
    icon: CircleUserRound,
    items: [
      {
        title: 'Overview ',
        url: '/view-profile',
      },
      {
        title: 'Edit Profile',
        url: '/edit-profile',
      },
    ],
  },
  {
    title: 'Companies',
    url: '/companies',
    icon: BriefcaseBusiness,
    // items: [
    //   {
    //     title: 'Companies',
    // url: '/companies',
    //   },
    //   {
    //     title: 'Explore Internships',
    //     url: '/internships',
    //   },
    // ],
  },
  {
    title: 'Opportunities',
    url: '/opportunities',
    icon: BriefcaseBusiness,
    items: [
      {
        title: 'Explore Jobs',
        url: '/jobs',
      },
      {
        title: 'Explore Internships',
        url: '/internships',
      },
    ],
  },
  {
    title: 'Skill Certifications',
    url: '/skill-certifications',
    icon: GraduationCap,
    items: [
      {
        title: 'Web Development',
        url: '/web-development',
      },
      {
        title: 'Data Science',
        url: '/data-science',
      },
      {
        title: 'Graphic Design',
        url: '/graphic-design',
      },
      {
        title: 'Digital Marketing',
        url: '/digital-marketing',
      },
      {
        title: 'Mobile App Development',
        url: '/mobile-app-development',
      },
    ],
  },

  {
    title: 'Events',
    url: '/events-and-webinars',
    icon: CalendarCheck,
    items: [
      {
        title: 'All Events',
        url: '/',
      },
      {
        title: 'Upcoming Events',
        url: '/upcoming-events',
      },
      {
        title: 'Past Event & Recording',
        url: '/past-events',
      },
      // {
      //   title: 'Host Your Webinar',
      //   url: '/host-webinar',
      // },
    ],
  },
  {
    title: 'Certificates',
    url: '/certificates',
    icon: Award,
    items: [
      {
        title: 'Skill Certificate',
        url: '/skill-certificate',
      },
      {
        title: 'Course Certificate',
        url: '/course-certificate',
      },
      {
        title: 'Event Certificate',
        url: '/event-certificate',
      },
    ],
  },
  {
    title: 'Applications',
    url: '/applications',
    icon: Paperclip,
    items: [
      {
        title: 'Job Applications',
        url: '/job-applications',
      },
      {
        title: 'Internship Applications',
        url: '/internship-applications',
      },
      {
        title: 'Interviews',
        url: '/interviews',
      },
    ],
  },
  {
    title: 'Resources',
    url: '/resources',
    icon: HandHelping,
    items: [
      {
        title: 'Videos',
        url: '/videos',
      },
      {
        title: 'Articles',
        url: '/articles',
      },
      {
        title: 'Courses',
        url: '/courses',
      },
      {
        title: 'Documents',
        url: '/documents',
      },
    ],
  },
];

const projects = [
  {
    name: 'Support',
    url: '/dashboard/support',
    icon: HeartPlus,
  },
  {
    name: 'Payments',
    url: '/dashboard/payments',
    icon: CreditCard,
  },
  {
    name: 'Notification',
    url: '/dashboard/notifications',
    icon: Bell,
  },
];

export function AppSidebar() {
  const [logout] = useLogoutMutation({});
  const user = useAppSelector(state => state.auth?.user);

  const handleLogOut = async () => {
    await logout();
  };

  return (
    <Sidebar variant="sidebar" side="left" collapsible="icon">
      <SidebarHeader className="items-center font-extrabold">
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton asChild>
              {/* Logo */}
              <Link
                href="/dashboard"
                aria-label="Home"
                className="flex items-center space-x-2"
              >
                <Image
                  src="/logo/white-logo.png"
                  alt="Sudha Software Solution Logo"
                  width={300}
                  height={100}
                  className="w-auto dark:hidden"
                />
                <Image
                  src="/logo/dark-logo.png"
                  alt="Sudha Software Solution Logo"
                  width={300}
                  height={100}
                  className="hidden w-auto dark:block"
                />
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <SidebarGroup className="">
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton asChild>
                <Link href="/dashboard">
                  <LayoutDashboard />
                  <span>Dashboard</span>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
          <SidebarMenu>
            {navMain.map(item => (
              <Collapsible
                key={item.title}
                asChild
                className="group/collapsible"
              >
                <SidebarMenuItem>
                  <CollapsibleTrigger asChild>
                    <SidebarMenuButton tooltip={item.title}>
                      {item.icon && <item.icon />}
                      <span>{item.title}</span>
                      <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                    </SidebarMenuButton>
                  </CollapsibleTrigger>
                  <CollapsibleContent>
                    <SidebarMenuSub>
                      {item.items?.map(subItem => (
                        <SidebarMenuSubItem key={subItem.title}>
                          <SidebarMenuSubButton asChild>
                            <Link href={'/dashboard' + item.url + subItem.url}>
                              <span>{subItem.title}</span>
                            </Link>
                          </SidebarMenuSubButton>
                        </SidebarMenuSubItem>
                      ))}
                    </SidebarMenuSub>
                  </CollapsibleContent>
                </SidebarMenuItem>
              </Collapsible>
            ))}
          </SidebarMenu>

          <SidebarMenu>
            {projects.map(item => (
              <SidebarMenuItem key={item.name}>
                <SidebarMenuButton asChild>
                  <Link href={item.url}>
                    <item.icon />
                    <span>{item.name}</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
            ))}
            <SidebarMenuItem>
              <Dialog>
                <DialogTrigger asChild>
                  <SidebarMenuButton asChild>
                    <div>
                      <LogOut />
                      <span>Logout</span>
                    </div>
                  </SidebarMenuButton>
                </DialogTrigger>
                <DialogContent className="sm:max-w-md">
                  <DialogHeader>
                    <DialogTitle>Logout</DialogTitle>
                    <DialogDescription>
                      Are you sure you want href logout
                    </DialogDescription>
                  </DialogHeader>
                  <div className="flex items-center space-x-2"></div>
                  <DialogFooter className="sm:justify-start">
                    <DialogClose asChild>
                      <Button
                        type="button"
                        className="bg-green-400 text-white hover:bg-green-500"
                        variant="secondary"
                      >
                        Cancel
                      </Button>
                    </DialogClose>
                    <DialogClose asChild>
                      <Button
                        onClick={handleLogOut}
                        type="button"
                        className="bg-red-400 text-white hover:bg-red-500"
                        variant="secondary"
                      >
                        Confrim
                      </Button>
                    </DialogClose>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroup>
      </SidebarContent>
      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <SidebarMenuButton
                  size="lg"
                  className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
                >
                  <Avatar className="h-8 w-8 rounded-lg">
                    <AvatarImage
                      src={''}
                      alt={user?.firstName + '-' + user?.lastName + '-photo'}
                    />

                    <AvatarFallback className="rounded-lg">
                      {' '}
                      {user?.firstName[0]} {user?.lastName[0]}
                    </AvatarFallback>
                  </Avatar>
                  <div className="grid flex-1 text-left text-sm leading-tight">
                    <span className="truncate font-semibold">
                      {' '}
                      {user?.firstName} {user?.lastName}
                    </span>
                    <span className="truncate text-xs">{user?.email}</span>
                  </div>
                  <ChevronsUpDown className="ml-auto size-4" />
                </SidebarMenuButton>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg">
                <DropdownMenuLabel className="p-0 font-normal">
                  <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                    <Avatar className="h-8 w-8 rounded-lg">
                      <AvatarImage
                        src={''}
                        alt={user?.firstName + '-' + user?.lastName + '-photo'}
                      />

                      <AvatarFallback className="rounded-lg">
                        {' '}
                        {user?.firstName[0]} {user?.lastName[0]}
                      </AvatarFallback>
                    </Avatar>
                    <div className="grid flex-1 text-left text-sm leading-tight">
                      <span className="truncate font-semibold">
                        {' '}
                        {user?.firstName} {user?.lastName}
                      </span>
                      <span className="truncate text-xs">{user?.email}</span>
                    </div>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuGroup>
                  <DropdownMenuItem>
                    <Sparkles />
                    Upgrade href Pro
                  </DropdownMenuItem>
                </DropdownMenuGroup>
                <DropdownMenuSeparator />
                <DropdownMenuGroup>
                  <DropdownMenuItem>
                    <BadgeCheck />
                    Account
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <CreditCard />
                    Billing
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Bell />
                    Notifications
                  </DropdownMenuItem>
                </DropdownMenuGroup>
                <DropdownMenuSeparator />
                <Dialog>
                  <DialogTrigger asChild>
                    <div className="focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm transition-colors outline-none select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&>svg]:size-4 [&>svg]:shrink-0">
                      <LogOut />
                      Log out
                    </div>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-md">
                    <DialogHeader>
                      <DialogTitle>Logout</DialogTitle>
                      <DialogDescription>
                        Are you sure you want href logout
                      </DialogDescription>
                    </DialogHeader>
                    <div className="flex items-center space-x-2"></div>
                    <DialogFooter className="sm:justify-start">
                      <DialogClose asChild>
                        <Button
                          type="button"
                          className="bg-green-400 text-white hover:bg-green-500"
                          variant="secondary"
                        >
                          Cancel
                        </Button>
                      </DialogClose>
                      <DialogClose asChild>
                        <Button
                          onClick={handleLogOut}
                          type="button"
                          className="bg-red-400 text-white hover:bg-red-500"
                          variant="secondary"
                        >
                          Confrim
                        </Button>
                      </DialogClose>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </DropdownMenuContent>
            </DropdownMenu>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
    </Sidebar>
  );
}
