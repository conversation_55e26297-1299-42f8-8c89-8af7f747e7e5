import React, { useState } from 'react';

interface Benefit {
  icon: React.ReactNode;
  title: string;
  description: string;
  stats?: string;
}

interface Statistic {
  value: string;
  label: string;
  description: string;
}

interface ResumeSection {
  title: string;
  items: string[];
}

interface ResumeData {
  name: string;
  title: string;
  email: string;
  sections: ResumeSection[];
}

interface CertificationsBenefitsData {
  hero: {
    title: string;
    subtitle: string;
    description: string;
    backgroundGradient: string;
    icon: React.ReactNode;
  };
  statistics: Statistic[];
  benefits: Benefit[];
  resumeComparison: {
    title: string;
    subtitle: string;
    without: ResumeData;
    with: ResumeData;
  };
  cta: {
    title: string;
    description: string;
    buttonText: string;
    buttonLink: string;
  };
}

export const CertificationsBenefitsPage = ({ data }: { data: CertificationsBenefitsData }) => {
  const [activeResume, setActiveResume] = useState<'without' | 'with'>('without');

  const ResumeCard = ({ resume, type }: { resume: ResumeData; type: 'without' | 'with' }) => (
    <div className={`bg-white dark:bg-slate-800 rounded-2xl shadow-lg p-8 transition-all duration-300 ${
      type === 'with' ? 'ring-2 ring-blue-500 transform scale-105' : ''
    }`}>
      <div className="mb-6">
        <h3 className="text-2xl font-bold text-gray-900 dark:text-white">{resume.name}</h3>
        <p className="text-lg text-gray-600 dark:text-slate-300">{resume.title}</p>
        <p className="text-sm text-gray-500 dark:text-slate-400">{resume.email}</p>
      </div>

      {resume.sections.map((section, index) => (
        <div key={index} className="mb-6">
          <h4 className="text-lg font-semibold text-gray-800 dark:text-white mb-3 pb-2 border-b border-gray-200 dark:border-slate-600">
            {section.title}
          </h4>
          <ul className="space-y-2">
            {section.items.map((item, itemIndex) => (
              <li key={itemIndex} className="text-gray-600 dark:text-slate-300 text-sm leading-relaxed flex items-start">
                <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                {item}
              </li>
            ))}
          </ul>
        </div>
      ))}

      {type === 'with' && (
        <div className="absolute -top-3 -right-3 bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-medium">
          ✓ Certified
        </div>
      )}
    </div>
  );

  return (
    <div className="bg-gray-50 dark:bg-slate-900 min-h-screen">
      {/* Hero Section */}
      <section className={`relative ${data.hero.backgroundGradient} text-white py-20 px-4`}>
        <div className="max-w-6xl mx-auto text-center">
          <div className="flex justify-center mb-6">
            <div className="bg-white/20 p-4 rounded-2xl">
              {data.hero.icon}
            </div>
          </div>
          <h1 className="text-4xl lg:text-6xl font-bold mb-6 leading-tight">
            {data.hero.title}
          </h1>
          <p className="text-xl lg:text-2xl text-white/90 mb-8 max-w-4xl mx-auto">
            {data.hero.subtitle}
          </p>
          <p className="text-lg text-white/80 max-w-3xl mx-auto">
            {data.hero.description}
          </p>
        </div>
      </section>

      {/* Statistics Section */}
      <section className="py-16 px-4 bg-white dark:bg-slate-800">
        <div className="max-w-6xl mx-auto">
          <div className="grid md:grid-cols-3 gap-8">
            {data.statistics.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-4xl lg:text-5xl font-bold text-blue-600 dark:text-blue-400 mb-2">
                  {stat.value}
                </div>
                <div className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  {stat.label}
                </div>
                <div className="text-gray-600 dark:text-slate-300">
                  {stat.description}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-16 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white mb-6">
              Why Professional Certifications Matter
            </h2>
            <p className="text-xl text-gray-600 dark:text-slate-300 max-w-3xl mx-auto">
              In today's competitive job market, certifications are your ticket to standing out, advancing your career, and commanding higher salaries.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {data.benefits.map((benefit, index) => (
              <div key={index} className="bg-white dark:bg-slate-800 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 group">
                <div className="bg-blue-100 dark:bg-blue-900 p-4 rounded-2xl w-fit mb-6 group-hover:scale-110 transition-transform">
                  {benefit.icon}
                </div>
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
                  {benefit.title}
                </h3>
                <p className="text-gray-600 dark:text-slate-300 mb-4">
                  {benefit.description}
                </p>
                {benefit.stats && (
                  <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                    {benefit.stats}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Resume Comparison Section */}
      <section className="py-16 px-4 bg-white dark:bg-slate-800">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white mb-6">
              {data.resumeComparison.title}
            </h2>
            <p className="text-xl text-gray-600 dark:text-slate-300 max-w-3xl mx-auto mb-8">
              {data.resumeComparison.subtitle}
            </p>

            {/* Toggle Buttons */}
            <div className="flex justify-center mb-8">
              <div className="bg-gray-100 dark:bg-slate-700 p-2 rounded-2xl flex">
                <button
                  onClick={() => setActiveResume('without')}
                  className={`px-6 py-3 rounded-xl font-medium transition-all ${
                    activeResume === 'without'
                      ? 'bg-red-500 text-white shadow-lg'
                      : 'text-gray-600 dark:text-slate-300 hover:text-gray-900 dark:hover:text-white'
                  }`}
                >
                  Without Certifications
                </button>
                <button
                  onClick={() => setActiveResume('with')}
                  className={`px-6 py-3 rounded-xl font-medium transition-all ${
                    activeResume === 'with'
                      ? 'bg-blue-500 text-white shadow-lg'
                      : 'text-gray-600 dark:text-slate-300 hover:text-gray-900 dark:hover:text-white'
                  }`}
                >
                  With Certifications
                </button>
              </div>
            </div>
          </div>

          <div className="hidden lg:grid lg:grid-cols-2 gap-8 mb-8">
            <div className="relative">
              <ResumeCard resume={data.resumeComparison.without} type="without" />
            </div>
            <div className="relative">
              <ResumeCard resume={data.resumeComparison.with} type="with" />
            </div>
          </div>

          <div className="lg:hidden">
            <div className="text-center mb-4">
              <span className={`px-4 py-2 rounded-full font-semibold ${
                activeResume === 'without'
                  ? 'bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-300'
                  : 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300'
              }`}>
                {activeResume === 'without' ? '❌ Without Certifications' : '✅ With Certifications'}
              </span>
            </div>
            <ResumeCard 
              resume={activeResume === 'without' ? data.resumeComparison.without : data.resumeComparison.with} 
              type={activeResume} 
            />
          </div>

          <div className="mt-12 bg-gradient-to-r from-blue-50 to-blue-50 dark:from-blue-900/20 dark:to-blue-900/20 rounded-2xl p-8">
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6 text-center">
              The Difference is Clear
            </h3>
            <div className="grid md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-2">3x</div>
                <div className="text-gray-700 dark:text-slate-300">More Interview Calls</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-2">25%</div>
                <div className="text-gray-700 dark:text-slate-300">Higher Salary Offers</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-2">5x</div>
                <div className="text-gray-700 dark:text-slate-300">Faster Career Growth</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 px-4 bg-gradient-to-r from-blue-600 to-purple-700 text-white">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl lg:text-4xl font-bold mb-6">
            {data.cta.title}
          </h2>
          <p className="text-xl text-white/90 mb-8">
            {data.cta.description}
          </p>
          <a href={data.cta.buttonLink}>
            <button className="px-8 py-4 bg-white text-blue-600 rounded-xl font-semibold hover:bg-gray-100 transition-colors text-lg">
              {data.cta.buttonText}
            </button>
          </a>
        </div>
      </section>
    </div>
  );
};