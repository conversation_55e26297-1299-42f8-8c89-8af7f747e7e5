import React, { useState } from 'react';
import Link from 'next/link';
import { CertificationsPageData } from '@/constant/courses';
import {
  CheckCircle,
  TrendingUp,
  Users,
  Award,
  Briefcase,
  DollarSign,
} from 'lucide-react';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';

export const CertificationComponent = ({
  data,
}: {
  data: CertificationsPageData;
}) => {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-slate-900">
      {/* Hero Section */}
      <section
        className={`relative ${data.header.backgroundGradient} px-4 py-20 text-white`}
      >
        <Link href={data.header.backLink}>
          <div className="bg-transparent px-4">
            <div className="mx-auto mt-[-3rem] max-w-7xl">
              <button className="mb-4 flex items-center gap-2 text-white/80 transition-colors hover:text-white">
                {data.header.backIcon}
                {data.header.backLabel}
              </button>
            </div>
          </div>
        </Link>
        <div className="mx-auto max-w-7xl text-center">
          <div className="mb-6 flex justify-center">
            <div className="rounded-2xl bg-white/20 p-4">
              {data.header.icon}
            </div>
          </div>
          <h1 className="mb-6 text-4xl leading-tight font-bold lg:text-6xl">
            {data.header.title}
          </h1>
          <p className="mx-auto mb-8 max-w-3xl text-xl text-white/90 lg:text-2xl">
            {data.header.subtitle}
          </p>
          <p className="mx-auto max-w-2xl text-lg text-white/80">
            {data.header.description}
          </p>
        </div>

        {/* Floating Stats */}
        <div className="mx-auto mt-16 max-w-5xl">
          <div className="grid grid-cols-2 gap-6 md:grid-cols-4">
            <div className="rounded-2xl bg-white/10 p-6 text-center backdrop-blur-sm">
              <div className="mb-2 text-3xl font-bold">
                {data.stats.totalCourses}
              </div>
              <div className="text-white/80">Total Courses</div>
            </div>
            <div className="rounded-2xl bg-white/10 p-6 text-center backdrop-blur-sm">
              <div className="mb-2 text-3xl font-bold">
                {data.stats.totalStudents}
              </div>
              <div className="text-white/80">Students Enrolled</div>
            </div>
            <div className="rounded-2xl bg-white/10 p-6 text-center backdrop-blur-sm">
              <div className="mb-2 text-3xl font-bold">
                {data.stats.averageRating}
              </div>
              <div className="text-white/80">Average Rating</div>
            </div>
            <div className="rounded-2xl bg-white/10 p-6 text-center backdrop-blur-sm">
              <div className="mb-2 text-3xl font-bold">
                {data.stats.completionRate}
              </div>
              <div className="text-white/80">Completion Rate</div>
            </div>
          </div>
        </div>
      </section>

      {/* Courses Grid */}
      <Tabs defaultValue="about" className="w-full items-center">
        <TabsList className="mt-8 rounded-4xl bg-gray-200 p-8 dark:bg-slate-800">
          <TabsTrigger className="rounded-4xl p-4 text-2xl" value="about">
            About
          </TabsTrigger>
          <TabsTrigger className="rounded-4xl p-4 text-2xl" value="outcomes">
            Outcomes
          </TabsTrigger>
          <TabsTrigger className="rounded-4xl p-4 text-2xl" value="courses">
            Courses
          </TabsTrigger>
        </TabsList>
        <TabsContent value="about">
          <section>
            <div className="bg-white px-6 py-12 dark:bg-slate-800">
              <div className="mx-auto max-w-4xl">
                <div className="mb-12 text-center">
                  <h2 className="mb-4 text-3xl font-bold text-gray-900 dark:text-white">
                    {data.about.title}
                  </h2>
                  <div className="mx-auto mb-8 h-1 w-24 bg-gradient-to-r from-blue-500 to-purple-600"></div>
                </div>

                <div className="grid items-center gap-12 md:grid-cols-2">
                  <div>
                    <p className="mb-6 text-lg leading-relaxed text-gray-600 dark:text-slate-300">
                      {data.about.description}
                    </p>

                    <div className="space-y-3">
                      {data.about.highlights.map((highlight, index) => (
                        <div key={index} className="flex items-start gap-3">
                          <CheckCircle className="mt-0.5 h-5 w-5 flex-shrink-0 text-green-500" />
                          <span className="text-gray-700 dark:text-slate-300">
                            {highlight}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="relative">
                    <div className="rounded-2xl bg-gradient-to-br from-blue-50 to-purple-50 p-8 dark:from-slate-700 dark:to-slate-600">
                      <div className="grid grid-cols-2 gap-6">
                        <div className="text-center">
                          <div className="mx-auto mb-3 flex h-16 w-16 items-center justify-center rounded-full bg-blue-500">
                            <Users className="h-8 w-8 text-white" />
                          </div>
                          <div className="text-2xl font-bold text-gray-900 dark:text-white">
                            50K+
                          </div>
                          <div className="text-sm text-gray-600 dark:text-slate-300">
                            Enrolled
                          </div>
                        </div>

                        <div className="text-center">
                          <div className="mx-auto mb-3 flex h-16 w-16 items-center justify-center rounded-full bg-purple-500">
                            <Award className="h-8 w-8 text-white" />
                          </div>
                          <div className="text-2xl font-bold text-gray-900 dark:text-white">
                            95%
                          </div>
                          <div className="text-sm text-gray-600 dark:text-slate-300">
                            Success Rate
                          </div>
                        </div>

                        <div className="text-center">
                          <div className="mx-auto mb-3 flex h-16 w-16 items-center justify-center rounded-full bg-green-500">
                            <TrendingUp className="h-8 w-8 text-white" />
                          </div>
                          <div className="text-2xl font-bold text-gray-900 dark:text-white">
                            4.9★
                          </div>
                          <div className="text-sm text-gray-600 dark:text-slate-300">
                            Rating
                          </div>
                        </div>

                        <div className="text-center">
                          <div className="mx-auto mb-3 flex h-16 w-16 items-center justify-center rounded-full bg-orange-500">
                            <Briefcase className="h-8 w-8 text-white" />
                          </div>
                          <div className="text-2xl font-bold text-gray-900 dark:text-white">
                            6 Months
                          </div>
                          <div className="text-sm text-gray-600 dark:text-slate-300">
                            Duration
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </TabsContent>
        <TabsContent value="outcomes">
          <section>
            <div className="bg-gray-50 px-6 py-12 dark:bg-slate-900">
              <div className="mx-auto max-w-6xl">
                <div className="mb-12 text-center">
                  <h2 className="mb-4 text-3xl font-bold text-gray-900 dark:text-white">
                    {data.outcomes.title}
                  </h2>
                  <div className="mx-auto mb-6 h-1 w-24 bg-gradient-to-r from-green-500 to-blue-600"></div>
                  <p className="mx-auto max-w-3xl text-lg text-gray-600 dark:text-slate-300">
                    {data.outcomes.description}
                  </p>
                </div>

                <div className="mb-12 grid gap-8 lg:grid-cols-3">
                  <div className="rounded-2xl bg-white p-8 shadow-lg dark:bg-slate-800">
                    <div className="mb-6 flex items-center">
                      <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-500">
                        <Award className="h-6 w-6 text-white" />
                      </div>
                      <h3 className="ml-4 text-xl font-bold text-gray-900 dark:text-white">
                        Skills You'll Gain
                      </h3>
                    </div>
                    <div className="space-y-3">
                      {data.outcomes.skills.map((skill, index) => (
                        <div key={index} className="flex items-center gap-3">
                          <div className="h-2 w-2 rounded-full bg-blue-500"></div>
                          <span className="text-gray-700 dark:text-slate-300">
                            {skill}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="rounded-2xl bg-white p-8 shadow-lg dark:bg-slate-800">
                    <div className="mb-6 flex items-center">
                      <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-500">
                        <TrendingUp className="h-6 w-6 text-white" />
                      </div>
                      <h3 className="ml-4 text-xl font-bold text-gray-900 dark:text-white">
                        Career Impact
                      </h3>
                    </div>
                    <div className="space-y-4">
                      {data.outcomes.careerStats.map((stat, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between rounded-lg bg-gray-50 p-3 dark:bg-slate-700"
                        >
                          <span className="text-sm text-gray-600 dark:text-slate-300">
                            {stat.label}
                          </span>
                          <span className="font-bold text-gray-900 dark:text-white">
                            {stat.value}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="rounded-2xl bg-white p-8 shadow-lg dark:bg-slate-800">
                    <div className="mb-6 flex items-center">
                      <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-purple-500">
                        <Briefcase className="h-6 w-6 text-white" />
                      </div>
                      <h3 className="ml-4 text-xl font-bold text-gray-900 dark:text-white">
                        Career Opportunities
                      </h3>
                    </div>
                    <div className="space-y-3">
                      {data.outcomes.jobRoles.map((role, index) => (
                        <div
                          key={index}
                          className="rounded-lg bg-purple-50 p-3 dark:bg-purple-900/20"
                        >
                          <span className="font-medium text-purple-700 dark:text-purple-300">
                            {role}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                <div className="rounded-2xl bg-gradient-to-r from-blue-600 via-purple-600 to-green-600 p-8 text-white">
                  <div className="grid gap-6 text-center md:grid-cols-4">
                    <div>
                      <DollarSign className="mx-auto mb-2 h-8 w-8" />
                      <div className="text-2xl font-bold">$75K+</div>
                      <div className="text-sm text-white/80">
                        Avg. Starting Salary
                      </div>
                    </div>
                    <div>
                      <Users className="mx-auto mb-2 h-8 w-8" />
                      <div className="text-2xl font-bold">87%</div>
                      <div className="text-sm text-white/80">
                        Job Placement Rate
                      </div>
                    </div>
                    <div>
                      <TrendingUp className="mx-auto mb-2 h-8 w-8" />
                      <div className="text-2xl font-bold">92%</div>
                      <div className="text-sm text-white/80">
                        Career Advancement
                      </div>
                    </div>
                    <div>
                      <Award className="mx-auto mb-2 h-8 w-8" />
                      <div className="text-2xl font-bold">4.8/5</div>
                      <div className="text-sm text-white/80">
                        Employer Satisfaction
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </TabsContent>
        <TabsContent value="courses">
          <section className="px-4 py-16">
            <div className="mx-auto max-w-7xl">
              <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
                {data.courses.map(course => (
                  <Link key={course.id} href={course.link}>
                    <div className="group cursor-pointer overflow-hidden rounded-2xl bg-white shadow-lg transition-all duration-300 hover:shadow-xl dark:bg-slate-800">
                      {/* Course Image */}
                      <div className="relative h-48 overflow-hidden bg-gradient-to-br from-blue-500 to-purple-600">
                        <img
                          src={course.image}
                          alt={course.title}
                          className="h-full w-full object-cover transition-transform duration-300 group-hover:scale-105"
                        />
                        <div className="absolute top-4 right-4">
                          <span
                            className={`rounded-full px-3 py-1 text-xs font-medium ${
                              course.level === 'Beginner'
                                ? 'bg-green-500 text-white'
                                : course.level === 'Intermediate'
                                  ? 'bg-yellow-500 text-white'
                                  : 'bg-red-500 text-white'
                            }`}
                          >
                            {course.level}
                          </span>
                        </div>
                      </div>

                      <div className="p-6">
                        {/* Course Title & Description */}
                        <h3 className="mb-2 line-clamp-2 text-xl font-bold text-gray-900 dark:text-white">
                          {course.title}
                        </h3>
                        <p className="mb-4 line-clamp-2 text-gray-600 dark:text-slate-300">
                          {course.description}
                        </p>

                        {/* Tags */}
                        <div className="mb-4 flex flex-wrap gap-2">
                          {course.tags.slice(0, 3).map(tag => (
                            <span
                              key={tag}
                              className="rounded-lg bg-blue-100 px-2 py-1 text-xs text-blue-700 dark:bg-blue-900 dark:text-blue-300"
                            >
                              {tag}
                            </span>
                          ))}
                        </div>

                        {/* Course Stats */}
                        <div className="mb-4 flex items-center justify-between text-sm text-gray-500 dark:text-slate-400">
                          <div className="flex items-center gap-1">
                            <svg
                              className="h-4 w-4 text-yellow-500"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                            >
                              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                            <span>{course.rating}</span>
                          </div>
                          <span>{course.students} students</span>
                          <span>{course.duration}</span>
                        </div>

                        {/* Instructor */}
                        <div className="mb-4 flex items-center gap-3">
                          <img
                            src={course.instructor.avatar}
                            alt={course.instructor.name}
                            className="h-8 w-8 rounded-full"
                          />
                          <span className="text-sm text-gray-600 dark:text-slate-300">
                            {course.instructor.name}
                          </span>
                        </div>

                        {/* Price */}
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <span className="text-2xl font-bold text-gray-900 dark:text-white">
                              {course.price}
                            </span>
                            {course.originalPrice && (
                              <span className="text-sm text-gray-500 line-through dark:text-slate-400">
                                {course.originalPrice}
                              </span>
                            )}
                          </div>
                          <div className="flex items-center gap-2 font-medium text-blue-600 dark:text-blue-400">
                            Learn More
                            <svg
                              className="h-4 w-4"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M9 5l7 7-7 7"
                              />
                            </svg>
                          </div>
                        </div>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          </section>
        </TabsContent>
      </Tabs>
    </div>
  );
};
