import React, { useState } from 'react';
import Link from 'next/link';
import { CertificationsPageData } from '@/constant/certifications';

export const CertificationsPageComponent = ({ data }: { data: CertificationsPageData }) => {
  const [selectedProvider, setSelectedProvider] = useState<string>('all');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedLevel, setSelectedLevel] = useState<string>('all');
  const [selectedFormat, setSelectedFormat] = useState<string>('all');
  const [selectedDuration, setSelectedDuration] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [sortBy, setSortBy] = useState<string>('featured');

  const filteredCertifications = data.certifications.filter(cert => {
    const matchesProvider = selectedProvider === 'all' || cert.provider.id === selectedProvider;
    const matchesCategory = selectedCategory === 'all' || cert.category.toLowerCase() === selectedCategory;
    const matchesLevel = selectedLevel === 'all' || cert.level.toLowerCase() === selectedLevel;
    const matchesFormat = selectedFormat === 'all' || cert.format.toLowerCase() === selectedFormat;
    const matchesSearch = searchQuery === '' || 
      cert.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      cert.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      cert.provider.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      cert.skills.some(skill => skill.toLowerCase().includes(searchQuery.toLowerCase()));
    
    return matchesProvider && matchesCategory && matchesLevel && matchesFormat && matchesSearch;
  });

  const sortedCertifications = [...filteredCertifications].sort((a, b) => {
    switch (sortBy) {
      case 'rating':
        return b.rating - a.rating;
      case 'price':
        return parseFloat(a.price.replace(/[^0-9.]/g, '')) - parseFloat(b.price.replace(/[^0-9.]/g, ''));
      case 'alphabetical':
        return a.title.localeCompare(b.title);
      case 'featured':
      default:
        return (b.featured ? 1 : 0) - (a.featured ? 1 : 0);
    }
  });

  const resetFilters = () => {
    setSelectedProvider('all');
    setSelectedCategory('all');
    setSelectedLevel('all');
    setSelectedFormat('all');
    setSelectedDuration('all');
    setSearchQuery('');
    setSortBy('featured');
  };

  return (
    <div className="bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-900 dark:to-slate-800 min-h-screen">
      <section className={`relative ${data.header.backgroundGradient} text-white py-24 px-4 overflow-hidden`}>
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-20 left-10 w-20 h-20 bg-white rounded-full blur-xl"></div>
          <div className="absolute top-40 right-20 w-32 h-32 bg-white rounded-full blur-2xl"></div>
          <div className="absolute bottom-20 left-1/3 w-24 h-24 bg-white rounded-full blur-xl"></div>
        </div>
        
        <div className="max-w-7xl mx-auto text-center relative z-10">
          <div className="flex justify-center mb-8">
            <div className="bg-white/20 backdrop-blur-sm p-6 rounded-3xl shadow-2xl">
              {data.header.icon}
            </div>
          </div>
          <h1 className="text-5xl lg:text-7xl font-bold mb-6 leading-tight bg-gradient-to-r from-white to-blue-100 bg-clip-text text-transparent">
            {data.header.title}
          </h1>
          <p className="text-xl lg:text-2xl text-white/90 mb-8 max-w-4xl mx-auto font-medium">
            {data.header.subtitle}
          </p>
          <p className="text-lg text-white/80 max-w-3xl mx-auto leading-relaxed">
            {data.header.description}
          </p>
        </div>

        <div className="max-w-6xl mx-auto mt-20 relative z-10">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <div className="bg-white/15 backdrop-blur-md rounded-3xl p-8 text-center border border-white/20 hover:bg-white/20 transition-all duration-300">
              <div className="text-4xl font-bold mb-3 bg-gradient-to-r from-white to-blue-100 bg-clip-text text-transparent">{data.stats.totalCertifications}</div>
              <div className="text-white/90 font-medium">Total Certifications</div>
            </div>
            <div className="bg-white/15 backdrop-blur-md rounded-3xl p-8 text-center border border-white/20 hover:bg-white/20 transition-all duration-300">
              <div className="text-4xl font-bold mb-3 bg-gradient-to-r from-white to-blue-100 bg-clip-text text-transparent">{data.stats.partneredCompanies}</div>
              <div className="text-white/90 font-medium">Partner Companies</div>
            </div>
            <div className="bg-white/15 backdrop-blur-md rounded-3xl p-8 text-center border border-white/20 hover:bg-white/20 transition-all duration-300">
              <div className="text-4xl font-bold mb-3 bg-gradient-to-r from-white to-blue-100 bg-clip-text text-transparent">{data.stats.averageRating}</div>
              <div className="text-white/90 font-medium">Average Rating</div>
            </div>
            <div className="bg-white/15 backdrop-blur-md rounded-3xl p-8 text-center border border-white/20 hover:bg-white/20 transition-all duration-300">
              <div className="text-4xl font-bold mb-3 bg-gradient-to-r from-white to-blue-100 bg-clip-text text-transparent">{data.stats.successRate}</div>
              <div className="text-white/90 font-medium">Success Rate</div>
            </div>
          </div>
        </div>
      </section>

      <section className="py-16 px-4 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm border-b border-gray-200 dark:border-slate-700">
        <div className="max-w-7xl mx-auto">
          <div className="mb-10">
            <div className="relative max-w-3xl mx-auto">
              <input
                type="text"
                placeholder="Search certifications, providers, or skills..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full px-8 py-5 rounded-2xl border-2 border-blue-200 dark:border-slate-600 bg-white dark:bg-slate-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-slate-400 focus:ring-4 focus:ring-blue-500/30 focus:border-blue-500 text-lg shadow-lg transition-all duration-300"
              />
              <div className="absolute right-6 top-1/2 transform -translate-y-1/2">
                <svg className="w-6 h-6 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
            </div>
          </div>

          <div className="grid lg:grid-cols-2 gap-8">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Filters</h3>
              <div className="grid grid-cols-2 gap-4">
                <select
                  value={selectedProvider}
                  onChange={(e) => setSelectedProvider(e.target.value)}
                  className="px-4 py-3 rounded-xl border-2 border-gray-200 dark:border-slate-600 bg-white dark:bg-slate-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300"
                >
                  <option value="all">All Providers</option>
                  {data.filters.providers.map(provider => (
                    <option key={provider.id} value={provider.id}>
                      {provider.label}
                    </option>
                  ))}
                </select>

                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="px-4 py-3 rounded-xl border-2 border-gray-200 dark:border-slate-600 bg-white dark:bg-slate-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300"
                >
                  <option value="all">All Categories</option>
                  {data.filters.categories.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.label}
                    </option>
                  ))}
                </select>

                <select
                  value={selectedLevel}
                  onChange={(e) => setSelectedLevel(e.target.value)}
                  className="px-4 py-3 rounded-xl border-2 border-gray-200 dark:border-slate-600 bg-white dark:bg-slate-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300"
                >
                  <option value="all">All Levels</option>
                  {data.filters.levels.map(level => (
                    <option key={level.id} value={level.id}>
                      {level.label}
                    </option>
                  ))}
                </select>

                <select
                  value={selectedFormat}
                  onChange={(e) => setSelectedFormat(e.target.value)}
                  className="px-4 py-3 rounded-xl border-2 border-gray-200 dark:border-slate-600 bg-white dark:bg-slate-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300"
                >
                  <option value="all">All Formats</option>
                  {data.filters.formats.map(format => (
                    <option key={format.id} value={format.id}>
                      {format.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="flex flex-col lg:flex-row lg:items-end lg:justify-between gap-4">
              <div className="flex items-center gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-slate-300 mb-2">Sort by</label>
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="px-4 py-3 rounded-xl border-2 border-gray-200 dark:border-slate-600 bg-white dark:bg-slate-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300"
                  >
                    <option value="featured">Featured</option>
                    <option value="rating">Rating</option>
                    <option value="price">Price</option>
                    <option value="alphabetical">A-Z</option>
                  </select>
                </div>
              </div>

              <div className="flex items-center gap-4">
                <span className="text-gray-600 dark:text-slate-300 font-medium">
                  {sortedCertifications.length} of {data.certifications.length} certifications
                </span>
                <button
                  onClick={resetFilters}
                  className="px-6 py-3 text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/30 rounded-xl transition-all duration-300 font-medium"
                >
                  Clear Filters
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="py-20 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {sortedCertifications.map((cert) => (
              <Link key={cert.id} href={cert.link}>
                <div className="bg-white dark:bg-slate-800 rounded-3xl shadow-xl hover:shadow-2xl transition-all duration-500 overflow-hidden group cursor-pointer border border-gray-100 dark:border-slate-700 hover:border-blue-200 dark:hover:border-blue-700 relative">
                  <div className="absolute top-4 left-4 z-10 flex gap-2">
                    {cert.featured && (
                      <span className="px-3 py-1 bg-gradient-to-r from-yellow-400 to-orange-500 text-white rounded-full text-xs font-bold">
                        Featured
                      </span>
                    )}
                    {cert.trending && (
                      <span className="px-3 py-1 bg-gradient-to-r from-pink-500 to-red-500 text-white rounded-full text-xs font-bold">
                        Trending
                      </span>
                    )}
                  </div>

                  <div className="absolute top-4 right-4 z-10">
                    <span className={`px-3 py-1 rounded-full text-xs font-bold ${
                      cert.level === 'Beginner' ? 'bg-green-500 text-white' :
                      cert.level === 'Intermediate' ? 'bg-yellow-500 text-white' :
                      cert.level === 'Advanced' ? 'bg-orange-500 text-white' :
                      'bg-purple-500 text-white'
                    }`}>
                      {cert.level}
                    </span>
                  </div>

                  <div className="relative h-56 bg-gradient-to-br from-blue-500 to-purple-600 overflow-hidden">
                    <img 
                      src={cert.image} 
                      alt={cert.title}
                      className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                    />
                    <div className="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-colors duration-300"></div>
                  </div>

                  <div className="p-6">
                    <div className="flex items-center gap-3 mb-4">
                      <img 
                        src={cert.provider.logo} 
                        alt={cert.provider.name}
                        className="w-8 h-8 rounded-lg"
                      />
                      <span className={`text-sm font-semibold ${cert.provider.color}`}>
                        {cert.provider.name}
                      </span>
                    </div>

                    <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3 line-clamp-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">
                      {cert.title}
                    </h3>
                    <p className="text-gray-600 dark:text-slate-300 mb-4 line-clamp-2 text-sm leading-relaxed">
                      {cert.description}
                    </p>

                    <div className="flex flex-wrap gap-2 mb-4">
                      {cert.skills.slice(0, 3).map((skill) => (
                        <span key={skill} className="px-3 py-1 bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-lg text-xs font-medium">
                          {skill}
                        </span>
                      ))}
                      {cert.skills.length > 3 && (
                        <span className="px-3 py-1 bg-gray-100 dark:bg-slate-700 text-gray-600 dark:text-slate-300 rounded-lg text-xs">
                          +{cert.skills.length - 3} more
                        </span>
                      )}
                    </div>

                    <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                      <div className="flex items-center gap-2">
                        <svg className="w-4 h-4 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                        <span className="text-gray-600 dark:text-slate-300">
                          {cert.rating} ({cert.totalReviews})
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <svg className="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span className="text-gray-600 dark:text-slate-300">{cert.duration}</span>
                      </div>
                    </div>

                    <div className="bg-gray-50 dark:bg-slate-700/50 rounded-xl p-3 mb-4">
                      <div className="grid grid-cols-3 gap-2 text-xs text-gray-600 dark:text-slate-300">
                        <div className="text-center">
                          <div className="font-semibold text-gray-900 dark:text-white">
                            {cert.examDetails.questions}
                          </div>
                          <div>Questions</div>
                        </div>
                        <div className="text-center">
                          <div className="font-semibold text-gray-900 dark:text-white">
                            {cert.examDetails.timeLimit}
                          </div>
                          <div>Time Limit</div>
                        </div>
                        <div className="text-center">
                          <div className="font-semibold text-gray-900 dark:text-white">
                            {cert.examDetails.passingScore}
                          </div>
                          <div>Pass Score</div>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center gap-2">
                        <span className="text-2xl font-bold text-gray-900 dark:text-white">
                          {cert.price}
                        </span>
                        {cert.originalPrice && (
                          <span className="text-sm text-gray-500 dark:text-slate-400 line-through">
                            {cert.originalPrice}
                          </span>
                        )}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-slate-400">
                        Valid for {cert.validityPeriod}
                      </div>
                    </div>

                    <div className="flex items-center justify-center gap-2 text-blue-600 dark:text-blue-400 font-semibold group-hover:text-blue-700 dark:group-hover:text-blue-300 transition-colors duration-300">
                      View Certification
                      <svg className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>

          {sortedCertifications.length === 0 && (
            <div className="text-center py-20">
              <div className="text-gray-400 dark:text-slate-500 text-8xl mb-6">🏆</div>
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                No certifications found
              </h3>
              <p className="text-gray-600 dark:text-slate-300 mb-8 text-lg">
                Try adjusting your search criteria or explore different categories
              </p>
              <button
                onClick={resetFilters}
                className="px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl"
              >
                Clear All Filters
              </button>
            </div>
          )}
        </div>
      </section>

      <section className="py-20 px-4 bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-700 text-white relative overflow-hidden">
        <div className="absolute inset-0 opacity-20">
          <div className="absolute top-10 left-10 w-32 h-32 bg-white rounded-full blur-3xl"></div>
          <div className="absolute bottom-10 right-10 w-40 h-40 bg-white rounded-full blur-3xl"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-white rounded-full blur-3xl"></div>
        </div>
        
        <div className="max-w-5xl mx-auto text-center relative z-10">
          <h2 className="text-4xl lg:text-5xl font-bold mb-8 leading-tight">
            {data.cta.title}
          </h2>
          <p className="text-xl text-white/90 mb-10 max-w-3xl mx-auto leading-relaxed">
            {data.cta.description}
          </p>
          <Link href={data.cta.buttonLink}>
            <button className="px-10 py-5 bg-white text-blue-600 rounded-2xl font-bold hover:bg-gray-100 transition-all duration-300 text-lg shadow-2xl hover:shadow-3xl transform hover:scale-105">
              {data.cta.buttonText}
            </button>
          </Link>
        </div>
      </section>
    </div>
  );
};