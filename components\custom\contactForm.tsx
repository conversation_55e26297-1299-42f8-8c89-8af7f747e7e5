import { useState } from 'react';
import Image from 'next/image';
import axios from 'axios';
import { z } from 'zod';

const contactSchema = z.object({
  fullName: z.string().min(1, 'Full name is required'),
  email: z.string().email('Invalid email address'),
  phone: z.string().optional(),
  subject: z.string().min(1, 'Subject is required'),
  message: z.string().min(1, 'Message is required'),
});

const ContactSection = () => {
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    phone: '',
    subject: '',
    message: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState('idle');
  const [formError, setFormError] = useState<string | null>(null);

  const handleInputChange = (e: React.ChangeEvent<
        HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
      >) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormError(null);

    const result = contactSchema.safeParse(formData);
    if (!result.success) {
      setFormError(result.error.errors[0]?.message || 'Validation failed');
      setSubmitStatus('error');
      return;
    }

    setIsSubmitting(true);

    try {
      const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || '';
      const url = new URL('/api/v1/contacts', baseUrl);
      const response = await axios.post(url.toString(), formData);

      if (response.data?.success) {
        setSubmitStatus('success');
        setFormData({
          fullName: '',
          email: '',
          phone: '',
          subject: '',
          message: '',
        });
        setTimeout(() => setSubmitStatus('idle'), 3000);
      } else {
        console.log(response.data);
        setFormError(response.data?.message || 'Submission failed');
        setSubmitStatus('error');
      }
    } catch (error: any) {
      console.log(error.response?.data);
      setFormError(error?.response?.data?.message || 'Submission failed');
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <section className="relative overflow-hidden bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30 py-16 lg:py-20 dark:from-gray-900 dark:via-blue-900/10 dark:to-purple-900/10">
      {/* Background Gradients */}
      <div className="pointer-events-none absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(59,130,246,0.1),transparent_50%)] dark:bg-[radial-gradient(circle_at_30%_20%,rgba(59,130,246,0.05),transparent_50%)]"></div>
      <div className="pointer-events-none absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(147,51,234,0.1),transparent_50%)] dark:bg-[radial-gradient(circle_at_70%_80%,rgba(147,51,234,0.05),transparent_50%)]"></div>

      <div className="relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="mb-12 text-center">
          <div className="mb-4 inline-flex items-center gap-2 rounded-full bg-gradient-to-r from-green-100 to-blue-100 px-4 py-2 text-sm font-medium text-gray-700 dark:from-green-900/50 dark:to-blue-900/50 dark:text-gray-300">
            <span className="text-lg">✉️</span>
            Direct Communication
          </div>
          <h2 className="font-nunito mb-4 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl lg:text-5xl dark:text-white">
            Get in{' '}
            <span className="bg-gradient-to-r from-blue-600 to-purple-800 bg-clip-text text-transparent">
              Touch
            </span>
          </h2>
          <p className="mx-auto max-w-2xl text-lg text-gray-600 dark:text-gray-300">
            Ready to start a conversation? Fill out the form below and we'll
            get back to you as soon as possible.
          </p>
        </div>

        <div className="grid items-center gap-12 lg:grid-cols-12">
          {/* Left Side - Image */}
          <div className="relative order-2 lg:order-1 lg:col-span-5">
            <div className="absolute -inset-4 rounded-3xl bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 opacity-20 blur-2xl"></div>
            <div className="relative rounded-3xl bg-white p-3 shadow-2xl dark:bg-gray-800">
              <Image
                src="https://images.unsplash.com/photo-1556761175-b413da4baf72?w=600&h=600&fit=crop"
                width={500}
                height={500}
                className="rounded-2xl"
                alt="Contact form"
              />
            </div>

            {/* Floating Elements */}
            <div className="absolute -top-3 -right-3 rounded-xl bg-white p-3 shadow-lg dark:bg-gray-800">
              <div className="flex items-center gap-2">
                <div className="h-2.5 w-2.5 rounded-full bg-green-500"></div>
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Online Now
                </span>
              </div>
            </div>

            <div className="absolute -bottom-3 -left-3 rounded-xl bg-white p-3 shadow-lg dark:bg-gray-800">
              <div className="flex items-center gap-2">
                <span className="text-lg">⚡</span>
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Quick Response
                </span>
              </div>
            </div>
          </div>

          {/* Right Side - Form Card */}
          <div className="order-1 lg:order-2 lg:col-span-7">
            <div className="relative">
              {/* Card Background with Gradient Border */}
              <div className="absolute -inset-1 rounded-3xl bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 opacity-20 blur-lg"></div>
              <div className="relative rounded-3xl border border-white/50 bg-white/80 p-6 shadow-2xl backdrop-blur-xl lg:p-8 dark:border-gray-700/50 dark:bg-gray-800/80">
                {/* Form Header */}
                <div className="mb-6 text-center">
                  <div className="mb-3 inline-flex h-10 w-10 items-center justify-center rounded-lg bg-gray-100 dark:bg-gray-700">
                    <span className="text-lg text-gray-600 dark:text-gray-300">
                      📝
                    </span>
                  </div>
                  <h3 className="font-nunito mb-2 text-xl font-semibold text-gray-900 dark:text-white">
                    Reach Out Confidentially
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    Your privacy is our priority — your information is never
                    shared, and we'll never spam your inbox or phone.
                  </p>

                  {/* Privacy Badge */}
                  <div className="mt-3 mb-4 inline-flex items-center gap-2 rounded-lg border border-green-200 bg-green-50 px-3 py-2 dark:border-green-700 dark:bg-green-900/30">
                    <span className="text-sm text-green-600 dark:text-green-400">
                      🔒
                    </span>
                    <span className="text-xs font-medium text-green-800 dark:text-green-300">
                      Your data is safe with us
                    </span>
                  </div>
                </div>

                {/* Success/Error Message */}
                {submitStatus === 'success' && (
                  <div className="mb-4 rounded-lg border border-green-200/50 bg-green-50 p-3 dark:border-green-700/50 dark:bg-green-900/30">
                    <div className="flex items-center gap-2">
                      <div className="flex h-5 w-5 flex-shrink-0 items-center justify-center rounded-full bg-green-100 dark:bg-green-800">
                        <span className="text-xs text-green-600 dark:text-green-300">
                          ✓
                        </span>
                      </div>
                      <p className="text-sm font-medium text-green-800 dark:text-green-300">
                        Message sent successfully!
                      </p>
                    </div>
                  </div>
                )}
                {formError && (
                  <div className="mb-4 rounded-lg border border-red-200/50 bg-red-50 p-3 dark:border-red-700/50 dark:bg-red-900/30">
                    <div className="flex items-center gap-2">
                      <div className="flex h-5 w-5 flex-shrink-0 items-center justify-center rounded-full bg-red-100 dark:bg-red-800">
                        <span className="text-xs text-red-600 dark:text-red-300">
                          !
                        </span>
                      </div>
                      <p className="text-sm font-medium text-red-800 dark:text-red-300">
                        {formError}
                      </p>
                    </div>
                  </div>
                )}

                {/* Contact Form */}
                <form onSubmit={handleSubmit} className="space-y-4">
                  {/* Name and Email Row */}
                  <div className="grid gap-4 sm:grid-cols-2">
                    <div>
                      <label
                        htmlFor="fullName"
                        className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300"
                      >
                        Full Name *
                      </label>
                      <input
                        type="text"
                        id="fullName"
                        name="fullName"
                        value={formData.fullName}
                        onChange={handleInputChange}
                        required
                        className="w-full rounded-lg border border-gray-200 bg-gray-50/30 px-4 py-3 text-sm text-gray-900 placeholder-gray-400 transition-all duration-300 focus:border-blue-500 focus:bg-white focus:ring-2 focus:ring-blue-500/20 focus:outline-none dark:border-gray-600 dark:bg-gray-700/30 dark:text-white dark:placeholder-gray-500 dark:focus:border-blue-400 dark:focus:bg-gray-700 dark:focus:ring-blue-400/20"
                        placeholder="Your full name"
                      />
                    </div>
                    <div>
                      <label
                        htmlFor="email"
                        className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300"
                      >
                        Email Address *
                      </label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        required
                        className="w-full rounded-lg border border-gray-200 bg-gray-50/30 px-4 py-3 text-sm text-gray-900 placeholder-gray-400 transition-all duration-300 focus:border-blue-500 focus:bg-white focus:ring-2 focus:ring-blue-500/20 focus:outline-none dark:border-gray-600 dark:bg-gray-700/30 dark:text-white dark:placeholder-gray-500 dark:focus:border-blue-400 dark:focus:bg-gray-700 dark:focus:ring-blue-400/20"
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>

                  {/* Phone and Subject Row */}
                  <div className="grid gap-4 sm:grid-cols-2">
                    <div>
                      <label
                        htmlFor="phone"
                        className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300"
                      >
                        Phone Number
                      </label>
                      <input
                        type="tel"
                        id="phone"
                        name="phone"
                        value={formData.phone}
                        onChange={handleInputChange}
                        className="w-full rounded-lg border border-gray-200 bg-gray-50/30 px-4 py-3 text-sm text-gray-900 placeholder-gray-400 transition-all duration-300 focus:border-blue-500 focus:bg-white focus:ring-2 focus:ring-blue-500/20 focus:outline-none dark:border-gray-600 dark:bg-gray-700/30 dark:text-white dark:placeholder-gray-500 dark:focus:border-blue-400 dark:focus:bg-gray-700 dark:focus:ring-blue-400/20"
                        placeholder="Your phone number"
                      />
                    </div>
                    <div>
                      <label
                        htmlFor="subject"
                        className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300"
                      >
                        Subject
                      </label>
                      <input
                        type="text"
                        id="subject"
                        name="subject"
                        value={formData.subject}
                        onChange={handleInputChange}
                        className="w-full rounded-lg border border-gray-200 bg-gray-50/30 px-4 py-3 text-sm text-gray-900 placeholder-gray-400 transition-all duration-300 focus:border-blue-500 focus:bg-white focus:ring-2 focus:ring-blue-500/20 focus:outline-none dark:border-gray-600 dark:bg-gray-700/30 dark:text-white dark:placeholder-gray-500 dark:focus:border-blue-400 dark:focus:bg-gray-700 dark:focus:ring-blue-400/20"
                        placeholder="Subject line"
                      />
                    </div>
                  </div>

                  {/* Message */}
                  <div>
                    <label
                      htmlFor="message"
                      className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300"
                    >
                      Message *
                    </label>
                    <textarea
                      id="message"
                      name="message"
                      value={formData.message}
                      onChange={handleInputChange}
                      required
                      rows={4}
                      className="w-full rounded-lg border border-gray-200 bg-gray-50/30 px-4 py-3 text-sm text-gray-900 placeholder-gray-400 transition-all duration-300 focus:border-blue-500 focus:bg-white focus:ring-2 focus:ring-blue-500/20 focus:outline-none dark:border-gray-600 dark:bg-gray-700/30 dark:text-white dark:placeholder-gray-500 dark:focus:border-blue-400 dark:focus:bg-gray-700 dark:focus:ring-blue-400/20"
                      placeholder="Tell us about your project or inquiry..."
                    />
                  </div>

                  {/* Submit Button */}
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full rounded-lg bg-gradient-to-r from-blue-600 to-purple-600 px-6 py-3 text-sm font-medium text-white shadow-lg transition-all duration-300 hover:from-blue-700 hover:to-purple-700 hover:shadow-xl focus:ring-2 focus:ring-blue-500/20 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50 dark:shadow-lg dark:hover:shadow-xl"
                  >
                    {isSubmitting ? (
                      <span className="flex items-center justify-center gap-2">
                        <svg
                          className="h-4 w-4 animate-spin"
                          viewBox="0 0 24 24"
                        >
                          <circle
                            className="opacity-25"
                            cx="12"
                            cy="12"
                            r="10"
                            stroke="currentColor"
                            strokeWidth="4"
                            fill="none"
                          />
                          <path
                            className="opacity-75"
                            fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                          />
                        </svg>
                        Sending Message...
                      </span>
                    ) : (
                      <span className="flex items-center justify-center gap-2">
                        <span>Send Message</span>
                        {/* <svg
                          className="h-4 w-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
                          />
                        </svg> */}
                      </span>
                    )}
                  </button>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ContactSection;