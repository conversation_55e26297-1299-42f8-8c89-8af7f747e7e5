import React, { useState } from 'react';
import { CourseData } from '@/constant/courses';
import Link from 'next/link';

export const CourseComponent = ({ course }: { course: CourseData }) => {
  const [activeTab, setActiveTab] = useState('overview');

  return (
    <div className="bg-white dark:bg-slate-900 min-h-screen">
      {/* Header */}
      <Link href={course.header.backLink}>
        <div className="bg-gradient-to-r from-blue-600 to-purple-700 px-4 py-6">
          <div className="max-w-7xl mx-auto">
            <button className="flex items-center gap-2 text-white/80 hover:text-white mb-4 transition-colors">
              {course.header.icon}
              {course.header.backLabel}
            </button>
          </div>
        </div>
      </Link>

      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-blue-600 to-purple-700 text-white pt-12 pb-20 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <div className="flex items-center gap-4">
                <div className="bg-white/20 p-4 rounded-2xl">
                  {course.hero.icon}
                </div>
                <div>
                  <div className="text-blue-200 font-medium mb-1">{course.hero.badge}</div>
                  <h1 className="text-4xl lg:text-5xl font-bold leading-tight">{course.hero.title}</h1>
                </div>
              </div>
              <p className="text-xl text-blue-100 leading-relaxed">{course.hero.description}</p>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                {course.hero.stats.map((stat) => (
                  <div className="text-center" key={stat.label}>
                    <div className="bg-white/20 p-3 rounded-xl mb-2 inline-block">{stat.icon}</div>
                    <div className="text-sm text-blue-200">{stat.label}</div>
                    <div className="font-semibold">{stat.value}</div>
                  </div>
                ))}
              </div>
              <div className="flex flex-col sm:flex-row gap-4">
                {course.hero.actions.map((action, idx) => (
                  <button key={action.label} className={action.className}>
                    {action.icon && <span className="mr-2">{action.icon}</span>}
                    {action.label}
                  </button>
                ))}
              </div>
            </div>
            {/* Code visualization */}
            <div className="relative">
              <div className="bg-slate-900 rounded-2xl p-6 shadow-2xl">
                <div className="flex items-center gap-2 mb-4">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-slate-400 text-sm ml-4">{course.hero.codeBlock.filename}</span>
                </div>
                <pre className="text-green-400 text-sm font-mono overflow-hidden">
                  {course.hero.codeBlock.code}
                </pre>
              </div>
              {course.hero.codeBlock.badges.map((badge) => (
                <div key={badge.label} className={badge.className}>
                  {badge.label}
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Tab Navigation */}
      <section className="border-b border-slate-200 dark:border-slate-700 px-4">
        <div className="max-w-7xl mx-auto">
          <nav className="flex space-x-8">
            {[
              { id: 'overview', label: 'Overview' },
              { id: 'curriculum', label: 'Curriculum' },
              { id: 'projects', label: 'Projects' },
              { id: 'certification', label: 'Certification' },
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-2 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-blue-600 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-slate-500 dark:text-slate-300 hover:text-slate-700 dark:hover:text-white'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>
      </section>

      {/* Content Sections */}
      <div className="max-w-7xl mx-auto px-4 py-16">
        {/* Overview Tab */}
        {activeTab === 'overview' && (
          <div className="space-y-16">
            {/* Why Get Certified */}
            <div className="grid lg:grid-cols-2 gap-12">
              <div>
                <h2 className="text-3xl font-bold text-slate-800 dark:text-white mb-6">Why Get Certified in {course.hero.title}?</h2>
                <div className="space-y-4">
                  {course.overview.why.map((item) => (
                    <div className="flex items-start gap-4" key={item.title}>
                      {item.icon}
                      <div>
                        <h3 className="font-semibold text-slate-800 dark:text-white">{item.title}</h3>
                        <p className="text-slate-600 dark:text-slate-300">{item.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              {/* Skills Progress */}
              <div>
                <h3 className="text-2xl font-bold text-slate-800 dark:text-white mb-6">Skills You'll Master</h3>
                <div className="space-y-4">
                  {course.overview.skills.map((skill) => (
                    <div key={skill.name}>
                      <div className="flex justify-between mb-2">
                        <span className="font-medium text-slate-700 dark:text-slate-200">{skill.name}</span>
                        <span className="text-slate-500 dark:text-slate-400">{skill.level}%</span>
                      </div>
                      <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2">
                        <div
                          className={`${skill.color} h-2 rounded-full transition-all duration-1000`}
                          style={{ width: `${skill.level}%` }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
            {/* Who Should Enroll */}
            <div className="bg-slate-50 dark:bg-slate-800 rounded-2xl p-8">
              <h2 className="text-3xl font-bold text-slate-800 dark:text-white mb-6 text-center">Who Should Enroll?</h2>
              <div className="grid md:grid-cols-3 gap-8">
                {course.overview.who.map((who) => (
                  <div className="text-center" key={who.title}>
                    <div className={`${who.bg} p-4 rounded-2xl inline-block mb-4`}>{who.icon}</div>
                    <h3 className="font-semibold text-slate-800 dark:text-white mb-2">{who.title}</h3>
                    <p className="text-slate-600 dark:text-slate-300">{who.description}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Curriculum Tab */}
        {activeTab === 'curriculum' && (
          <div className="space-y-12">
            <h2 className="text-3xl font-bold text-slate-800 dark:text-white mb-8">Curriculum</h2>
            <div className="grid md:grid-cols-2 gap-10">
              {course.curriculum.map((mod) => (
                <div key={mod.module} className="bg-white dark:bg-slate-800 rounded-2xl shadow p-6 flex flex-col gap-4 border border-slate-100 dark:border-slate-700">
                  <div className="flex items-center gap-3">
                    <div className="bg-blue-100 dark:bg-blue-900 p-3 rounded-xl">{mod.icon}</div>
                    <div>
                      <div className="font-semibold text-blue-700 dark:text-blue-300">{mod.module}</div>
                      <div className="text-slate-700 dark:text-slate-200">{mod.title}</div>
                      <div className="text-xs text-slate-400 dark:text-slate-400">{mod.duration}</div>
                    </div>
                  </div>
                  <ul className="list-disc list-inside ml-2 text-slate-600 dark:text-slate-300">
                    {mod.topics.map((topic) => (
                      <li key={topic}>{topic}</li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Projects Tab */}
        {activeTab === 'projects' && (
          <div className="space-y-12">
            <h2 className="text-3xl font-bold text-slate-800 dark:text-white mb-8">Capstone Projects</h2>
            <div className="grid md:grid-cols-2 gap-10">
              {course.projects.map((proj) => (
                <div key={proj.title} className="bg-white dark:bg-slate-800 rounded-2xl shadow p-6 border border-slate-100 dark:border-slate-700">
                  <h3 className="font-semibold text-xl text-blue-700 dark:text-blue-300 mb-2">{proj.title}</h3>
                  <p className="text-slate-600 dark:text-slate-300 mb-3">{proj.description}</p>
                  <div className="flex flex-wrap gap-2">
                    {proj.tech.map((tech) => (
                      <span key={tech} className="bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-200 px-3 py-1 rounded-full text-xs font-medium">{tech}</span>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Certification Tab */}
        {activeTab === 'certification' && (
          <div className="space-y-12">
            <h2 className="text-3xl font-bold text-slate-800 dark:text-white mb-8">Certification & Career Support</h2>
            <div className="grid md:grid-cols-2 gap-10">
              <div className="bg-white dark:bg-slate-800 rounded-2xl shadow p-6 border border-slate-100 dark:border-slate-700 flex flex-col gap-4">
                {course.certification.items.map((item) => (
                  <div className="flex items-center gap-3" key={item.title}>
                    {item.icon}
                    <div>
                      <div className={`font-semibold ${item.color}`}>{item.title}</div>
                      <div className="text-slate-700 dark:text-slate-300">{item.description}</div>
                    </div>
                  </div>
                ))}
              </div>
              <div className="bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900 dark:to-purple-900 rounded-2xl flex items-center justify-center p-8">
                <div className="text-center space-y-4">
                  <div className="h-48 w-48 bg-white dark:bg-slate-800 rounded-xl shadow flex items-center justify-center mx-auto mb-4">
                    {course.certification.illustration.icon}
                  </div>
                  <div className="font-semibold text-lg text-blue-700 dark:text-blue-300">{course.certification.illustration.title}</div>
                  <div className="text-slate-600 dark:text-slate-300">{course.certification.illustration.description}</div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};