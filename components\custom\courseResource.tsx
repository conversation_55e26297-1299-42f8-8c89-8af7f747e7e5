'use client';
import React, { useEffect, useState } from 'react';
import axios from 'axios';
import { motion } from 'framer-motion';
import {
  Search,
  Filter,
  ChevronDown,
  X,
  ExternalLink,
  Tag,
  Award,
  BookOpen,
  Star,
  Clock,
  User,
  Eye,
  TrendingUp,
  CheckCircle,
  Heart,
  DollarSign,
  Calendar,
  Monitor,
  Play,
  GraduationCap,
} from 'lucide-react';

type CourseResourceType = {
  _id: string;
  title: string;
  description?: string;
  courseUrl: string;
  platform: 'Udemy' | 'Coursera' | 'edX' | 'YouTube' | 'LinkedIn Learning' | 'Other';
  isPaid: boolean;
  price?: number;
  category: string;
  subcategory: string;
  tags?: string[];
  level: 'Beginner' | 'Intermediate' | 'Advanced';
  benefits?: string[];
  duration?: string;
  recommendedBy: string[];
  isVerified?: boolean;
  isFeatured?: boolean;
  importanceLevel?: 'Core' | 'Reference' | 'Bonus';
  moderationStatus: 'pending' | 'approved' | 'rejected';
  visible: boolean;
  thumbnailUrl?: string;
  createdBy?: {
    _id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  updatedBy?: {
    _id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  createdAt: string;
  updatedAt: string;
};

type FilterState = {
  title?: string;
  category?: string;
  subcategory?: string;
  level?: string;
  platform?: string;
  isPaid?: string;
  tags?: string;
  isVerified?: string;
  isFeatured?: string;
  importanceLevel?: string;
};

type SortState = {
  field: 'createdAt' | 'updatedAt' | 'title' | 'recommendedBy' | 'price';
  order: 'asc' | 'desc';
};

function useDebounce<T>(value: T, delay: number) {
  const [debounced, setDebounced] = useState(value);
  useEffect(() => {
    const handler = setTimeout(() => setDebounced(value), delay);
    return () => clearTimeout(handler);
  }, [value, delay]);
  return debounced;
}

const getPlatformIcon = (platform: string) => {
  switch (platform) {
    case 'Udemy':
      return <Monitor className="h-4 w-4 text-purple-600" />;
    case 'Coursera':
      return <GraduationCap className="h-4 w-4 text-blue-600" />;
    case 'edX':
      return <BookOpen className="h-4 w-4 text-red-600" />;
    case 'YouTube':
      return <Play className="h-4 w-4 text-red-500" />;
    case 'LinkedIn Learning':
      return <Award className="h-4 w-4 text-blue-500" />;
    case 'Other':
      return <Monitor className="h-4 w-4 text-gray-500" />;
    default:
      return <Monitor className="h-4 w-4" />;
  }
};

const getLevelColor = (level: string) => {
  switch (level) {
    case 'Beginner':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
    case 'Intermediate':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
    case 'Advanced':
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200';
  }
};

const getImportanceColor = (level: string) => {
  switch (level) {
    case 'Core':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
    case 'Reference':
      return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
    case 'Bonus':
      return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200';
  }
};

const defaultThumbnail = '/images/internships.png';

export default function CourseResources() {
  const [courses, setCourses] = useState<CourseResourceType[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<FilterState>({});
  const [sort, setSort] = useState<SortState>({
    field: 'createdAt',
    order: 'desc',
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(12);
  const [meta, setMeta] = useState<{
    total: number;
    totalPages: number;
    page: number;
    limit: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  } | null>(null);

  const categoryOptions = [
    'Technology',
    'Marketing',
    'Finance',
    'HR',
    'Design',
    'Soft Skills',
    'Communication',
    'Entrepreneurship',
    'General Knowledge',
  ];

  const levelOptions = ['Beginner', 'Intermediate', 'Advanced'];
  const platformOptions = ['Udemy', 'Coursera', 'edX', 'YouTube', 'LinkedIn Learning', 'Other'];
  const importanceOptions = ['Core', 'Reference', 'Bonus'];

  const fetchCourses = async () => {
    try {
      setLoading(true);
      const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
      if (!baseUrl) {
        throw new Error('NEXT_PUBLIC_BASE_URL environment variable is not set');
      }

      const params = new URLSearchParams();
      if (searchTerm) params.append('title', searchTerm);
      Object.entries(filters).forEach(([key, value]) => {
        if (value) params.append(key, value);
      });
      params.append('sortBy', sort.field);
      params.append('sortOrder', sort.order);
      params.append('visible', 'true');
      params.append('isVerified', 'true');
      params.append('moderationStatus', 'approved');
      params.append('page', String(page));
      params.append('limit', String(limit));

      const url = new URL('/api/v1/courseResources', baseUrl);
      url.search = params.toString();

      const response = await axios.get(url.toString());
      if (!response.data.success) {
        throw new Error(response.data.error || 'Failed to fetch courses');
      }

      setCourses(response.data.data.resources);
      setMeta(response.data.data.meta);
      setError(null);
    } catch (err: any) {
      setCourses([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCourses();
  }, [filters, sort, useDebounce(searchTerm, 1000), page, limit]);

  const handleFilterChange = (key: keyof FilterState, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value || undefined,
    }));
    setPage(1);
  };

  const clearFilters = () => {
    setFilters({});
    setSearchTerm('');
    setPage(1);
  };

  const getCreatorName = (course: CourseResourceType) => {
    if (course.createdBy) {
      return `${course.createdBy.firstName} ${course.createdBy.lastName}`;
    }
    return 'Unknown Author';
  };

  const formatPrice = (price?: number) => {
    if (!price) return 'Free';
    return `₹${price.toLocaleString()}`;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-16">
        <div className="flex items-center space-x-2">
          <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-blue-600"></div>
          <span className="text-gray-600 dark:text-gray-400">
            Loading courses...
          </span>
        </div>
      </div>
    );
  }

  return (
    <div className="mx-auto max-w-7xl space-y-8 px-4 py-16 sm:px-6 lg:px-8">
      <div className="space-y-4 text-center">
        <div className="inline-flex items-center gap-2 rounded-full bg-gradient-to-r from-blue-100 to-purple-100 px-4 py-2 text-sm font-medium text-gray-700 dark:from-blue-900/50 dark:to-purple-900/50 dark:text-gray-300">
          <GraduationCap className="h-4 w-4" />
          Curated Course Resources
        </div>
        <h2 className="font-nunito text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl dark:text-white">
          Discover <span className="text-blue-600">Quality Courses</span>
        </h2>
        <p className="mx-auto max-w-2xl text-lg text-gray-600 dark:text-gray-300">
          Explore our handpicked collection of courses and educational resources to
          accelerate your learning journey.
        </p>
      </div>

      <div className="space-y-4">
        <div className="relative">
          <Search className="absolute top-3 left-3 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search courses..."
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
            className="w-full rounded-lg border border-gray-300 py-2 pr-4 pl-10 focus:border-transparent focus:ring-2 focus:ring-blue-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white"
          />
        </div>

        <div className="flex flex-wrap items-center gap-4">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center gap-2 rounded-lg border border-gray-300 bg-white px-4 py-2 transition-colors hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:hover:bg-gray-700"
          >
            <Filter className="h-4 w-4" />
            Filters
            <ChevronDown
              className={`h-4 w-4 transition-transform ${showFilters ? 'rotate-180' : ''}`}
            />
          </button>

          <select
            value={`${sort.field}-${sort.order}`}
            onChange={e => {
              const [field, order] = e.target.value.split('-') as [
                typeof sort.field,
                typeof sort.order,
              ];
              setSort({ field, order });
            }}
            className="rounded-lg border border-gray-300 bg-white px-4 py-2 focus:ring-2 focus:ring-blue-500 dark:border-gray-700 dark:bg-gray-800"
          >
            <option value="createdAt-desc">Newest First</option>
            <option value="createdAt-asc">Oldest First</option>
            <option value="title-asc">Title (A-Z)</option>
            <option value="title-desc">Title (Z-A)</option>
            <option value="price-asc">Price (Low to High)</option>
            <option value="price-desc">Price (High to Low)</option>
            <option value="recommendedBy-desc">Most Recommended</option>
          </select>

          {(Object.keys(filters).length > 0 || searchTerm) && (
            <button
              onClick={clearFilters}
              className="flex items-center gap-2 px-4 py-2 text-red-600 transition-colors hover:text-red-700"
            >
              <X className="h-4 w-4" />
              Clear All
            </button>
          )}
        </div>

        {showFilters && (
          <div className="grid grid-cols-1 gap-4 rounded-lg bg-gray-50 p-4 md:grid-cols-2 lg:grid-cols-3 dark:bg-gray-800">
            <div>
              <label className="mb-2 block text-sm font-medium">Category</label>
              <select
                value={filters.category || ''}
                onChange={e => handleFilterChange('category', e.target.value)}
                className="w-full rounded-lg border border-gray-300 bg-white px-3 py-2 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
              >
                <option value="">All Categories</option>
                {categoryOptions.map(cat => (
                  <option key={cat} value={cat}>
                    {cat}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="mb-2 block text-sm font-medium">Level</label>
              <select
                value={filters.level || ''}
                onChange={e => handleFilterChange('level', e.target.value)}
                className="w-full rounded-lg border border-gray-300 bg-white px-3 py-2 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
              >
                <option value="">All Levels</option>
                {levelOptions.map(level => (
                  <option key={level} value={level}>
                    {level}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="mb-2 block text-sm font-medium">Platform</label>
              <select
                value={filters.platform || ''}
                onChange={e => handleFilterChange('platform', e.target.value)}
                className="w-full rounded-lg border border-gray-300 bg-white px-3 py-2 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
              >
                <option value="">All Platforms</option>
                {platformOptions.map(platform => (
                  <option key={platform} value={platform}>
                    {platform}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="mb-2 block text-sm font-medium">
                Importance
              </label>
              <select
                value={filters.importanceLevel || ''}
                onChange={e =>
                  handleFilterChange('importanceLevel', e.target.value)
                }
                className="w-full rounded-lg border border-gray-300 bg-white px-3 py-2 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
              >
                <option value="">All Importance</option>
                {importanceOptions.map(importance => (
                  <option key={importance} value={importance}>
                    {importance}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="mb-2 block text-sm font-medium">Price Type</label>
              <select
                value={filters.isPaid || ''}
                onChange={e => handleFilterChange('isPaid', e.target.value)}
                className="w-full rounded-lg border border-gray-300 bg-white px-3 py-2 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
              >
                <option value="">All Courses</option>
                <option value="false">Free Only</option>
                <option value="true">Paid Only</option>
              </select>
            </div>

            <div>
              <label className="mb-2 block text-sm font-medium">Status</label>
              <select
                value={filters.isFeatured || ''}
                onChange={e => handleFilterChange('isFeatured', e.target.value)}
                className="w-full rounded-lg border border-gray-300 bg-white px-3 py-2 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
              >
                <option value="">All Courses</option>
                <option value="true">Featured Only</option>
                <option value="false">Non-Featured</option>
              </select>
            </div>
          </div>
        )}
      </div>

      {courses.length === 0 ? (
        <div className="flex flex-col items-center justify-center py-16 text-center">
          <GraduationCap className="mb-4 h-16 w-16 text-gray-400 dark:text-gray-600" />
          <h3 className="mb-2 text-xl font-semibold text-gray-600 dark:text-gray-400">
            No Courses Found
          </h3>
          <p className="text-gray-500 dark:text-gray-500">
            Try adjusting your filters or search terms to find more courses.
          </p>
        </div>
      ) : (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {courses.map((course, idx) => (
            <motion.div
              key={course._id}
              className="group relative overflow-hidden rounded-2xl border border-gray-200 bg-white shadow-lg transition-all duration-500 hover:scale-105 hover:shadow-xl dark:border-gray-700 dark:bg-gray-800"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: idx * 0.1 }}
            >
              <div className="relative">
                <img
                  src={course.thumbnailUrl === null ? defaultThumbnail : course.thumbnailUrl}
                  alt={course.title}
                  className="h-48 w-full object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />

                <div className="absolute top-4 left-4 flex flex-wrap gap-2">
                  {course.isFeatured && (
                    <div className="flex items-center gap-1 rounded-full bg-yellow-400 px-3 py-1 text-xs font-semibold text-yellow-800">
                      <Star className="h-3 w-3" />
                      Featured
                    </div>
                  )}
                  {course.isVerified && (
                    <div className="flex items-center gap-1 rounded-full bg-green-400 px-3 py-1 text-xs font-semibold text-green-800">
                      <CheckCircle className="h-3 w-3" />
                      Verified
                    </div>
                  )}
                </div>

                <div className="absolute top-4 right-4">
                  <div
                    className={`rounded-full px-3 py-1 text-xs font-semibold ${getImportanceColor(course.importanceLevel || 'Reference')}`}
                  >
                    {course.importanceLevel}
                  </div>
                </div>

                <div className="absolute bottom-4 left-4 flex items-center gap-4">
                  <div className="flex items-center gap-2">
                    <div className="flex items-center gap-1 text-white">
                      {getPlatformIcon(course.platform)}
                      <span className="text-sm font-medium">
                        {course.platform}
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center gap-1 text-white">
                    <DollarSign className="h-3 w-3" />
                    <span className="text-sm font-medium">
                      {course.isPaid ? formatPrice(course.price) : 'Free'}
                    </span>
                  </div>
                </div>
              </div>

              <div className="p-6">
                <div className="mb-3 flex flex-wrap items-center gap-2">
                  <span className="rounded-full bg-blue-100 px-3 py-1 text-xs font-medium text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                    {course.category}
                  </span>
                  <span
                    className={`rounded-full px-3 py-1 text-xs font-medium ${getLevelColor(course.level)}`}
                  >
                    {course.level}
                  </span>
                  {course.duration && (
                    <span className="rounded-full bg-gray-100 px-3 py-1 text-xs font-medium text-gray-700 dark:bg-gray-700 dark:text-gray-300">
                      {course.duration}
                    </span>
                  )}
                </div>

                <h3 className="font-nunito mb-2 line-clamp-2 text-lg font-bold text-gray-900 transition-colors group-hover:text-blue-600 dark:text-white dark:group-hover:text-blue-400">
                  {course.title}
                </h3>

                {course.description && (
                  <p className="mb-4 line-clamp-3 text-sm text-gray-600 dark:text-gray-400">
                    {course.description}
                  </p>
                )}

                <div className="mb-4 space-y-2">
                  <div className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
                    <User className="h-3 w-3" />
                    <span>By {getCreatorName(course)}</span>
                  </div>
                  <div className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
                    <Clock className="h-3 w-3" />
                    <span>
                      {new Date(course.createdAt).toLocaleDateString()}
                    </span>
                  </div>
                  {course.recommendedBy.length > 0 && (
                    <div className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
                      <Heart className="h-3 w-3" />
                      <span>{course.recommendedBy.length} recommendations</span>
                    </div>
                  )}
                </div>

                {course.tags && course.tags.length > 0 && (
                  <div className="mb-4 flex flex-wrap gap-1">
                    {course.tags.slice(0, 3).map((tag, tagIdx) => (
                      <span
                        key={tagIdx}
                        className="inline-flex items-center gap-1 rounded-full bg-gray-100 px-2 py-1 text-xs text-gray-600 dark:bg-gray-700 dark:text-gray-400"
                      >
                        <Tag className="h-2 w-2" />
                        {tag}
                      </span>
                    ))}
                    {course.tags.length > 3 && (
                      <span className="text-xs text-gray-500">
                        +{course.tags.length - 3} more
                      </span>
                    )}
                  </div>
                )}

                {course.benefits && course.benefits.length > 0 && (
                  <div className="mb-4">
                    <h4 className="mb-2 text-xs font-semibold text-gray-700 dark:text-gray-300">
                      Key Benefits:
                    </h4>
                    <ul className="space-y-1 text-xs text-gray-600 dark:text-gray-400">
                      {course.benefits.slice(0, 2).map((benefit, idx) => (
                        <li key={idx} className="flex items-start gap-2">
                          <CheckCircle className="mt-0.5 h-3 w-3 flex-shrink-0 text-green-500" />
                          <span className="line-clamp-2">{benefit}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                <a
                  href={course.courseUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex w-full items-center justify-center gap-2 rounded-lg bg-blue-600 px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:outline-none"
                >
                  View Course
                  <ExternalLink className="h-4 w-4" />
                </a>
              </div>
            </motion.div>
          ))}
        </div>
      )}

      {meta && meta.totalPages > 1 && (
        <div className="mt-12 flex items-center justify-center gap-4">
          <button
            onClick={() => setPage(p => Math.max(1, p - 1))}
            disabled={page === 1}
            className="rounded-lg border border-gray-300 bg-white px-4 py-2 text-gray-700 transition-colors hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700"
          >
            Previous
          </button>

          <div className="flex items-center gap-2">
            {Array.from({ length: Math.min(5, meta.totalPages) }, (_, i) => {
              const pageNum = i + 1;
              return (
                <button
                  key={pageNum}
                  onClick={() => setPage(pageNum)}
                  className={`rounded-lg px-3 py-2 text-sm font-medium transition-colors ${
                    page === pageNum
                      ? 'bg-blue-600 text-white'
                      : 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700'
                  }`}
                >
                  {pageNum}
                </button>
              );
            })}
            {meta.totalPages > 5 && (
              <>
                <span className="text-gray-500">...</span>
                <button
                  onClick={() => setPage(meta.totalPages)}
                  className={`rounded-lg px-3 py-2 text-sm font-medium transition-colors ${
                    page === meta.totalPages
                      ? 'bg-blue-600 text-white'
                      : 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700'
                  }`}
                >
                  {meta.totalPages}
                </button>
              </>
            )}
          </div>

          <button
            onClick={() => setPage(p => Math.min(meta.totalPages, p + 1))}
            disabled={page === meta.totalPages}
            className="rounded-lg border border-gray-300 bg-white px-4 py-2 text-gray-700 transition-colors hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700"
          >
            Next
          </button>
        </div>
      )}

      {meta && (
        <div className="text-center text-sm text-gray-500 dark:text-gray-400">
          Showing {(meta.page - 1) * meta.limit + 1} to{' '}
          {Math.min(meta.page * meta.limit, meta.total)} of {meta.total} courses
        </div>
      )}
    </div>
  );
}