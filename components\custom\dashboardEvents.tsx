'use client';
import React, { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import {
  Calendar,
  Clock,
  Users,
  Tag,
  Award,
  Filter,
  Search,
  Globe,
  User,
  DollarSign,
  Video,
  FileText,
  MapPinIcon,
  X,
  ChevronDown,
  Play,
  Download,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardAction,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { IEvent } from '@/types/IEvents';
import { ApiResponse } from '@/types/IGobal';
import {
  useGetRegisteredEventsQuery,
  useGetAllEventsQuery,
} from '@/store/features/events-features/eventsApi';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

type FilterState = {
  category?: string;
  level?: string;
  mode?: string;
  language?: string;
  isPaid?: string;
  isFeatured?: string;
  tags?: string;
  eventType?: 'upcoming' | 'past';
};

type SortState = {
  field: 'startTime' | 'createdAt' | 'title' | 'registeredUsers';
  order: 'asc' | 'desc';
};

interface EventsComponentProps {
  type: 'upcoming' | 'past' | 'all';
  registered?: boolean;
  initialFilters?: FilterState;
}

const formatDateTime = (dateString: string, timezone?: string) => {
  const date = new Date(dateString);
  const options: Intl.DateTimeFormatOptions = {
    timeZone: timezone || 'UTC',
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  };
  return date.toLocaleString('en-US', options);
};

const formatDuration = (minutes: number) => {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  if (hours > 0) {
    return `${hours}h ${mins}m`;
  }
  return `${mins}m`;
};

const getTimeAgo = (dateString: string) => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) return 'Just now';
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} min ago`;
  if (diffInSeconds < 86400)
    return `${Math.floor(diffInSeconds / 3600)} hours ago`;
  if (diffInSeconds < 2592000)
    return `${Math.floor(diffInSeconds / 86400)} days ago`;
  if (diffInSeconds < 31536000)
    return `${Math.floor(diffInSeconds / 2592000)} months ago`;
  return `${Math.floor(diffInSeconds / 31536000)} years ago`;
};

const getHostName = (event: IEvent) => {
  if (event.hostName) return event.hostName;
  if (event.hostedByUsers?.[0]) {
    const user = event.hostedByUsers[0].user;
    return `${user.firstName} ${user.lastName}`;
  }
  if (event.hostedByCompanies?.[0]) {
    return event.hostedByCompanies[0].user.name;
  }
  return 'Unknown Host';
};

const isEventUpcoming = (event: IEvent): boolean => {
  const now = new Date();
  const eventEndTime = event.endTime
    ? new Date(event.endTime)
    : new Date(event.startTime);
  return eventEndTime > now;
};

function useDebounce<T>(value: T, delay: number) {
  const [debounced, setDebounced] = useState(value);
  useEffect(() => {
    const handler = setTimeout(() => setDebounced(value), delay);
    return () => clearTimeout(handler);
  }, [value, delay]);
  return debounced;
}

const defaultEventImage = '/images/annie-spratt-QckxruozjRg-unsplash.jpg';

export default function EventsComponent({
  type,
  registered = false,
  initialFilters = {},
}: EventsComponentProps) {
  const [filteredEvents, setFilteredEvents] = useState<IEvent[]>([]);
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<FilterState>(initialFilters);
  const [sort, setSort] = useState<SortState>({
    field: 'startTime',
    order: type === 'upcoming' ? 'asc' : 'desc',
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [page, setPage] = useState(1);
  const [limit] = useState(12);

  const debouncedSearch = useDebounce(searchTerm, 1000);

  const categoryOptions = [
    'Web Development',
    'Data Science',
    'Mobile Development',
    'AI/ML',
    'DevOps',
    'Design',
    'Marketing',
  ];
  const levelOptions = ['Beginner', 'Intermediate', 'Advanced'];
  const modeOptions = ['Online', 'Offline'];
  const languageOptions = ['English', 'Hindi', 'Spanish', 'French', 'German'];

  const queryParams = {
    page: page.toString(),
    limit: limit.toString(),
    sortBy: sort.field,
    sortOrder: sort.order,
    isActive: 'true',
    isApproved: 'true',
    ...(debouncedSearch && { title: debouncedSearch }),
    ...(filters.category && { category: filters.category }),
    ...(filters.level && { level: filters.level }),
    ...(filters.mode && { mode: filters.mode }),
    ...(filters.language && { language: filters.language }),
    ...(filters.isPaid && { isPaid: filters.isPaid }),
    ...(filters.isFeatured && { isFeatured: filters.isFeatured }),
    ...(filters.tags && { tags: filters.tags }),
  };

  const {
    data: registeredData,
    isLoading: isRegisteredLoading,
    isError: isRegisteredError,
    refetch: refetchRegistered,
  } = useGetRegisteredEventsQuery(queryParams, {
    skip: !registered,
  });

  const {
    data: allData,
    isLoading: isAllLoading,
    isError: isAllError,
    refetch: refetchAll,
  } = useGetAllEventsQuery(queryParams, {
    skip: registered,
  });

  const data = registered ? registeredData : allData;
  const isLoading = registered ? isRegisteredLoading : isAllLoading;
  const isError = registered ? isRegisteredError : isAllError;
  const refetch = registered ? refetchRegistered : refetchAll;

  const filterEventsByType = useCallback(
    (events: IEvent[]) => {
      if (!events) return [];

      let filteredEvents = [...events];

      if (type === 'upcoming') {
        filteredEvents = filteredEvents.filter(isEventUpcoming);
      } else if (type === 'past') {
        filteredEvents = filteredEvents.filter(
          event => !isEventUpcoming(event)
        );
      } else if (type === 'all' && filters.eventType) {
        if (filters.eventType === 'upcoming') {
          filteredEvents = filteredEvents.filter(isEventUpcoming);
        } else if (filters.eventType === 'past') {
          filteredEvents = filteredEvents.filter(
            event => !isEventUpcoming(event)
          );
        }
      }

      return filteredEvents;
    },
    [type, filters.eventType]
  );

  useEffect(() => {
    if (data?.data?.events) {
      const filtered = filterEventsByType(data.data.events);
      setFilteredEvents(filtered);
    }
  }, [data, filterEventsByType]);

  useEffect(() => {
    refetch();
  }, [filters, sort, debouncedSearch, page, refetch]);

  const handleFilterChange = (key: keyof FilterState, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value || undefined,
    }));
    setPage(1);
  };

  const clearFilters = () => {
    setFilters({});
    setSearchTerm('');
    setPage(1);
  };

  const getEventTypeLabel = () => {
    switch (type) {
      case 'upcoming':
        return 'Upcoming';
      case 'past':
        return 'Past';
      case 'all':
        return filters.eventType
          ? filters.eventType === 'upcoming'
            ? 'Upcoming'
            : 'Past'
          : 'All';
      default:
        return 'All';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-16">
        <div className="h-12 w-12 animate-spin rounded-full border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex flex-col items-center justify-center py-16 text-center">
        <p className="text-red-600">
          Error loading events. Please try again later.
        </p>
        <Button onClick={() => refetch()} className="mt-4">
          Retry
        </Button>
      </div>
    );
  }

  const hasActiveFilters = Object.keys(filters).length > 0 || searchTerm;

  return (
    <div className="space-y-6">
      <div className="mb-6 space-y-4">
        <div className="relative">
          <Search className="absolute top-3 left-3 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder={`Search ${getEventTypeLabel().toLowerCase()} events...`}
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
            className="w-full rounded-lg border border-gray-300 py-2 pr-4 pl-10 focus:border-transparent focus:ring-2 focus:ring-blue-500 dark:border-gray-700 dark:bg-gray-800"
          />
        </div>

        <Popover>
          <div className="flex flex-wrap items-center gap-4 justify-end">
            <PopoverTrigger>
              <button
                className="flex items-center gap-2 rounded-lg bg-gray-100 px-4 py-2 transition-colors hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700"
              >
                <Filter className="h-4 w-4" />
                Filters
              </button>
            </PopoverTrigger>

            {/* <DropdownMenu>
                <DropdownMenuTrigger 
                value={`${sort.field}-${sort.order}`} 
                onChange={e => {
                  const [field, order] = e.target.value.split('-') as [typeof sort.field, typeof sort.order];
                  setSort({ field, order });
                }}
                >
                  Sorting
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem value="startTime-asc">Start Time (Earliest First)</DropdownMenuItem>
                  <DropdownMenuItem value="startTime-desc">Start Time (Latest First)</DropdownMenuItem>
                  <DropdownMenuItem value="createdAt-desc">Newest First</DropdownMenuItem>
                  <DropdownMenuItem value="createdAt-asc">Oldest First</DropdownMenuItem>
                  <DropdownMenuItem value="title-asc">Title (A-Z)</DropdownMenuItem>
                  <DropdownMenuItem value="title-desc">Title (Z-A)</DropdownMenuItem>
                  <DropdownMenuItem value="registeredUsers-desc">Most Popular</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu> */}

            <select
              value={`${sort.field}-${sort.order}`}
              onChange={e => {
                const [field, order] = e.target.value.split('-') as [
                  typeof sort.field,
                  typeof sort.order,
                ];
                setSort({ field, order });
              }}
              className="rounded-lg border border-gray-300 px-4 py-2 focus:ring-2 focus:ring-blue-500 dark:border-gray-700 dark:bg-gray-800"
            >
              <option value="startTime-asc">Start Time (Earliest First)</option>
              <option value="startTime-desc">Start Time (Latest First)</option>
              <option value="createdAt-desc">Newest First</option>
              <option value="createdAt-asc">Oldest First</option>
              <option value="title-asc">Title (A-Z)</option>
              <option value="title-desc">Title (Z-A)</option>
              <option value="registeredUsers-desc">Most Popular</option>
            </select>

            {hasActiveFilters && (
              <button
                onClick={clearFilters}
                className="flex items-center gap-2 px-4 py-2 text-red-600 transition-colors hover:text-red-700"
              >
                <X className="h-4 w-4" />
                Clear All
              </button>
            )}
          </div>

          <PopoverContent className='w-fit'>
            <div className="grid grid-cols-1 gap-4 rounded-lg bg-gray-50 p-4 md:grid-cols-2 lg:grid-cols-4 dark:bg-gray-800">
              {/* Event Type Filter - Only for 'all' type */}
              {type === 'all' && (
                <div>
                  <label className="mb-2 block text-sm font-medium">
                    Event Type
                  </label>
                  <select
                    value={filters.eventType || ''}
                    onChange={e =>
                      handleFilterChange('eventType', e.target.value)
                    }
                    className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
                  >
                    <option value="">All Events</option>
                    <option value="upcoming">Upcoming Events</option>
                    <option value="past">Past Events</option>
                  </select>
                </div>
              )}

              <div>
                <label className="mb-2 block text-sm font-medium">
                  Category
                </label>
                <select
                  value={filters.category || ''}
                  onChange={e =>
                    handleFilterChange('category', e.target.value)
                  }
                  className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
                >
                  <option value="">All Categories</option>
                  {categoryOptions.map(cat => (
                    <option key={cat} value={cat}>
                      {cat}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="mb-2 block text-sm font-medium">
                  Level
                </label>
                <select
                  value={filters.level || ''}
                  onChange={e => handleFilterChange('level', e.target.value)}
                  className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
                >
                  <option value="">All Levels</option>
                  {levelOptions.map(level => (
                    <option key={level} value={level}>
                      {level}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="mb-2 block text-sm font-medium">Mode</label>
                <select
                  value={filters.mode || ''}
                  onChange={e => handleFilterChange('mode', e.target.value)}
                  className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
                >
                  <option value="">All Modes</option>
                  {modeOptions.map(mode => (
                    <option key={mode} value={mode}>
                      {mode}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="mb-2 block text-sm font-medium">
                  Language
                </label>
                <select
                  value={filters.language || ''}
                  onChange={e =>
                    handleFilterChange('language', e.target.value)
                  }
                  className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
                >
                  <option value="">All Languages</option>
                  {languageOptions.map(lang => (
                    <option key={lang} value={lang}>
                      {lang}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="mb-2 block text-sm font-medium">
                  Price
                </label>
                <select
                  value={filters.isPaid || ''}
                  onChange={e => handleFilterChange('isPaid', e.target.value)}
                  className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
                >
                  <option value="">All Events</option>
                  <option value="false">Free Events</option>
                  <option value="true">Paid Events</option>
                </select>
              </div>

              <div>
                <label className="mb-2 block text-sm font-medium">
                  Featured
                </label>
                <select
                  value={filters.isFeatured || ''}
                  onChange={e =>
                    handleFilterChange('isFeatured', e.target.value)
                  }
                  className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
                >
                  <option value="">All Events</option>
                  <option value="true">Featured Only</option>
                </select>
              </div>

              <div>
                <label className="mb-2 block text-sm font-medium">Tags</label>
                <input
                  type="text"
                  placeholder="Search by tags..."
                  value={filters.tags || ''}
                  onChange={e => handleFilterChange('tags', e.target.value)}
                  className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
                />
              </div>
            </div>
          </PopoverContent>
        </Popover>
      </div>

      {filteredEvents.length === 0 ? (
        <div className="flex flex-col items-center justify-center py-16 text-center">
          <Calendar className="mb-4 h-16 w-16 text-gray-400 dark:text-gray-600" />
          <h3 className="mb-2 text-xl font-semibold text-gray-600 dark:text-gray-400">
            No {getEventTypeLabel()} Events Found
          </h3>
          <p className="text-gray-500 dark:text-gray-500">
            {hasActiveFilters
              ? 'Try adjusting your filters or search terms'
              : 'Check back later for new events'}
          </p>
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
            {filteredEvents.map((event, idx) => (
              <motion.div
                key={event._id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: idx * 0.1 }}
              >
                <Link href={`/dashboard/events-and-webinars/${event.slug}`}>
                  <Card className="h-full pt-0">
                    <CardHeader className="p-0">
                      <div className="relative">
                        <img
                          src={event.bannerImage?.url || defaultEventImage}
                          alt={event.title}
                          className="mx-auto h-48 w-full rounded-t-lg object-cover"
                        />
                        {event.isFeatured && (
                          <div className="absolute top-2 left-2 flex items-center gap-1 rounded-full bg-yellow-400 px-2 py-1 text-xs font-semibold text-yellow-800">
                            <Award className="h-3 w-3" />
                            Featured
                          </div>
                        )}
                        <div className="absolute top-2 right-2">
                          {event.isPaid ? (
                            <div className="rounded-full bg-green-500 px-2 py-1 text-xs font-semibold text-white">
                              {event.currency} {event.price}
                            </div>
                          ) : (
                            <div className="rounded-full bg-blue-500 px-2 py-1 text-xs font-semibold text-white">
                              Free
                            </div>
                          )}
                        </div>
                        {(!isEventUpcoming(event) || type === 'past') && (
                          <div className="bg-opacity-50 absolute bottom-2 left-2 rounded bg-black px-2 py-1 text-xs font-medium text-white">
                            {getTimeAgo(event.endTime || event.startTime)}
                          </div>
                        )}
                      </div>
                      <div className="p-4">
                        <div className="mb-2 flex flex-wrap gap-1">
                          <span className="rounded-full bg-blue-100 px-2 py-1 text-xs font-medium text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                            {event.category}
                          </span>
                          <span className="rounded-full bg-gray-100 px-2 py-1 text-xs font-medium text-gray-800 dark:bg-gray-800 dark:text-gray-200">
                            {event.level}
                          </span>
                        </div>
                        <CardTitle className="mb-2 line-clamp-2 text-lg">
                          {event.title}
                        </CardTitle>
                        <CardDescription className="line-clamp-3 text-sm">
                          {event.description}
                        </CardDescription>
                      </div>
                    </CardHeader>

                    <CardContent className="px-4 pb-4">
                      <div className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4" />
                          <span>
                            {formatDateTime(event.startTime, event.timezone)}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Clock className="h-4 w-4" />
                          <span>
                            {event.durationMinutes
                              ? formatDuration(event.durationMinutes)
                              : 'Duration TBD'}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          {event.mode === 'Online' ? (
                            <Globe className="h-4 w-4" />
                          ) : (
                            <MapPinIcon className="h-4 w-4" />
                          )}
                          <span>
                            {event.mode === 'Online'
                              ? `${event.mode} • ${event.platform}`
                              : `${event.mode} • ${event.location}`}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Users className="h-4 w-4" />
                          <span>
                            {event.registeredUsers?.length || 0}
                            {isEventUpcoming(event)
                              ? ' registered'
                              : ' participants'}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4" />
                          <span>By {getHostName(event)}</span>
                        </div>
                      </div>
                      {(event.certificateOffered ||
                        event.recordingAccess ||
                        event.resourcesProvided) && (
                        <div className="mt-3 flex flex-wrap gap-1">
                          {event.certificateOffered && (
                            <div className="flex items-center gap-1 rounded-full bg-green-100 px-2 py-1 text-xs text-green-800 dark:bg-green-900 dark:text-green-200">
                              <Award className="h-3 w-3" />
                              Certificate
                            </div>
                          )}
                          {event.recordingAccess && (
                            <div className="flex items-center gap-1 rounded-full bg-purple-100 px-2 py-1 text-xs text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                              <Video className="h-3 w-3" />
                              Recording
                            </div>
                          )}
                          {event.resourcesProvided && (
                            <div className="flex items-center gap-1 rounded-full bg-blue-100 px-2 py-1 text-xs text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                              <FileText className="h-3 w-3" />
                              Resources
                            </div>
                          )}
                        </div>
                      )}
                      {event.tags && event.tags.length > 0 && (
                        <div className="mt-3 flex flex-wrap gap-1">
                          {event.tags.slice(0, 3).map((tag, tagIdx) => (
                            <span
                              key={tagIdx}
                              className="inline-flex items-center gap-1 rounded-full bg-gray-100 px-2 py-1 text-xs text-gray-600 dark:bg-gray-800 dark:text-gray-400"
                            >
                              <Tag className="h-2 w-2" />
                              {tag}
                            </span>
                          ))}
                          {event.tags.length > 3 && (
                            <span className="text-xs text-gray-500">
                              +{event.tags.length - 3} more
                            </span>
                          )}
                        </div>
                      )}
                    </CardContent>
                    <CardFooter className="px-4 pt-0">
                      {isEventUpcoming(event) && type === 'all' ? (
                        <div className="w-full space-y-2">
                          {event.registrationDeadline && (
                            <div className="text-xs text-gray-500">
                              Registration closes:{' '}
                              {formatDateTime(event.registrationDeadline)}
                            </div>
                          )}
                          <Button className="w-full">Register Now</Button>
                        </div>
                      ) : (
                        <div className="flex w-full flex-wrap gap-2">
                          {event.recordingAccess && (
                            <Button
                              variant="outline"
                              size="sm"
                              className="flex items-center gap-1"
                            >
                              <Play className="h-3 w-3" />
                              Watch
                            </Button>
                          )}
                          {event.resourcesProvided && (
                            <Button
                              variant="outline"
                              size="sm"
                              className="flex items-center gap-1"
                            >
                              <Download className="h-3 w-3" />
                              Resources
                            </Button>
                          )}
                          {event.certificateOffered && (
                            <Button
                              variant="outline"
                              size="sm"
                              className="flex items-center gap-1"
                            >
                              <Award className="h-3 w-3" />
                              Certificate
                            </Button>
                          )}
                        </div>
                      )}
                    </CardFooter>
                  </Card>
                </Link>
              </motion.div>
            ))}
          </div>

          {/* Pagination */}
          {data?.data?.meta && (
            <div className="mt-8 flex items-center justify-center gap-2">
              <Button
                variant="outline"
                onClick={() => setPage(prev => Math.max(1, prev - 1))}
                disabled={!data.data.meta.hasPreviousPage}
              >
                Previous
              </Button>

              <span className="px-4 py-2 text-sm text-gray-600">
                Page {data.data.meta.page} of {data.data.meta.totalPages}
              </span>
              <Button
                variant="outline"
                onClick={() => setPage(prev => prev + 1)}
                disabled={!data.data.meta.hasNextPage}
              >
                Next
              </Button>
            </div>
          )}
        </>
      )}
    </div>
  );
}
