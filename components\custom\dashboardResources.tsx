'use client';
import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import {
  BookOpen,
  Clock,
  Users,
  Tag,
  Award,
  Filter,
  Search,
  Globe,
  User,
  DollarSign,
  Video,
  FileText,
  X,
  ChevronDown,
  Play,
  Download,
  ExternalLink,
  Star,
  Shield,
  Bookmark,
  Eye,
  Github,
  MonitorPlay,
  GraduationCap,
  Heart,
  Calendar,
  TrendingUp,
} from 'lucide-react';
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { IVideoResource, IArticleResource, ICourseResource, IDocumentResource } from '@/types/IResources';
import {
  useGetAllArticlesQuery,
  useGetAllCoursesQuery,
  useGetAllDocumentsQuery,
  useGetAllVideosQuery,
} from '@/store/features/resources/resourceApi';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';

type IResource = IVideoResource | IDocumentResource | ICourseResource | IArticleResource;

type ResourceType = 'video' | 'document' | 'course' | 'article' | 'all';

type FilterState = {
  category?: string;
  subcategory?: string;
  level?: string;
  platform?: string;
  type?: string;
  isPaid?: string;
  isFeatured?: string;
  isVerified?: string;
  importanceLevel?: string;
  tags?: string;
  resourceType?: ResourceType;
};

type SortState = {
  field: 'createdAt' | 'updatedAt' | 'title' | 'recommendedBy';
  order: 'asc' | 'desc';
};

interface ResourcesComponentProps {
  type: ResourceType;
  initialFilters?: FilterState;
}

const categoryOptions = [
    'Web Development',
    'Data Science',
    'Mobile Development',
    'AI/ML',
    'DevOps',
    'Design',
    'Marketing',
];
const levelOptions = ['Beginner', 'Intermediate', 'Advanced'];

const getResourceIcon = (resource: IResource) => {
  if ('videoUrl' in resource) return <Video className="h-4 w-4" />;
  if ('fileUrl' in resource) return <FileText className="h-4 w-4" />;
  if ('courseUrl' in resource) return <GraduationCap className="h-4 w-4" />;
  if ('type' in resource && resource.type === 'GitHub Repository') return <Github className="h-4 w-4" />;
  return <BookOpen className="h-4 w-4" />;
};

const getResourceUrl = (resource: IResource) => {
  if ('videoUrl' in resource) return resource.videoUrl;
  if ('fileUrl' in resource) return resource.fileUrl;
  if ('courseUrl' in resource) return resource.courseUrl;
  if ('url' in resource) return resource.url;
  return '#';
};

const getResourcePlatform = (resource: IResource) => {
  if ('platform' in resource) return resource.platform;
  return 'Unknown';
};

const getResourceTypeLabel = (resource: IResource) => {
  if ('videoUrl' in resource) return 'Video';
  if ('fileUrl' in resource) return 'Document';
  if ('courseUrl' in resource) return 'Course';
  if ('type' in resource) return resource.type;
  return 'Resource';
};

function useDebounce<T>(value: T, delay: number) {
  const [debounced, setDebounced] = useState(value);
  useEffect(() => {
    const handler = setTimeout(() => setDebounced(value), delay);
    return () => clearTimeout(handler);
  }, [value, delay]);
  return debounced;
}

const defaultResourceImage = '/images/certifications.jpg';

export default function ResourcesComponent({ type, initialFilters = {} }: ResourcesComponentProps) {
  const [filteredResources, setFilteredResources] = useState<(IVideoResource | IDocumentResource | ICourseResource | IArticleResource)[]>([]);
  const [filters, setFilters] = useState<FilterState>(initialFilters);
  const [sort, setSort] = useState<SortState>({
    field: 'createdAt',
    order: 'desc',
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [page, setPage] = useState(1);
  const [limit] = useState(12);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const debouncedSearch = useDebounce(searchTerm, 1000);

  const queryParams = {
    sortBy: sort.field,
    sortOrder: sort.order,
    ...(debouncedSearch && { title: debouncedSearch }),
    ...(filters.category && { category: filters.category }),
    ...(filters.level && { level: filters.level }),
    ...(filters.isFeatured && { isFeatured: filters.isFeatured }),
    ...(filters.isVerified && { isVerified: filters.isVerified }),
    ...(filters.importanceLevel && { importanceLevel: filters.importanceLevel }),
    ...(filters.tags && { tags: filters.tags }),
    page: page.toString(),
    limit: limit.toString(),
  };

  const {
    data,
    refetch,
  } =
    type === 'video'
      ? useGetAllVideosQuery(queryParams)
      : type === 'document'
      ? useGetAllDocumentsQuery(queryParams)
      : type === 'course'
      ? useGetAllCoursesQuery(queryParams)
      : type === 'article'
      ? useGetAllArticlesQuery(queryParams)
      : { data: undefined, refetch: () => {} };

  useEffect(() => {
    if (type !== 'all') {
      setFilters(prev => ({ ...prev, resourceType: type }));
    }
  }, [type]);

  useEffect(() => {
    setLoading(true);
      setTimeout(() => {
          console.log(data);
          if (data?.data.resources) {
          setFilteredResources(data.data.resources);
          }
          setLoading(false);
      }, 500);
  }, [data]);

  useEffect(() => {
    refetch();
  }, [filters, sort, debouncedSearch, page]);

  const handleFilterChange = (key: keyof FilterState, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value || undefined,
    }));
    setPage(1);
  };

  const clearFilters = () => {
    setFilters({});
    setSearchTerm('');
    setPage(1);
  };

  const getTimeAgo = (dateString: string | Date) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} min ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
    if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} days ago`;
    if (diffInSeconds < 31536000) return `${Math.floor(diffInSeconds / 2592000)} months ago`;
    return `${Math.floor(diffInSeconds / 31536000)} years ago`;
  };

  const getResourceTypeTitle = () => {
    switch (type) {
      case 'video':
        return 'Video';
      case 'document':
        return 'Document';
      case 'course':
        return 'Course';
      case 'article':
        return 'Article';
      case 'all':
        return filters.resourceType ? 
          (filters.resourceType.charAt(0).toUpperCase() + filters.resourceType.slice(1)) : 
          'All';
      default:
        return 'All';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-16">
        <div className="h-12 w-12 animate-spin rounded-full border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center py-16 text-center">
        <p className="text-red-600">Error loading resources. Please try again later.</p>
        <Button onClick={() => window.location.reload()} className="mt-4">
          Retry
        </Button>
      </div>
    );
  }

  const hasActiveFilters = Object.keys(filters).length > 1 || searchTerm;

  return (
    <div className="space-y-6">
      <div className="mb-6 space-y-4">
        <div className="relative">
          <Search className="absolute top-3 left-3 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder={`Search ${getResourceTypeTitle().toLowerCase()} resources...`}
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
            className="w-full rounded-lg border border-gray-300 py-2 pr-4 pl-10 focus:border-transparent focus:ring-2 focus:ring-blue-500 dark:border-gray-700 dark:bg-gray-800"
          />
        </div>

        <Popover>
          <div className="flex flex-wrap items-center gap-4 justify-end">
            <PopoverTrigger>
              <button
                className="flex items-center gap-2 rounded-lg bg-gray-100 px-4 py-2 transition-colors hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700"
              >
                <Filter className="h-4 w-4" />
                Filters
              </button>
            </PopoverTrigger>
            <select
              value={`${sort.field}-${sort.order}`}
              onChange={e => {
                const [field, order] = e.target.value.split('-') as [typeof sort.field, typeof sort.order];
                setSort({ field, order });
              }}
              className="rounded-lg border border-gray-300 px-4 py-2 focus:ring-2 focus:ring-blue-500 dark:border-gray-700 dark:bg-gray-800"
            >
              <option value="createdAt-desc">Newest First</option>
              <option value="createdAt-asc">Oldest First</option>
              <option value="updatedAt-desc">Recently Updated</option>
              <option value="title-asc">Title (A-Z)</option>
              <option value="title-desc">Title (Z-A)</option>
              <option value="recommendedBy-desc">Most Recommended</option>
            </select>
            {hasActiveFilters && (
              <button
                onClick={clearFilters}
                className="flex items-center gap-2 px-4 py-2 text-red-600 transition-colors hover:text-red-700"
              >
                <X className="h-4 w-4" />
                Clear All
              </button>
            )}
          </div>
          <PopoverContent className="w-fit">
            <div className="grid grid-cols-1 gap-4 rounded-lg bg-gray-50 p-4 md:grid-cols-2 lg:grid-cols-4 dark:bg-gray-800">
              {/* Resource Type Filter - Only for 'all' type */}
              {type === 'all' && (
                <div>
                  <label className="mb-2 block text-sm font-medium">Resource Type</label>
                  <select
                    value={filters.resourceType || ''}
                    onChange={e => handleFilterChange('resourceType', e.target.value)}
                    className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
                  >
                    <option value="">All Types</option>
                    <option value="video">Videos</option>
                    <option value="document">Documents</option>
                    <option value="course">Courses</option>
                    <option value="article">Articles</option>
                  </select>
                </div>
              )}
              <div>
                <label className="mb-2 block text-sm font-medium">Category</label>
                <select
                  value={filters.category || ''}
                  onChange={e => handleFilterChange('category', e.target.value)}
                  className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
                >
                  <option value="">All Categories</option>
                  {categoryOptions.map(cat => (
                    <option key={cat} value={cat}>{cat}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="mb-2 block text-sm font-medium">Level</label>
                <select
                  value={filters.level || ''}
                  onChange={e => handleFilterChange('level', e.target.value)}
                  className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
                >
                  <option value="">All Levels</option>
                  {levelOptions.map(level => (
                    <option key={level} value={level}>{level}</option>
                  ))}
                </select>
              </div>
              {/* <div>
                <label className="mb-2 block text-sm font-medium">Importance</label>
                <select
                  value={filters.importanceLevel || ''}
                  onChange={e => handleFilterChange('importanceLevel', e.target.value)}
                  className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
                >
                  <option value="">All Importance</option>
                  {importanceLevelOptions.map(level => (
                    <option key={level} value={level}>{level}</option>
                  ))}
                </select>
              </div> */}
              <div>
                <label className="mb-2 block text-sm font-medium">Featured</label>
                <select
                  value={filters.isFeatured || ''}
                  onChange={e => handleFilterChange('isFeatured', e.target.value)}
                  className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
                >
                  <option value="">All Resources</option>
                  <option value="true">Featured Only</option>
                </select>
              </div>
              <div>
                <label className="mb-2 block text-sm font-medium">Verified</label>
                <select
                  value={filters.isVerified || ''}
                  onChange={e => handleFilterChange('isVerified', e.target.value)}
                  className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
                >
                  <option value="">All Resources</option>
                  <option value="true">Verified Only</option>
                </select>
              </div>
              <div>
                <label className="mb-2 block text-sm font-medium">Tags</label>
                <input
                  type="text"
                  placeholder="Search by tags..."
                  value={filters.tags || ''}
                  onChange={e => handleFilterChange('tags', e.target.value)}
                  className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
                />
              </div>
            </div>
          </PopoverContent>
        </Popover>
      </div>

      {filteredResources.length === 0 ? (
        <div className="flex flex-col items-center justify-center py-16 text-center">
          <BookOpen className="mb-4 h-16 w-16 text-gray-400 dark:text-gray-600" />
          <h3 className="mb-2 text-xl font-semibold text-gray-600 dark:text-gray-400">
            No {getResourceTypeTitle()} Resources Found
          </h3>
          <p className="text-gray-500 dark:text-gray-500">
            {hasActiveFilters
              ? 'Try adjusting your filters or search terms'
              : 'Check back later for new resources'
            }
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
          {filteredResources.map((resource, idx) => (
            <motion.div
              key={resource._id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: idx * 0.1 }}
            >
              <Card className="h-full hover:shadow-lg transition-shadow pt-0">
                <CardHeader className="p-0">
                  <div className="relative">
                    <img
                      src={resource.thumbnailUrl || defaultResourceImage}
                      alt={resource.title}
                      className="h-48 w-full object-cover rounded-t-lg"
                    />
                    {resource.isFeatured && (
                      <div className="absolute top-2 left-2 flex items-center gap-1 rounded-full bg-yellow-400 px-2 py-1 text-xs font-semibold text-yellow-800">
                        <Star className="h-3 w-3" />
                        Featured
                      </div>
                    )}
                    <div className="absolute top-2 right-2 flex gap-1">
                      {resource.isVerified && (
                        <div className="rounded-full bg-green-500 px-2 py-1 text-xs font-semibold text-white flex items-center gap-1">
                          <Shield className="h-3 w-3" />
                          Verified
                        </div>
                      )}
                      <div className="rounded-full bg-blue-600 px-2 py-1 text-xs font-semibold text-white flex items-center gap-1">
                        {getResourceIcon(resource)}
                        {getResourceTypeLabel(resource)}
                      </div>
                    </div>
                  </div>
                </CardHeader>

                <CardContent className="p-4 space-y-2">
                  <CardTitle className="text-lg font-semibold line-clamp-2">{resource.title}</CardTitle>
                  <CardDescription className="line-clamp-3 text-sm text-gray-600 dark:text-gray-400">
                    {resource.description}
                  </CardDescription>
                  <div className="flex flex-wrap gap-2 text-xs text-gray-500 dark:text-gray-400">
                    <div className="flex items-center gap-1">
                      <Tag className="h-3 w-3" />
                      {resource.tags?.slice(0, 2).join(', ') || 'No Tags'}
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      {getTimeAgo(resource.createdAt)}
                    </div>
                    <div className="flex items-center gap-1">
                      <Award className="h-3 w-3" />
                      {resource.level}
                    </div>
                    {resource.importanceLevel && (
                      <div className="flex items-center gap-1">
                        <Bookmark className="h-3 w-3" />
                        {resource.importanceLevel}
                      </div>
                    )}
                  </div>
                </CardContent>

                <CardFooter className="flex justify-between p-4 border-t dark:border-gray-700">
                  <Link
                    href={getResourceUrl(resource)}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-2 text-sm font-medium text-blue-600 hover:underline"
                  >
                    View <ExternalLink className="h-4 w-4" />
                  </Link>

                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {getResourcePlatform(resource)}
                  </div>
                </CardFooter>
              </Card>
            </motion.div>
          ))}
        </div>
      )}
    </div>
  );
}