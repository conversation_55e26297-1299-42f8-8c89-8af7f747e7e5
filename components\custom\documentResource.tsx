import { useState, useEffect } from 'react';
import axios from 'axios';
import {
  BookOpen,
  Download,
  Search,
  Filter,
  ChevronDown,
  X,
  Star,
  Eye,
  FileText,
  Award,
  Tag,
  User,
  Calendar,
  SortAsc,
  SortDesc,
  Users,
} from 'lucide-react';

type DocumentResourceType = {
  _id: string;
  title: string;
  description?: string;
  fileUrl: string;
  type: 'PDF' | 'eBook' | 'Handwritten Note';
  category: string;
  subcategory: string;
  tags?: string[];
  level: 'Beginner' | 'Intermediate' | 'Advanced';
  benefits?: string[];
  recommendedBy: Array<{
    _id: string;
    firstName: string;
    lastName: string;
    email: string;
  }>;
  isVerified?: boolean;
  isFeatured?: boolean;
  importanceLevel?: 'Core' | 'Reference' | 'Bonus';
  moderationStatus: 'pending' | 'approved' | 'rejected';
  visible: boolean;
  downloadCount: number;
  thumbnailUrl?: string;
  createdBy?: {
    _id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  updatedBy?: {
    _id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  createdAt: string;
  updatedAt: string;
};

type FilterState = {
  search?: string;
  category?: string;
  subcategory?: string;
  type?: string;
  level?: string;
  importanceLevel?: string;
  isVerified?: string;
  tags?: string;
};

type SortState = {
  field: 'createdAt' | 'title' | 'downloadCount' | 'updatedAt';
  order: 'asc' | 'desc';
};

function useDebounce<T>(value: T, delay: number) {
  const [debounced, setDebounced] = useState(value);
  useEffect(() => {
    const handler = setTimeout(() => setDebounced(value), delay);
    return () => clearTimeout(handler);
  }, [value, delay]);
  return debounced;
}

const defaultThumbnail = '/images/annie-spratt-QckxruozjRg-unsplash.jpg';

interface DocumentResourcesProps {
  showFeaturedOnly?: boolean;
  showSearch?: boolean;
  showFilters?: boolean;
  initialLimit?: number;
}

export default function DocumentResources({
  showFeaturedOnly = false,
  showSearch = true,
  showFilters = true,
  initialLimit = 10,
}: DocumentResourcesProps) {
  const [documents, setDocuments] = useState<DocumentResourceType[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showFiltersPanel, setShowFiltersPanel] = useState(false);
  const [filters, setFilters] = useState<FilterState>({});
  const [sort, setSort] = useState<SortState>({
    field: 'createdAt',
    order: 'desc',
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(initialLimit);
  const [meta, setMeta] = useState<{
    total: number;
    totalPages: number;
    page: number;
    limit: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  } | null>(null);

  const categoryOptions = [
    'Web Development',
    'Data Science',
    'Mobile Development',
    'AI/ML',
    'DevOps',
    'Design',
    'Programming',
    'Business',
    'Mathematics',
    'Science',
  ];

  const typeOptions = ['PDF', 'eBook', 'Handwritten Note'];
  const levelOptions = ['Beginner', 'Intermediate', 'Advanced'];
  const importanceLevelOptions = ['Core', 'Reference', 'Bonus'];

  const fetchDocuments = async () => {
    try {
      setLoading(true);
      const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
      if (!baseUrl) {
        throw new Error('NEXT_PUBLIC_BASE_URL environment variable is not set');
      }

      const params = new URLSearchParams();
      if (searchTerm) params.append('search', searchTerm);

      // Add featured filter if showFeaturedOnly is true
      if (showFeaturedOnly) {
        params.append('isFeatured', 'true');
      }

      // Add other filters
      Object.entries(filters).forEach(([key, value]) => {
        if (value) params.append(key, value);
      });

      params.append('sortBy', sort.field);
      params.append('sortOrder', sort.order);
      params.append('moderationStatus', 'approved');
      params.append('visible', 'true');
      params.append('page', String(page));
      params.append('limit', String(limit));

      const url = new URL('/api/v1/documents', baseUrl);
      url.search = params.toString();

      console.log(url.toString());

      const response = await axios.get(url.toString());
      if (!response.data.success) {
        throw new Error(response.data.error || 'Failed to fetch documents');
      }

      console.log(response.data.data);

      setDocuments(response.data.data.documents);
      setMeta(response.data.data.meta);
      setError(null);
    } catch (err: any) {
      setDocuments([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDocuments();
  }, [
    filters,
    sort,
    useDebounce(searchTerm, 1000),
    page,
    limit,
    showFeaturedOnly,
  ]);

  const handleFilterChange = (key: keyof FilterState, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value || undefined,
    }));
    setPage(1);
  };

  const clearFilters = () => {
    setFilters({});
    setSearchTerm('');
    setPage(1);
  };

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'Beginner':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'Intermediate':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'Advanced':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200';
    }
  };

  const getImportanceColor = (importance: string) => {
    switch (importance) {
      case 'Core':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      case 'Reference':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'Bonus':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'PDF':
        return <FileText className="h-4 w-4" />;
      case 'eBook':
        return <BookOpen className="h-4 w-4" />;
      case 'Handwritten Note':
        return <FileText className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-16">
        <div className="h-12 w-12 animate-spin rounded-full border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        {meta && (
          <span className="text-sm text-gray-500 dark:text-gray-400">
            {meta.total} document{meta.total !== 1 ? 's' : ''} found
          </span>
        )}
      </div>

      {(showSearch || showFilters) && (
        <div className="space-y-4">
          {showSearch && (
            <div className="relative">
              <Search className="absolute top-3 left-3 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search documents..."
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
                className="w-full rounded-lg border border-gray-300 py-2 pr-4 pl-10 focus:border-transparent focus:ring-2 focus:ring-blue-500 dark:border-gray-700 dark:bg-gray-800"
              />
            </div>
          )}

          {showFilters && (
            <div className="flex flex-wrap items-center gap-4">
              <button
                onClick={() => setShowFiltersPanel(!showFiltersPanel)}
                className="flex items-center gap-2 rounded-lg bg-gray-100 px-4 py-2 transition-colors hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700"
              >
                <Filter className="h-4 w-4" />
                Filters
                <ChevronDown
                  className={`h-4 w-4 transition-transform ${showFiltersPanel ? 'rotate-180' : ''}`}
                />
              </button>

              <select
                value={`${sort.field}-${sort.order}`}
                onChange={e => {
                  const [field, order] = e.target.value.split('-') as [
                    typeof sort.field,
                    typeof sort.order,
                  ];
                  setSort({ field, order });
                }}
                className="rounded-lg border border-gray-300 px-4 py-2 focus:ring-2 focus:ring-blue-500 dark:border-gray-700 dark:bg-gray-800"
              >
                <option value="createdAt-desc">Newest First</option>
                <option value="createdAt-asc">Oldest First</option>
                <option value="title-asc">Title (A-Z)</option>
                <option value="title-desc">Title (Z-A)</option>
                <option value="downloadCount-desc">Most Downloaded</option>
                <option value="downloadCount-asc">Least Downloaded</option>
                <option value="updatedAt-desc">Recently Updated</option>
              </select>

              {(Object.keys(filters).length > 0 || searchTerm) && (
                <button
                  onClick={clearFilters}
                  className="flex items-center gap-2 px-4 py-2 text-red-600 transition-colors hover:text-red-700"
                >
                  <X className="h-4 w-4" />
                  Clear All
                </button>
              )}
            </div>
          )}

          {showFiltersPanel && (
            <div className="grid grid-cols-1 gap-4 rounded-lg bg-gray-50 p-4 md:grid-cols-2 lg:grid-cols-4 dark:bg-gray-800">
              <div>
                <label className="mb-2 block text-sm font-medium">
                  Category
                </label>
                <select
                  value={filters.category || ''}
                  onChange={e => handleFilterChange('category', e.target.value)}
                  className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
                >
                  <option value="">All Categories</option>
                  {categoryOptions.map(cat => (
                    <option key={cat} value={cat}>
                      {cat}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="mb-2 block text-sm font-medium">Type</label>
                <select
                  value={filters.type || ''}
                  onChange={e => handleFilterChange('type', e.target.value)}
                  className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
                >
                  <option value="">All Types</option>
                  {typeOptions.map(type => (
                    <option key={type} value={type}>
                      {type}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="mb-2 block text-sm font-medium">Level</label>
                <select
                  value={filters.level || ''}
                  onChange={e => handleFilterChange('level', e.target.value)}
                  className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
                >
                  <option value="">All Levels</option>
                  {levelOptions.map(level => (
                    <option key={level} value={level}>
                      {level}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="mb-2 block text-sm font-medium">
                  Importance
                </label>
                <select
                  value={filters.importanceLevel || ''}
                  onChange={e =>
                    handleFilterChange('importanceLevel', e.target.value)
                  }
                  className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
                >
                  <option value="">All Importance</option>
                  {importanceLevelOptions.map(level => (
                    <option key={level} value={level}>
                      {level}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="mb-2 block text-sm font-medium">
                  Verified
                </label>
                <select
                  value={filters.isVerified || ''}
                  onChange={e =>
                    handleFilterChange('isVerified', e.target.value)
                  }
                  className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
                >
                  <option value="">All Documents</option>
                  <option value="true">Verified Only</option>
                  <option value="false">Not Verified</option>
                </select>
              </div>

              <div>
                <label className="mb-2 block text-sm font-medium">Tags</label>
                <input
                  type="text"
                  placeholder="Enter tags (comma-separated)"
                  value={filters.tags || ''}
                  onChange={e => handleFilterChange('tags', e.target.value)}
                  className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
                />
              </div>
            </div>
          )}
        </div>
      )}

      {documents.length === 0 ? (
        <div className="flex flex-col items-center justify-center py-16 text-center">
          <BookOpen className="mb-4 h-16 w-16 text-gray-400 dark:text-gray-600" />
          <h3 className="mb-2 text-xl font-semibold text-gray-600 dark:text-gray-400">
            No Documents Found
          </h3>
          <p className="text-gray-500 dark:text-gray-500">
            Try adjusting your filters or search terms
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
          {documents.map((doc, idx) => (
            <div
              key={doc._id}
              className="overflow-hidden rounded-xl bg-white shadow-lg transition-shadow duration-300 hover:shadow-xl dark:bg-gray-900"
            >
              <div className="relative">
                <img
                  src={doc.thumbnailUrl === null ? defaultThumbnail : doc.thumbnailUrl}
                  alt={doc.title}
                  className="h-48 w-full object-cover"
                />
                {doc.isFeatured && (
                  <div className="absolute top-3 left-3 flex items-center gap-1 rounded-full bg-yellow-400 px-2 py-1 text-xs font-semibold text-yellow-800">
                    <Star className="h-3 w-3" />
                    Featured
                  </div>
                )}
                {doc.isVerified && (
                  <div className="absolute top-3 right-3 flex items-center gap-1 rounded-full bg-green-400 px-2 py-1 text-xs font-semibold text-green-800">
                    <Award className="h-3 w-3" />
                    Verified
                  </div>
                )}
              </div>

              <div className="p-4">
                <div className="mb-3 flex flex-wrap items-center gap-2">
                  <div className="flex items-center gap-1 rounded-full bg-blue-100 px-2 py-1 text-xs font-medium text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                    {getTypeIcon(doc.type)}
                    {doc.type}
                  </div>
                  <span
                    className={`rounded-full px-2 py-1 text-xs font-medium ${getLevelColor(doc.level)}`}
                  >
                    {doc.level}
                  </span>
                  {doc.importanceLevel && (
                    <span
                      className={`rounded-full px-2 py-1 text-xs font-medium ${getImportanceColor(doc.importanceLevel)}`}
                    >
                      {doc.importanceLevel}
                    </span>
                  )}
                </div>

                <h3 className="mb-2 line-clamp-2 text-lg font-bold text-gray-900 dark:text-white">
                  {doc.title}
                </h3>

                {doc.description && (
                  <p className="mb-3 line-clamp-3 text-sm text-gray-600 dark:text-gray-400">
                    {doc.description}
                  </p>
                )}

                <div className="mb-3 text-xs text-gray-500 dark:text-gray-400">
                  <div className="flex items-center justify-between">
                    <span>{doc.category}</span>
                    <span>{doc.subcategory}</span>
                  </div>
                </div>

                <div className="mb-3 flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
                  <div className="flex items-center gap-1">
                    <Download className="h-4 w-4" />
                    <span>{doc.downloadCount} downloads</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Users className="h-4 w-4" />
                    <span>{doc.recommendedBy?.length || 0} recommends</span>
                  </div>
                </div>

                {doc.tags && doc.tags.length > 0 && (
                  <div className="mb-3 flex flex-wrap gap-1">
                    {doc.tags.slice(0, 3).map((tag, tagIdx) => (
                      <span
                        key={tagIdx}
                        className="inline-flex items-center gap-1 rounded-full bg-gray-100 px-2 py-1 text-xs text-gray-600 dark:bg-gray-800 dark:text-gray-400"
                      >
                        <Tag className="h-2 w-2" />
                        {tag}
                      </span>
                    ))}
                    {doc.tags.length > 3 && (
                      <span className="text-xs text-gray-500">
                        +{doc.tags.length - 3} more
                      </span>
                    )}
                  </div>
                )}

                {doc.benefits && doc.benefits.length > 0 && (
                  <div className="mb-3">
                    <h4 className="mb-1 text-xs font-semibold text-gray-700 dark:text-gray-300">
                      Benefits:
                    </h4>
                    <ul className="space-y-1 text-xs text-gray-600 dark:text-gray-400">
                      {doc.benefits.slice(0, 2).map((benefit, benefitIdx) => (
                        <li key={benefitIdx} className="flex items-start gap-1">
                          <span className="mt-0.5 text-green-500">•</span>
                          <span className="line-clamp-1">{benefit}</span>
                        </li>
                      ))}
                      {doc.benefits.length > 2 && (
                        <li className="text-gray-500">
                          +{doc.benefits.length - 2} more benefits
                        </li>
                      )}
                    </ul>
                  </div>
                )}

                <div className="flex items-center justify-between">
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {doc.createdBy && (
                      <div className="flex items-center gap-1">
                        <User className="h-3 w-3" />
                        <span>
                          By {doc.createdBy.firstName} {doc.createdBy.lastName}
                        </span>
                      </div>
                    )}
                    <div className="mt-1 flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      <span>
                        {new Date(doc.createdAt).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                  <button className="flex items-center gap-2 rounded-lg bg-blue-600 px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-blue-700">
                    <Download className="h-4 w-4" />
                    Download
                    {/* TODO: Implement download functionality */}
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {meta && meta.totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-700 dark:text-gray-300">
              Showing {(meta.page - 1) * meta.limit + 1} to{' '}
              {Math.min(meta.page * meta.limit, meta.total)} of {meta.total}{' '}
              results
            </span>
          </div>
          <div className="flex items-center gap-4">
            <button
              onClick={() => setPage(p => Math.max(1, p - 1))}
              disabled={page === 1}
              className="flex items-center gap-2 rounded-lg bg-gray-200 px-4 py-2 text-gray-700 disabled:opacity-50 dark:bg-gray-700 dark:text-gray-200"
            >
              <SortAsc className="h-4 w-4" />
              Previous
            </button>
            <span className="text-sm text-gray-700 dark:text-gray-300">
              Page {meta.page} of {meta.totalPages}
            </span>
            <button
              onClick={() => setPage(p => Math.min(meta.totalPages, p + 1))}
              disabled={page === meta.totalPages}
              className="flex items-center gap-2 rounded-lg bg-gray-200 px-4 py-2 text-gray-700 disabled:opacity-50 dark:bg-gray-700 dark:text-gray-200"
            >
              Next
              <SortDesc className="h-4 w-4" />
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
