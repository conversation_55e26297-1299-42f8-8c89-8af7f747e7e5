'use client';

import {
  BriefcaseBusiness,
  Building2,
  CalendarCheck,
  GraduationCap,
  Home,
  Menu,
  X,
} from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import React, { useState, useRef, useEffect } from 'react';
import { usePathname } from 'next/navigation';
import { ModeToggle } from '../ui/mode-toggle';
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from '../ui/hover-card';
import {
  eventSubMenu,
  freelanceCategories,
  internshipCategories,
  jobCategories,
  skillCertifications,
} from '@/constant/navItems';
import { useAppSelector } from '@/store';

type Item = {
  label: string;
  href: string;
};

interface ItemLinksProps {
  items: Item[];
  className?: string; // optional override for list styling
}

const ItemsLink = ({ items, className }: ItemLinksProps) => {
  const pathname = usePathname();

  return (
    <ul className={cn('font-secondary space-y-1 text-xs', className)}>
      {items.map(({ label, href }) => {
        const isActive = pathname === href;

        return (
          <li key={href}>
            <Link
              href={href}
              className={cn(
                'font-primary flex items-center space-x-1 py-1 transition hover:text-orange-600',
                isActive
                  ? 'font-medium text-orange-600'
                  : 'text-gray-900 dark:text-gray-50 dark:hover:text-orange-600'
              )}
            >
              <span>{label}</span>
            </Link>
          </li>
        );
      })}
    </ul>
  );
};

const NavBar: React.FC = () => {
  const pathname = usePathname();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  const { isLogin } = useAppSelector(state => state.auth);
  const navItems = [
    {
      name: 'Discover',
      link: '/',
      icon: <Home className="h-4 w-4" />,
    },
    {
      name: 'Companies',
      link: '/companies',
      icon: <Building2 className="h-4 w-4" />,
    },
    {
      name: 'Opportunities',
      link: '/opportunities',
      icon: <BriefcaseBusiness className="h-4 w-4" />,
    },
    {
      name: 'Skill Certifications',
      link: '/professional-certifications',
      icon: <GraduationCap className="h-4 w-4" />,
    },

    {
      name: 'Event & Webinars',
      link: '/event-and-webinars',
      icon: <CalendarCheck className="h-4 w-4" />,
    },
  ];

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsMenuOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <header className="bg-background text-foreground sticky inset-x-0 top-0 z-50 border-b shadow-md dark:bg-black dark:text-neutral-100">
      <div className="mx-auto flex max-w-7xl items-center justify-between px-4 py-4 md:px-6 lg:px-8">
        {/* Logo */}
        <Link
          href="/"
          aria-label="Home"
          className="flex items-center space-x-2"
        >
          <Image
            src="/logo/white-logo.png"
            alt="Sudha Software Solution Logo"
            width={300}
            height={100}
            className="h-8 w-auto dark:hidden"
          />
          <Image
            src="/logo/dark-logo.png"
            alt="Sudha Software Solution Logo"
            width={300}
            height={100}
            className="hidden h-8 w-auto dark:block"
          />
        </Link>

        {/* Desktop Nav */}
        <nav className="hidden space-x-5 text-xs lg:flex">
          <Link
            href="/"
            className={cn(
              'font-secondary flex items-center space-x-1 transition hover:text-orange-600',
              pathname === '/'
                ? 'font-medium text-indigo-600 dark:text-orange-600'
                : 'text-gray-900 dark:text-gray-50 dark:hover:text-orange-600'
            )}
          >
            {' '}
            <Home className="h-4 w-4" />
            <span>Discover</span>
          </Link>

          <Link
            href="/companies"
            className={cn(
              'font-secondary flex items-center space-x-1 transition hover:text-orange-600',
              pathname === '/companies'
                ? 'font-medium text-indigo-600 dark:text-orange-600'
                : 'text-gray-900 dark:text-gray-50 dark:hover:text-orange-600'
            )}
          >
            <Building2 className="h-4 w-4" />
            <span>Companies</span>
          </Link>

          <HoverCard>
            <HoverCardTrigger asChild>
              <Link
                href="/opportunities"
                className={cn(
                  'font-secondary flex items-center space-x-1 transition hover:text-orange-600',
                  pathname === '/opportunities'
                    ? 'font-medium text-indigo-600 dark:text-orange-600'
                    : 'text-gray-900 dark:text-gray-50 dark:hover:text-orange-600'
                )}
              >
                <BriefcaseBusiness className="h-4 w-4" />
                <span> Opportunities </span>
              </Link>
            </HoverCardTrigger>
            <HoverCardContent className="flex min-w-min justify-between space-x-4 divide-x divide-indigo-400">
              <div className="min-w-32 pr-4">
                <Link
                  href="/opportunities/jobs"
                  className={cn(
                    'font-primary mb-2 flex items-center space-x-1 py-1 text-sm font-semibold transition hover:text-orange-600',
                    pathname === '/opportunities/jobs'
                      ? 'font-medium text-orange-600'
                      : 'text-indigo-600'
                  )}
                >
                  <span> Jobs </span>
                </Link>
                <h5 className="font-primary mb-2 text-xs font-medium">
                  Categories
                </h5>
                <ItemsLink items={jobCategories} />
              </div>
              <div className="min-w-32 pr-4">
                <Link
                  href="/opportunities/internships"
                  className={cn(
                    'font-primary mb-2 flex items-center space-x-1 py-1 text-sm font-semibold transition hover:text-orange-600',
                    pathname === '/opportunities/internships'
                      ? 'font-medium text-indigo-600'
                      : 'text-indigo-600'
                  )}
                >
                  <span> Internships </span>
                </Link>
                <h5 className="font-primary mb-2 text-xs font-semibold">
                  Categories
                </h5>
                <ItemsLink items={internshipCategories} />
              </div>{' '}
              {/* <div className="min-w-32 pr-4">
                <Link
                  href="/opportunities/freelance"
                  className={cn(
                    'font-primary mb-2 flex items-center space-x-1 py-1 text-sm font-semibold transition hover:text-orange-600',
                    pathname === '/opportunities/freelance'
                      ? 'font-medium text-indigo-600'
                      : 'text-indigo-600'
                  )}
                >
                  <span> Freelance </span>
                </Link>
                <h5 className="font-primary mb-2 text-xs font-semibold">
                  Categories
                </h5>
                <ItemsLink items={freelanceCategories} />
              </div> */}
            </HoverCardContent>
          </HoverCard>

          {/* <HoverCard>
            <HoverCardTrigger asChild>
              <Link
                href="/professional-certifications"
                className={cn(
                  'font-secondary flex items-center space-x-1 transition hover:text-orange-600',
                  pathname === '/professional-certifications'
                    ? 'font-medium text-indigo-600 dark:text-orange-600'
                    : 'text-gray-900 dark:text-gray-50 dark:hover:text-orange-600'
                )}
              >
                <GraduationCap className="h-4 w-4" />
                <span>Skill Certifications</span>
              </Link>
            </HoverCardTrigger>
            <HoverCardContent className="">
              <ItemsLink items={skillCertifications} />
            </HoverCardContent>
          </HoverCard> */}

          <HoverCard>
            <HoverCardTrigger asChild>
              <Link
                href="/events-and-webinars"
                className={cn(
                  'font-secondary flex items-center space-x-1 transition hover:text-orange-600',
                  pathname === '/events-and-webinars'
                    ? 'font-medium text-indigo-600 dark:text-orange-600'
                    : 'text-gray-900 dark:text-gray-50 dark:hover:text-orange-600'
                )}
              >
                <CalendarCheck className="h-4 w-4" />
                <span>Events & Webinars</span>
              </Link>
            </HoverCardTrigger>
            <HoverCardContent className="">
              <ItemsLink items={eventSubMenu} />
            </HoverCardContent>
          </HoverCard>
        </nav>
        <div className="hidden space-x-3 lg:inline-flex">
          <ModeToggle />

          <Link
            href={isLogin ? '/dashboard' : '/sign-in'}
            className="font-primary items-center justify-center rounded-md bg-gradient-to-r from-blue-600 to-purple-600 px-3 py-2 text-xs font-semibold text-white shadow-lg transition-all duration-300 hover:-translate-y-1 hover:scale-105 hover:from-blue-700 hover:to-purple-700 hover:shadow-xl"
          >
            Start Your Journey
          </Link>
        </div>
        {/* Mobile Menu Button */}
        <button
          aria-label="Toggle Menu"
          aria-expanded={isMenuOpen}
          onClick={() => setIsMenuOpen(!isMenuOpen)}
          className="text-muted-foreground hover:bg-muted inline-flex min-h-10 min-w-10 items-center justify-center rounded-md p-2 lg:hidden"
        >
          {isMenuOpen ? (
            <X className="h-6 w-6" />
          ) : (
            <Menu className="h-6 w-6" />
          )}
        </button>
      </div>

      {/* Mobile Nav */}
      {isMenuOpen && (
        <div
          ref={menuRef}
          className="bg-background border-muted absolute top-20 left-[5%] z-50 w-[90%] rounded-lg p-5 drop-shadow-lg lg:hidden"
        >
          <nav className="font-urbanist space-y-2 text-sm">
            {navItems.map((item, idx) => (
              <Link
                key={idx}
                href={item.link}
                onClick={() => setIsMenuOpen(false)}
                className={cn(
                  'font-primary flex items-center space-x-2 py-1.5 transition',
                  pathname === item.link
                    ? 'font-semibold text-indigo-600 dark:text-orange-600'
                    : 'text-gray-900 dark:text-gray-50'
                )}
              >
                {item.icon}
                <span>{item.name}</span>
              </Link>
            ))}

            <button className="mx-auto mt-4 items-center justify-center rounded-md bg-gradient-to-r from-blue-600 to-purple-600 px-4 py-2 text-xs font-semibold text-white shadow-lg transition-all duration-300 hover:-translate-y-1 hover:scale-105 hover:from-blue-700 hover:to-purple-700 hover:shadow-xl">
              Start Your Journey
            </button>
          </nav>
        </div>
      )}
    </header>
  );
};

export default NavBar;
