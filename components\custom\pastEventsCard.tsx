import { motion } from 'framer-motion';
import {
  Calendar,
  Clock,
  MapPin,
  Users,
  Tag,
  DollarSign,
  Award,
  PlayCircle,
  Download,
} from 'lucide-react';

type EventType = {
  _id: string;
  bannerImage?: string | null;
  title: string;
  description: string;
  startTime: string;
  endTime: string;
  timezone: string;
  durationMinutes: number;
  mode: string;
  platform: string;
  registeredUsers?: any[];
  isActive: boolean;
  isDeleted: boolean;
  isFeatured?: boolean;
  recordingAccess?: boolean;
  certificateOffered?: boolean;
  resourcesProvided?: boolean;
  tags?: string[];
  category?: string;
  level?: string;
  hostName?: string;
  isPaid?: boolean;
  currency?: string;
  price?: number;
  capacity?: number;
};

const formatDateTime = (dateString: string, timezone: string) => {
  const date = new Date(dateString);
  const options = {
    timeZone: timezone,
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  } as const;
  return date.toLocaleString('en-US', options);
};

const formatDuration = (minutes: number) => {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  if (hours > 0) {
    return mins > 0 ? `${hours}h ${mins}m` : `${hours}h`;
  }
  return `${mins}m`;
};

const getPastEvents = (events: EventType[]) => {
  const now = new Date();
  return events
    .filter(event => {
      const endTime = new Date(event.endTime);
      return endTime < now && event.isActive && !event.isDeleted;
    })
    .sort(
      (a, b) => new Date(b.endTime).getTime() - new Date(a.endTime).getTime()
    );
};

const defaultEventImage = '/images/job.png';

const PastEventCards = ({ events = [] }: { events: EventType[] }) => {
  const pastEvents = getPastEvents(events);

  if (pastEvents.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-16 text-center">
        <Calendar className="mb-4 h-16 w-16 text-gray-400 dark:text-gray-600" />
        <h3 className="mb-2 text-xl font-semibold text-gray-600 dark:text-gray-400">
          No Past Events
        </h3>
        <p className="text-gray-500 dark:text-gray-500">
          Past events will appear here once they're completed
        </p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
      {pastEvents.slice(0,2).map((event, idx) => (
        <motion.div
          key={event._id}
          className="flex flex-col gap-6 rounded-2xl border border-gray-100 bg-white p-6 shadow-lg transition-transform duration-300 hover:scale-[1.02] md:flex-row dark:border-gray-800 dark:bg-gray-900"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ delay: idx * 0.1, duration: 0.5 }}
          viewport={{ once: true }}
        >
          <div className="relative">
            <img
              src={event.bannerImage || defaultEventImage}
              alt={event.title}
              className="h-full w-full rounded-xl object-cover md:w-40"
              loading="lazy"
            />

            {event.isFeatured && (
              <div className="absolute -top-2 -right-2 rounded-full bg-yellow-400 p-2">
                <Award className="h-4 w-4 text-yellow-800" />
              </div>
            )}

            {event.recordingAccess && (
              <div className="absolute right-2 bottom-2 rounded-full bg-red-500 p-1">
                <PlayCircle className="h-4 w-4 text-white" />
              </div>
            )}
          </div>

          <div className="flex-1">
            <div className="mb-2 flex items-center gap-2">
              <span className="rounded-full bg-blue-100 px-2 py-1 text-xs font-medium text-blue-700 dark:bg-blue-900 dark:text-blue-300">
                {event.category}
              </span>
              <span className="rounded-full bg-gray-100 px-2 py-1 text-xs font-medium text-gray-700 dark:bg-gray-800 dark:text-gray-300">
                {event.level}
              </span>
            </div>

            <h3 className="mb-2 text-lg font-bold text-blue-700 dark:text-blue-400">
              {event.title}
            </h3>

            <div className="mb-3 space-y-1 text-sm text-gray-500 dark:text-gray-400">
              <div className="flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                <span>{formatDateTime(event.startTime, event.timezone)}</span>
              </div>
              <div className="flex items-center gap-1">
                <Clock className="h-3 w-3" />
                <span>{formatDuration(event.durationMinutes)}</span>
              </div>
              <div className="flex items-center gap-1">
                <MapPin className="h-3 w-3" />
                <span>
                  {event.mode} • {event.platform}
                </span>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400">
                <span>Hosted by {event.hostName}</span>
              </div>

              <div className="flex items-center gap-2">
                {event.certificateOffered && (
                  <div className="flex items-center gap-1 text-xs text-green-600 dark:text-green-400">
                    <Award className="h-3 w-3" />
                    <span>Certificate</span>
                  </div>
                )}
                {event.recordingAccess && (
                  <div className="flex items-center gap-1 rounded-full bg-red-100 px-2 py-1 text-xs font-medium text-red-700 dark:bg-red-900 dark:text-red-300">
                    <PlayCircle className="h-3 w-3" />
                    <span>Recording</span>
                  </div>
                )}
                {event.resourcesProvided && (
                  <div className="flex items-center gap-1 rounded-full bg-purple-100 px-2 py-1 text-xs font-medium text-purple-700 dark:bg-purple-900 dark:text-purple-300">
                    <Download className="h-3 w-3" />
                    <span>Resources</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </motion.div>
      ))}
    </div>
  );
};

export default PastEventCards;
