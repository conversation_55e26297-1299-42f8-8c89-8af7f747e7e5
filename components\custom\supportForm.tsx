'use client';
import { useState } from 'react';
import Image from 'next/image';
import axios from 'axios';
import { z } from 'zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '../ui/form';
import { Input } from '../ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select';

const supportSchema = z.object({
  fullName: z.string().min(1, 'Full name is required'),
  email: z.string().email('Invalid email address'),
  phone: z.string().optional(),
  issueType: z.enum(['Technical', 'Billing', 'Access', 'Feedback', 'Other'], {
    required_error: 'Issue type is required',
  }),
  subject: z.string().min(1, 'Subject is required'),
  description: z.string().min(1, 'Description is required'),
  priority: z.enum(['Low', 'Medium', 'High', 'Urgent']).default('Medium'),
});

type SupportFormData = {
  fullName: string;
  email: string;
  phone: string;
  issueType: 'Technical' | 'Billing' | 'Access' | 'Feedback' | 'Other' | '';
  subject: string;
  description: string;
  priority: 'Low' | 'Medium' | 'High' | 'Urgent';
};

const SupportSection = () => {
  const [formData, setFormData] = useState<SupportFormData>({
    fullName: '',
    email: '',
    phone: '',
    issueType: '',
    subject: '',
    description: '',
    priority: 'Medium',
  });
  const [files, setFiles] = useState<FileList | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState('idle');
  const [formError, setFormError] = useState<string | null>(null);
  const [ticketId, setTicketId] = useState<string | null>(null);

  const handleInputChange = (e: React.ChangeEvent<
        HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
      >) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFiles(e.target.files);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormError(null);

    // Validate form data
    const result = supportSchema.safeParse(formData);
    if (!result.success) {
      setFormError(result.error.errors[0]?.message || 'Validation failed');
      setSubmitStatus('error');
      return;
    }

    setIsSubmitting(true);

    try {
      const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || '';
      const url = new URL('/api/v1/support', baseUrl);

      // Create FormData for file upload
      const submitData = new FormData();
      console.log('Submitting support ticket with data:', formData);
      Object.entries(formData).forEach(([key, value]) => {
        if (value) {
          submitData.append(key, value);
        }
      });

      // Add files if any
      if (files) {
        for (let i = 0; i < files.length; i++) {
          submitData.append('files', files[i]);
        }
      }

      const response = await axios.post(url.toString(), formData);

      if (response.data?.success) {
        setSubmitStatus('success');
        setTicketId(response.data?.ticketId || null);
        setFormData({
          fullName: '',
          email: '',
          phone: '',
          issueType: '',
          subject: '',
          description: '',
          priority: 'Medium',
        });
        setFiles(null);
        // Reset file input
        const fileInput = document.getElementById('files') as HTMLInputElement;
        if (fileInput) fileInput.value = '';
        
        setTimeout(() => {
          setSubmitStatus('idle');
          setTicketId(null);
        }, 5000);
      } else {
        console.log(response.data);
        setFormError(response.data?.message || 'Submission failed');
        setSubmitStatus('error');
      }
    } catch (error: any) {
      console.log(error.response?.data);
      setFormError(error?.response?.data?.message || 'Submission failed');
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <section className="relative overflow-hidden bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30 py-16 lg:py-20 dark:from-gray-900 dark:via-orange-900/10 dark:to-red-900/10">
      {/* Background Gradients */}
      <div className="pointer-events-none absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(251,146,60,0.1),transparent_50%)] dark:bg-[radial-gradient(circle_at_30%_20%,rgba(251,146,60,0.05),transparent_50%)]"></div>
      <div className="pointer-events-none absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(239,68,68,0.1),transparent_50%)] dark:bg-[radial-gradient(circle_at_70%_80%,rgba(239,68,68,0.05),transparent_50%)]"></div>

      <div className="relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="mb-12 text-center">
          <div className="mb-4 inline-flex items-center gap-2 rounded-full bg-gradient-to-r from-orange-100 to-red-100 px-4 py-2 text-sm font-medium text-gray-700 dark:from-orange-900/50 dark:to-red-900/50 dark:text-gray-300">
            <span className="text-lg">🛠️</span>
            Technical Support
          </div>
          <h2 className="font-nunito mb-4 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl lg:text-5xl dark:text-white">
            Get{' '}
            <span className="bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
              Support
            </span>
          </h2>
          <p className="mx-auto max-w-2xl text-lg text-gray-600 dark:text-gray-300">
            Need help? Submit a support ticket and our team will get back to you
            with a solution as quickly as possible.
          </p>
        </div>

        <div className="grid items-center gap-12 lg:grid-cols-12">
          {/* Left Side - Image */}
          <div className="relative order-2 lg:order-1 lg:col-span-5">
            <div className="absolute -inset-4 rounded-3xl bg-gradient-to-r from-orange-400 via-red-400 to-pink-400 opacity-20 blur-2xl"></div>
            <div className="relative rounded-3xl bg-white p-3 shadow-2xl dark:bg-gray-800">
              <Image
                src="https://images.unsplash.com/photo-1553877522-43269d4ea984?w=600&h=600&fit=crop"
                width={500}
                height={500}
                className="rounded-2xl"
                alt="Support team"
              />
            </div>

            {/* Floating Elements */}
            <div className="absolute -top-3 -right-3 rounded-xl bg-white p-3 shadow-lg dark:bg-gray-800">
              <div className="flex items-center gap-2">
                <div className="h-2.5 w-2.5 rounded-full bg-green-500"></div>
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  24/7 Support
                </span>
              </div>
            </div>

            <div className="absolute -bottom-3 -left-3 rounded-xl bg-white p-3 shadow-lg dark:bg-gray-800">
              <div className="flex items-center gap-2">
                <span className="text-lg">🎯</span>
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Expert Help
                </span>
              </div>
            </div>
          </div>

          {/* Right Side - Form Card */}
          <div className="order-1 lg:order-2 lg:col-span-7">
            <div className="relative">
              {/* Card Background with Gradient Border */}
              <div className="absolute -inset-1 rounded-3xl bg-gradient-to-r from-orange-500 via-red-500 to-pink-500 opacity-20 blur-lg"></div>
              <div className="relative rounded-3xl border border-white/50 bg-white/80 p-6 shadow-2xl backdrop-blur-xl lg:p-8 dark:border-gray-700/50 dark:bg-gray-800/80">
                {/* Form Header */}
                <div className="mb-6 text-center">
                  <div className="mb-3 inline-flex h-10 w-10 items-center justify-center rounded-lg bg-gray-100 dark:bg-gray-700">
                    <span className="text-lg text-gray-600 dark:text-gray-300">
                      🎫
                    </span>
                  </div>
                  <h3 className="font-nunito mb-2 text-xl font-semibold text-gray-900 dark:text-white">
                    Submit Support Ticket
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    Describe your issue and we'll help you resolve it quickly.
                    Include any relevant files or screenshots.
                  </p>
                </div>

                {/* Success/Error Message */}
                {submitStatus === 'success' && (
                  <div className="mb-4 rounded-lg border border-green-200/50 bg-green-50 p-4 dark:border-green-700/50 dark:bg-green-900/30">
                    <div className="flex items-start gap-3">
                      <div className="flex h-5 w-5 flex-shrink-0 items-center justify-center rounded-full bg-green-100 dark:bg-green-800">
                        <span className="text-xs text-green-600 dark:text-green-300">
                          ✓
                        </span>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-green-800 dark:text-green-300">
                          Support ticket submitted successfully!
                        </p>
                        {ticketId && (
                          <p className="text-xs text-green-700 dark:text-green-400 mt-1">
                            Your ticket ID: <span className="font-mono font-semibold">{ticketId}</span>
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                )}
                {formError && (
                  <div className="mb-4 rounded-lg border border-red-200/50 bg-red-50 p-3 dark:border-red-700/50 dark:bg-red-900/30">
                    <div className="flex items-center gap-2">
                      <div className="flex h-5 w-5 flex-shrink-0 items-center justify-center rounded-full bg-red-100 dark:bg-red-800">
                        <span className="text-xs text-red-600 dark:text-red-300">
                          !
                        </span>
                      </div>
                      <p className="text-sm font-medium text-red-800 dark:text-red-300">
                        {formError}
                      </p>
                    </div>
                  </div>
                )}

                {/* Support Form */}
                <form onSubmit={handleSubmit} className="space-y-4">
                  {/* Name and Email Row */}
                  <div className="grid gap-4 sm:grid-cols-2">
                    <div>
                      <label
                        htmlFor="fullName"
                        className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300"
                      >
                        Full Name *
                      </label>
                      <input
                        type="text"
                        id="fullName"
                        name="fullName"
                        value={formData.fullName}
                        onChange={handleInputChange}
                        required
                        className="w-full rounded-lg border border-gray-200 bg-gray-50/30 px-4 py-3 text-sm text-gray-900 placeholder-gray-400 transition-all duration-300 focus:border-orange-500 focus:bg-white focus:ring-2 focus:ring-orange-500/20 focus:outline-none dark:border-gray-600 dark:bg-gray-700/30 dark:text-white dark:placeholder-gray-500 dark:focus:border-orange-400 dark:focus:bg-gray-700 dark:focus:ring-orange-400/20"
                        placeholder="Your full name"
                      />
                    </div>
                    <div>
                      <label
                        htmlFor="email"
                        className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300"
                      >
                        Email Address *
                      </label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        required
                        className="w-full rounded-lg border border-gray-200 bg-gray-50/30 px-4 py-3 text-sm text-gray-900 placeholder-gray-400 transition-all duration-300 focus:border-orange-500 focus:bg-white focus:ring-2 focus:ring-orange-500/20 focus:outline-none dark:border-gray-600 dark:bg-gray-700/30 dark:text-white dark:placeholder-gray-500 dark:focus:border-orange-400 dark:focus:bg-gray-700 dark:focus:ring-orange-400/20"
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>

                  {/* Phone and Issue Type Row */}
                  <div className="grid gap-4 sm:grid-cols-2">
                    <div>
                      <label
                        htmlFor="phone"
                        className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300"
                      >
                        Phone Number
                      </label>
                      <input
                        type="tel"
                        id="phone"
                        name="phone"
                        value={formData.phone}
                        onChange={handleInputChange}
                        className="w-full rounded-lg border border-gray-200 bg-gray-50/30 px-4 py-3 text-sm text-gray-900 placeholder-gray-400 transition-all duration-300 focus:border-orange-500 focus:bg-white focus:ring-2 focus:ring-orange-500/20 focus:outline-none dark:border-gray-600 dark:bg-gray-700/30 dark:text-white dark:placeholder-gray-500 dark:focus:border-orange-400 dark:focus:bg-gray-700 dark:focus:ring-orange-400/20"
                        placeholder="Your phone number"
                      />
                    </div>
                    <div>
                      <label
                        htmlFor="issueType"
                        className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300"
                      >
                        Issue Type *
                      </label>
                      <select
                        id="issueType"
                        name="issueType"
                        value={formData.issueType}
                        onChange={handleInputChange}
                        required
                        className="w-full rounded-lg border border-gray-200 bg-gray-50/30 px-4 py-3 text-sm text-gray-900 transition-all duration-300 focus:border-orange-500 focus:bg-white focus:ring-2 focus:ring-orange-500/20 focus:outline-none dark:border-gray-600 dark:bg-gray-700/30 dark:text-white dark:focus:border-orange-400 dark:focus:bg-gray-700 dark:focus:ring-orange-400/20"
                      >
                        <option value="">Select issue type</option>
                        <option value="Technical">Technical Issue</option>
                        <option value="Billing">Billing Question</option>
                        <option value="Access">Access Problem</option>
                        <option value="Feedback">Feedback</option>
                        <option value="Other">Other</option>
                      </select>
                    </div>
                  </div>

                  {/* Subject and Priority Row */}
                  <div className="grid gap-4 sm:grid-cols-2">
                    <div>
                      <label
                        htmlFor="subject"
                        className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300"
                      >
                        Subject *
                      </label>
                      <input
                        type="text"
                        id="subject"
                        name="subject"
                        value={formData.subject}
                        onChange={handleInputChange}
                        required
                        className="w-full rounded-lg border border-gray-200 bg-gray-50/30 px-4 py-3 text-sm text-gray-900 placeholder-gray-400 transition-all duration-300 focus:border-orange-500 focus:bg-white focus:ring-2 focus:ring-orange-500/20 focus:outline-none dark:border-gray-600 dark:bg-gray-700/30 dark:text-white dark:placeholder-gray-500 dark:focus:border-orange-400 dark:focus:bg-gray-700 dark:focus:ring-orange-400/20"
                        placeholder="Brief description of your issue"
                      />
                    </div>
                    <div>
                      <label
                        htmlFor="priority"
                        className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300"
                      >
                        Priority
                      </label>
                      <select
                        id="priority"
                        name="priority"
                        value={formData.priority}
                        onChange={handleInputChange}
                        className="w-full rounded-lg border border-gray-200 bg-gray-50/30 px-4 py-3 text-sm text-gray-900 transition-all duration-300 focus:border-orange-500 focus:bg-white focus:ring-2 focus:ring-orange-500/20 focus:outline-none dark:border-gray-600 dark:bg-gray-700/30 dark:text-white dark:focus:border-orange-400 dark:focus:bg-gray-700 dark:focus:ring-orange-400/20"
                      >
                        <option value="Low">Low</option>
                        <option value="Medium">Medium</option>
                        <option value="High">High</option>
                        <option value="Urgent">Urgent</option>
                      </select>
                    </div>
                  </div>

                  {/* Description */}
                  <div>
                    <label
                      htmlFor="description"
                      className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300"
                    >
                      Description *
                    </label>
                    <textarea
                      id="description"
                      name="description"
                      value={formData.description}
                      onChange={handleInputChange}
                      required
                      rows={4}
                      className="w-full rounded-lg border border-gray-200 bg-gray-50/30 px-4 py-3 text-sm text-gray-900 placeholder-gray-400 transition-all duration-300 focus:border-orange-500 focus:bg-white focus:ring-2 focus:ring-orange-500/20 focus:outline-none dark:border-gray-600 dark:bg-gray-700/30 dark:text-white dark:placeholder-gray-500 dark:focus:border-orange-400 dark:focus:bg-gray-700 dark:focus:ring-orange-400/20"
                      placeholder="Please describe your issue in detail. Include any error messages, steps to reproduce, or other relevant information..."
                    />
                  </div>

                  {/* File Upload */}
                  {/* <div>
                    <label
                      htmlFor="files"
                      className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300"
                    >
                      Attachments (Optional)
                    </label>
                    <input
                      type="file"
                      id="files"
                      name="files"
                      multiple
                      onChange={handleFileChange}
                      accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.txt,.zip"
                      className="w-full rounded-lg border border-gray-200 bg-gray-50/30 px-4 py-3 text-sm text-gray-900 transition-all duration-300 focus:border-orange-500 focus:bg-white focus:ring-2 focus:ring-orange-500/20 focus:outline-none dark:border-gray-600 dark:bg-gray-700/30 dark:text-white dark:focus:border-orange-400 dark:focus:bg-gray-700 dark:focus:ring-orange-400/20"
                    />
                    <p className="mt-2 text-xs text-gray-500 dark:text-gray-400">
                      Max 10MB per file. Supported formats: JPG, PNG, PDF, DOC, TXT, ZIP
                    </p>
                  </div> */}

                  {/* Submit Button */}
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full rounded-lg bg-gradient-to-r from-orange-600 to-red-600 px-6 py-3 text-sm font-medium text-white shadow-lg transition-all duration-300 hover:from-orange-700 hover:to-red-700 hover:shadow-xl focus:ring-2 focus:ring-orange-500/20 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50 dark:shadow-lg dark:hover:shadow-xl"
                  >
                    {isSubmitting ? (
                      <span className="flex items-center justify-center gap-2">
                        <svg
                          className="h-4 w-4 animate-spin"
                          viewBox="0 0 24 24"
                        >
                          <circle
                            className="opacity-25"
                            cx="12"
                            cy="12"
                            r="10"
                            stroke="currentColor"
                            strokeWidth="4"
                            fill="none"
                          />
                          <path
                            className="opacity-75"
                            fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                          />
                        </svg>
                        Submitting Ticket...
                      </span>
                    ) : (
                      <span className="flex items-center justify-center gap-2">
                        <span>Submit Support Ticket</span>
                        <span className="text-lg">🎫</span>
                      </span>
                    )}
                  </button>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default SupportSection;