'use client';
import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import axios from 'axios';
import { motion } from 'framer-motion';
import {
  Calendar,
  Clock,
  MapPin,
  Users,
  Tag,
  Award,
  Filter,
  SortAsc,
  SortDesc,
  Search,
  Globe,
  Building,
  User,
  DollarSign,
  Video,
  FileText,
  MapPinIcon,
  X,
  ChevronDown,
} from 'lucide-react';

type EventType = {
  _id: string;
  title: string;
  slug: string;
  description: string;
  tags?: string[];
  hostedByUsers?: Array<{
    user: {
      _id: string;
      firstName: string;
      lastName: string;
      email: string;
    };
    sortOrder: number;
  }>;
  hostedByCompanies?: Array<{
    user: {
      _id: string;
      name: string;
    };
    sortOrder: number;
  }>;
  hostName?: string;
  hostDesignation?: string;
  hostImage?: {
    _id: string;
    url: string;
  };
  bannerImage?: {
    _id: string;
    url: string;
  };
  language?: string;
  category?: string;
  level?: 'Beginner' | 'Intermediate' | 'Advanced';
  mode: 'Online' | 'Offline';
  platform?: string;
  meetingLink?: string;
  location?: string;
  timezone?: string;
  startTime: string;
  endTime?: string;
  durationMinutes?: number;
  isPaid: boolean;
  price?: number;
  currency?: string;
  registrationDeadline?: string;
  capacity?: number;
  registeredUsers: string[];
  certificateOffered: boolean;
  resourcesProvided?: boolean;
  recordingAccess?: boolean;
  isActive: boolean;
  isApproved: boolean;
  isFeatured?: boolean;
  createdBy: {
    _id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  createdAt: string;
  updatedAt: string;
};

type FilterState = {
  title?: string;
  category?: string;
  level?: string;
  mode?: string;
  language?: string;
  tags?: string;
  isPaid?: string;
  isFeatured?: string;
};

type SortState = {
  field: 'startTime' | 'createdAt' | 'title' | 'registeredUsers';
  order: 'asc' | 'desc';
};

const formatDateTime = (dateString: string, timezone?: string) => {
  const date = new Date(dateString);
  const options: Intl.DateTimeFormatOptions = {
    timeZone: timezone || 'UTC',
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  };
  return date.toLocaleString('en-US', options);
};

const formatDuration = (minutes: number) => {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  if (hours > 0) {
    return `${hours}h ${mins}m`;
  }
  return `${mins}m`;
};

function useDebounce<T>(value: T, delay: number) {
  const [debounced, setDebounced] = useState(value);
  useEffect(() => {
    const handler = setTimeout(() => setDebounced(value), delay);
    return () => clearTimeout(handler);
  }, [value, delay]);
  return debounced;
}

const defaultEventImage = '/images/annie-spratt-QckxruozjRg-unsplash.jpg';

export default function UpcomingEvents() {
  const [events, setEvents] = useState<EventType[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<FilterState>({});
  const [sort, setSort] = useState<SortState>({
    field: 'startTime',
    order: 'asc',
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(12);
  const [meta, setMeta] = useState<{
    total: number;
    totalPages: number;
    page: number;
    limit: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  } | null>(null);

  const debouncedSearch = useDebounce(searchTerm, 1000);

  const categoryOptions = [
    'Web Development',
    'Data Science',
    'Mobile Development',
    'AI/ML',
    'DevOps',
    'Design',
    'Marketing',
  ];
  const levelOptions = ['Beginner', 'Intermediate', 'Advanced'];
  const modeOptions = ['Online', 'Offline'];
  const languageOptions = ['English', 'Hindi', 'Spanish', 'French', 'German'];

  const fetchEvents = async () => {
    try {
      setLoading(true);
      const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
      if (!baseUrl) {
        throw new Error('NEXT_PUBLIC_BASE_URL environment variable is not set');
      }

      const params = new URLSearchParams();
      if (debouncedSearch) params.append('title', debouncedSearch);
      Object.entries(filters).forEach(([key, value]) => {
        if (value) params.append(key, value);
      });
      params.append('sortBy', sort.field);
      params.append('sortOrder', sort.order);
      params.append('isActive', 'true');
      params.append('isApproved', 'true');
      params.append('page', String(page));
      params.append('limit', String(limit));

      const url = new URL('/api/v1/events', baseUrl);
      url.search = params.toString();

      const response = await axios.get(url.toString());
      if (!response.data.success) {
        throw new Error(response.data.error || 'Failed to fetch events');
      }

      const now = new Date();
      const upcomingEvents = response.data.data.events.filter(
        (event: EventType) => {
          const eventEndTime = event.endTime
            ? new Date(event.endTime)
            : new Date(event.startTime);
          return eventEndTime > now;
        }
      );

      setEvents(upcomingEvents);
      setMeta(response.data.data.meta);
      setError(null);
    } catch (err: any) {
      setEvents([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchEvents();
  }, [filters, sort, debouncedSearch, page, limit]);

  const handleFilterChange = (key: keyof FilterState, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value || undefined,
    }));
  };

  const clearFilters = () => {
    setFilters({});
    setSearchTerm('');
  };

  const getHostName = (event: EventType) => {
    if (event.hostName) return event.hostName;
    if (event.hostedByUsers?.[0]) {
      const user = event.hostedByUsers[0].user;
      return `${user.firstName} ${user.lastName}`;
    }
    if (event.hostedByCompanies?.[0]) {
      return event.hostedByCompanies[0].user.name;
    }
    return 'Unknown Host';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-16">
        <div className="h-12 w-12 animate-spin rounded-full border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <>
      <div className="mb-6 space-y-4">
        <div className="relative">
          <Search className="absolute top-3 left-3 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search events..."
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
            className="w-full rounded-lg border border-gray-300 py-2 pr-4 pl-10 focus:border-transparent focus:ring-2 focus:ring-blue-500 dark:border-gray-700 dark:bg-gray-800"
          />
        </div>

        <div className="flex flex-wrap items-center gap-4">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center gap-2 rounded-lg bg-gray-100 px-4 py-2 transition-colors hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700"
          >
            <Filter className="h-4 w-4" />
            Filters
            <ChevronDown
              className={`h-4 w-4 transition-transform ${showFilters ? 'rotate-180' : ''}`}
            />
          </button>

          <select
            value={`${sort.field}-${sort.order}`}
            onChange={e => {
              const [field, order] = e.target.value.split('-') as [
                typeof sort.field,
                typeof sort.order,
              ];
              setSort({ field, order });
            }}
            className="rounded-lg border border-gray-300 px-4 py-2 focus:ring-2 focus:ring-blue-500 dark:border-gray-700 dark:bg-gray-800"
          >
            <option value="startTime-asc">Start Time (Earliest First)</option>
            <option value="startTime-desc">Start Time (Latest First)</option>
            <option value="createdAt-desc">Newest First</option>
            <option value="createdAt-asc">Oldest First</option>
            <option value="title-asc">Title (A-Z)</option>
            <option value="title-desc">Title (Z-A)</option>
            <option value="registeredUsers-desc">Most Popular</option>
          </select>

          {(Object.keys(filters).length > 0 || searchTerm) && (
            <button
              onClick={clearFilters}
              className="flex items-center gap-2 px-4 py-2 text-red-600 transition-colors hover:text-red-700"
            >
              <X className="h-4 w-4" />
              Clear All
            </button>
          )}
        </div>

        {showFilters && (
          <div className="grid grid-cols-1 gap-4 rounded-lg bg-gray-50 p-4 md:grid-cols-2 lg:grid-cols-4 dark:bg-gray-800">
            <div>
              <label className="mb-2 block text-sm font-medium">Category</label>
              <select
                value={filters.category || ''}
                onChange={e => handleFilterChange('category', e.target.value)}
                className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
              >
                <option value="">All Categories</option>
                {categoryOptions.map(cat => (
                  <option key={cat} value={cat}>
                    {cat}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="mb-2 block text-sm font-medium">Level</label>
              <select
                value={filters.level || ''}
                onChange={e => handleFilterChange('level', e.target.value)}
                className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
              >
                <option value="">All Levels</option>
                {levelOptions.map(level => (
                  <option key={level} value={level}>
                    {level}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="mb-2 block text-sm font-medium">Mode</label>
              <select
                value={filters.mode || ''}
                onChange={e => handleFilterChange('mode', e.target.value)}
                className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
              >
                <option value="">All Modes</option>
                {modeOptions.map(mode => (
                  <option key={mode} value={mode}>
                    {mode}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="mb-2 block text-sm font-medium">Language</label>
              <select
                value={filters.language || ''}
                onChange={e => handleFilterChange('language', e.target.value)}
                className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
              >
                <option value="">All Languages</option>
                {languageOptions.map(lang => (
                  <option key={lang} value={lang}>
                    {lang}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="mb-2 block text-sm font-medium">Price</label>
              <select
                value={filters.isPaid || ''}
                onChange={e => handleFilterChange('isPaid', e.target.value)}
                className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
              >
                <option value="">All Events</option>
                <option value="false">Free Events</option>
                <option value="true">Paid Events</option>
              </select>
            </div>

            <div>
              <label className="mb-2 block text-sm font-medium">Featured</label>
              <select
                value={filters.isFeatured || ''}
                onChange={e => handleFilterChange('isFeatured', e.target.value)}
                className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
              >
                <option value="">All Events</option>
                <option value="true">Featured Only</option>
              </select>
            </div>
          </div>
        )}
      </div>

      {events.length === 0 ? (
        <div className="flex flex-col items-center justify-center py-16 text-center">
          <Calendar className="mb-4 h-16 w-16 text-gray-400 dark:text-gray-600" />
          <h3 className="mb-2 text-xl font-semibold text-gray-600 dark:text-gray-400">
            No Upcoming Events Found
          </h3>
          <p className="text-gray-500 dark:text-gray-500">
            Try adjusting your filters or check back later for new events
          </p>
        </div>
      ) : (
        <div className="space-y-6">
          {events.map((event, idx) => (
            <motion.div
              key={event._id}
              className="overflow-hidden rounded-xl bg-white shadow-lg transition-shadow duration-300 hover:shadow-xl dark:bg-gray-900"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: idx * 0.1 }}
            >
              <div className="flex flex-col lg:flex-row">
                <div className="relative lg:w-1/3">
                  <img
                    src={event.bannerImage?.url || defaultEventImage}
                    alt={event.title}
                    className="h-48 w-full object-cover lg:h-full"
                  />
                  {event.isFeatured && (
                    <div className="absolute top-4 left-4 flex items-center gap-1 rounded-full bg-yellow-400 px-3 py-1 text-sm font-semibold text-yellow-800">
                      <Award className="h-4 w-4" />
                      Featured
                    </div>
                  )}
                  <div className="absolute top-4 right-4">
                    {event.isPaid ? (
                      <div className="rounded-full bg-green-500 px-3 py-1 text-sm font-semibold text-white">
                        {event.currency} {event.price}
                      </div>
                    ) : (
                      <div className="rounded-full bg-blue-500 px-3 py-1 text-sm font-semibold text-white">
                        Free
                      </div>
                    )}
                  </div>
                </div>

                <div className="p-6 lg:w-2/3">
                  <div className="mb-3 flex flex-wrap items-center gap-2">
                    <span className="rounded-full bg-blue-100 px-3 py-1 text-sm font-medium text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                      {event.category}
                    </span>
                    <span className="rounded-full bg-gray-100 px-3 py-1 text-sm font-medium text-gray-800 dark:bg-gray-800 dark:text-gray-200">
                      {event.level}
                    </span>
                    <span className="rounded-full bg-purple-100 px-3 py-1 text-sm font-medium text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                      {event.language}
                    </span>
                    <Link href={`/dashboard/events-and-webinars/${event.slug}`} className="ml-auto">
                      <span className="rounded-full bg-gray-200 px-3 py-1 text-sm font-medium text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                        View Details
                      </span>
                    </Link>
                  </div>

                  <h3 className="mb-2 text-xl font-bold text-gray-900 dark:text-white">
                    {event.title}
                  </h3>

                  <p className="mb-4 line-clamp-2 text-gray-600 dark:text-gray-400">
                    {event.description}
                  </p>

                  <div className="mb-4 grid grid-cols-1 gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                        <Calendar className="h-4 w-4" />
                        <span>
                          {formatDateTime(event.startTime, event.timezone)}
                        </span>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                        <Clock className="h-4 w-4" />
                        <span>
                          {event.durationMinutes
                            ? formatDuration(event.durationMinutes)
                            : 'Duration TBD'}
                        </span>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                        {event.mode === 'Online' ? (
                          <Globe className="h-4 w-4" />
                        ) : (
                          <MapPinIcon className="h-4 w-4" />
                        )}
                        <span>
                          {event.mode === 'Online'
                            ? `${event.mode} • ${event.platform}`
                            : `${event.mode} • ${event.location}`}
                        </span>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                        <Users className="h-4 w-4" />
                        <span>
                          {event.registeredUsers?.length || 0}
                          {event.capacity && ` / ${event.capacity}`} registered
                        </span>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                        <User className="h-4 w-4" />
                        <span>By {getHostName(event)}</span>
                        {event.hostDesignation && (
                          <span className="text-gray-500">
                            • {event.hostDesignation}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="mb-4 flex flex-wrap gap-2">
                    {event.certificateOffered && (
                      <div className="flex items-center gap-1 rounded-full bg-green-100 px-2 py-1 text-xs text-green-800 dark:bg-green-900 dark:text-green-200">
                        <Award className="h-3 w-3" />
                        Certificate
                      </div>
                    )}
                    {event.recordingAccess && (
                      <div className="flex items-center gap-1 rounded-full bg-purple-100 px-2 py-1 text-xs text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                        <Video className="h-3 w-3" />
                        Recording
                      </div>
                    )}
                    {event.resourcesProvided && (
                      <div className="flex items-center gap-1 rounded-full bg-blue-100 px-2 py-1 text-xs text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                        <FileText className="h-3 w-3" />
                        Resources
                      </div>
                    )}
                  </div>

                  {event.tags && event.tags.length > 0 && (
                    <div className="mb-4 flex flex-wrap gap-1">
                      {event.tags.slice(0, 5).map((tag, tagIdx) => (
                        <span
                          key={tagIdx}
                          className="inline-flex items-center gap-1 rounded-full bg-gray-100 px-2 py-1 text-xs text-gray-600 dark:bg-gray-800 dark:text-gray-400"
                        >
                          <Tag className="h-2 w-2" />
                          {tag}
                        </span>
                      ))}
                      {event.tags.length > 5 && (
                        <span className="text-xs text-gray-500">
                          +{event.tags.length - 5} more
                        </span>
                      )}
                    </div>
                  )}

                  <div className="flex items-center justify-between">
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {event.registrationDeadline && (
                        <span>
                          Registration closes:{' '}
                          {formatDateTime(event.registrationDeadline)}
                        </span>
                      )}
                    </div>
                    <button className="rounded-lg bg-blue-600 px-6 py-2 font-medium text-white transition-colors hover:bg-blue-700">
                      Register Now
                    </button>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      )}

      {meta && meta.totalPages > 1 && (
        <div className="mt-8 flex items-center justify-center gap-4">
          <button
            onClick={() => setPage(p => Math.max(1, p - 1))}
            disabled={page === 1}
            className="rounded bg-gray-200 px-4 py-2 text-gray-700 disabled:opacity-50 dark:bg-gray-700 dark:text-gray-200"
          >
            Previous
          </button>
          <span>
            Page {meta.page} of {meta.totalPages}
          </span>
          <button
            onClick={() => setPage(p => Math.min(meta.totalPages, p + 1))}
            disabled={page === meta.totalPages}
            className="rounded bg-gray-200 px-4 py-2 text-gray-700 disabled:opacity-50 dark:bg-gray-700 dark:text-gray-200"
          >
            Next
          </button>
        </div>
      )}
    </>
  );
}
