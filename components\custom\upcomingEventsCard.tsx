import { motion } from 'framer-motion';
import {
  Calendar,
  Clock,
  MapPin,
  Users,
  Tag,
  DollarSign,
  Award,
} from 'lucide-react';

const formatDateTime = (dateString: string, timezone: string) => {
  const date = new Date(dateString);
  const options: Intl.DateTimeFormatOptions = {
    timeZone: timezone,
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  };
  return date.toLocaleString('en-US', options);
};

const formatDuration = (minutes: number) => {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  if (hours > 0) {
    return `${hours}h ${mins}m`;
  }
  return `${mins}m`;
};

type EventType = {
  _id: string;
  bannerImage?: string | null;
  title: string;
  description: string;
  startTime: string;
  endTime: string;
  timezone: string;
  durationMinutes: number;
  mode: string;
  platform: string;
  registeredUsers?: any[];
  isActive: boolean;
  isDeleted: boolean;
  isFeatured?: boolean;
  recordingAccess?: boolean;
  certificateOffered?: boolean;
  resourcesProvided?: boolean;
  tags?: string[];
  category?: string;
  level?: string;
  hostName?: string;
  isPaid?: boolean;
  currency?: string;
  price?: number;
  capacity?: number;
};

const getUpcomingEvents = (events: EventType[]) => {
  const now = new Date();
  return events
    .filter(event => new Date(event.startTime) > now)
    .sort(
      (a, b) =>
        new Date(a.startTime).getTime() - new Date(b.startTime).getTime()
    );
};

const defaultEventImage =
  'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=400&h=200&fit=crop';

const UpcomingEventCards = ({ events }: { events: EventType[] }) => {
  const upcomingEvents = getUpcomingEvents(events);

  if (upcomingEvents.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-16 text-center">
        <Calendar className="mb-4 h-16 w-16 text-gray-400 dark:text-gray-600" />
        <h3 className="mb-2 text-xl font-semibold text-gray-600 dark:text-gray-400">
          No Upcoming Events
        </h3>
        <p className="text-gray-500 dark:text-gray-500">
          Check back later for new events and workshops
        </p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
      {upcomingEvents.slice(0,2).map((event, idx) => (
        <motion.div
          key={event._id}
          className="flex flex-col gap-6 rounded-2xl border border-gray-100 bg-white p-6 shadow-lg transition-transform duration-300 hover:scale-[1.02] md:flex-row dark:border-gray-800 dark:bg-gray-900"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ delay: idx * 0.1, duration: 0.5 }}
          viewport={{ once: true }}
        >
          <div className="relative">
            <img
              src={event.bannerImage || defaultEventImage}
              alt={event.title}
              className="h-full w-full rounded-xl object-cover md:w-40"
              loading="lazy"
            />
            {event.isFeatured && (
              <div className="absolute -top-2 -right-2 rounded-full bg-yellow-400 p-2">
                <Award className="h-4 w-4 text-yellow-800" />
              </div>
            )}
            {event.isPaid && (
              <div className="absolute bottom-2 left-2 rounded-full bg-green-500 px-2 py-1 text-xs font-medium text-white">
                {event.currency} {event.price}
              </div>
            )}
            {!event.isPaid && (
              <div className="absolute bottom-2 left-2 rounded-full bg-blue-500 px-2 py-1 text-xs font-medium text-white">
                Free
              </div>
            )}
          </div>

          <div className="flex-1">
            <div className="mb-2 flex items-center gap-2">
              <span className="rounded-full bg-blue-100 px-2 py-1 text-xs font-medium text-blue-700 dark:bg-blue-900 dark:text-blue-300">
                {event.category}
              </span>
              <span className="rounded-full bg-gray-100 px-2 py-1 text-xs font-medium text-gray-700 dark:bg-gray-800 dark:text-gray-300">
                {event.level}
              </span>
            </div>

            <h3 className="mb-2 text-lg font-bold text-blue-700 dark:text-blue-400">
              {event.title}
            </h3>

            <div className="mb-3 space-y-1 text-sm text-gray-500 dark:text-gray-400">
              <div className="flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                <span>{formatDateTime(event.startTime, event.timezone)}</span>
              </div>
              <div className="flex items-center gap-1">
                <Clock className="h-3 w-3" />
                <span>{formatDuration(event.durationMinutes)}</span>
              </div>
              <div className="flex items-center gap-1">
                <MapPin className="h-3 w-3" />
                <span>
                  {event.mode} • {event.platform}
                </span>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400">
                <span>By {event.hostName}</span>
              </div>

              <div className="flex items-center gap-2">
                {event.certificateOffered && (
                  <div className="flex items-center gap-1 text-xs text-green-600 dark:text-green-400">
                    <Award className="h-3 w-3" />
                    <span>Certificate</span>
                  </div>
                )}
                {event.recordingAccess && (
                  <div className="rounded-full bg-purple-100 px-2 py-1 text-xs font-medium text-purple-700 dark:bg-purple-900 dark:text-purple-300">
                    Recording
                  </div>
                )}
              </div>
            </div>
          </div>
        </motion.div>
      ))}
    </div>
  );
};

export default UpcomingEventCards;
