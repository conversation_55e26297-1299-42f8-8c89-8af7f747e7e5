"use client"


import {
  Building2,
  Mail,
  Phone,
  MapPin,
  ArrowRight
} from 'lucide-react';
import Link from 'next/link';


const truncateWords = (text: string, maxWords: number) => {
  if (!text) return ''; // Handle null or undefined text
  const words = text.split(/\s+/).filter(word => word.length > 0); // Split by whitespace and remove empty strings
  if (words.length <= maxWords) {
    return text;
  }
  return words.slice(0, maxWords).join(' ') + '...';
};

import { ICompany } from '@/types/ICompany';

interface CompanyCardProps {
  company: ICompany;
}


export default function CompanyCard({company}: CompanyCardProps) {
  // console.log("company card", company)

   const truncatedDescription = truncateWords(company.description, 15);

  const primaryWebsite = company.websites?.find(w => w.isDefault) || company.websites?.[0];

  const websiteUrl = primaryWebsite?.website || '';
  
  // Format website URL for display (remove http/https)
  const displayWebsite = websiteUrl.replace(/^https?:\/\//, '');

const cities = company.addresses.map(item => item.address.city); // Correct
const phone = company.phones.map(item => item.phone)

  return (
    <div className=" p-6 flex flex-wrap gap-10">
      <div className="max-w-sm w-full">
        <div className="bg-white dark:bg-[#171717] rounded-2xl  border border-slate-100 dark:border-[#2e2e2e] overflow-hidden">
          
          {/* Header with gradient accent */}
          <div className="relative p-6 pb-4">
            
            <div className="flex items-start space-x-4 mb-4">
              <div className="w-14 h-14 bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-blue-950 dark:to-indigo-900 rounded-2xl flex items-center justify-center border border-blue-100/50 dark:border-blue-800">
                <Building2 className="w-7 h-7 text-blue-600 dark:text-blue-400" />
              </div>
              <div className="flex-1">
                <h1 className="text-xl font-bold text-slate-900 dark:text-white mb-1">{company.name}</h1>
                <span className="inline-block px-3 py-1 bg-slate-100 dark:bg-slate-700 text-slate-700 dark:text-slate-200 text-sm font-medium rounded-full">
                  {company.industry}
                </span>
              </div>
            </div>

            {/* Description with modern typography */}
            <p className="text-slate-600 dark:text-slate-300 text-sm leading-relaxed mb-5 font-medium">
              {truncatedDescription}
            </p>

            {/* Contact Info with grid layout */}
            <div className="grid grid-cols-1 gap-4 mb-4">


<div className="flex items-center">
                <div className="w-8 h-8 bg-blue-50 dark:bg-blue-950 rounded-lg flex items-center justify-center mr-3">
                  <Mail className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                </div>
                <a href={websiteUrl.startsWith('http') ? websiteUrl : `https://${websiteUrl}`}  className="text-slate-700 dark:text-slate-200 hover:text-blue-600 dark:hover:text-blue-400 font-medium text-sm transition-colors">
                  {displayWebsite}
                </a>
              </div>
             
              
               <div className="flex items-center">
                <div className="w-8 h-8 bg-purple-50 dark:bg-purple-950 rounded-lg flex items-center justify-center mr-3">
                  <Phone className="w-4 h-4 text-purple-600 dark:text-purple-400" />
                </div>
                <a href={`tel:${phone}`} className="text-slate-700 dark:text-slate-200 hover:text-purple-600 dark:hover:text-purple-400 font-medium text-sm transition-colors">
                  {phone}
                </a>
              </div>

              

               <div className=" flex items-center">
                <div className="w-8 h-8 bg-emerald-50 dark:bg-emerald-950 rounded-lg flex items-center justify-center mr-3">
                  <MapPin className="w-4 h-4 text-emerald-600 dark:text-emerald-400" />
                </div>
                <span className="text-slate-700 dark:text-slate-200 font-medium text-sm">{cities}</span>
              </div>
              
             
            </div>
          </div>

          {/* Modern View More Button */}
          <div className="px-6 pb-6">
            <Link href={`/dashboard/companies/${company.slug}`}>
            <button className="w-full bg-slate-900 dark:bg-[#262626] hover:bg-slate-800 dark:hover:bg-[#292929] text-white font-semibold py-2 px-2 rounded-md flex items-center justify-center space-x-2 transition-all duration-200 active:scale-95">
              <span>View More Details</span>
              <ArrowRight className="w-4 h-4 group-hover/btn:translate-x-1 transition-transform duration-200" />
            </button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}