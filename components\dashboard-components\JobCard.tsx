"use client"

import React from 'react';
import { MapPin, Clock,ExternalLink, ArrowRight, Bookmark, Building2, Calendar, Users, DollarSign, } from 'lucide-react';
import Link from 'next/link';

import { IJob } from '@/types/IJob';

interface JobCardProps {
  jobs: IJob;
}

    const JobCard = ({ jobs } : JobCardProps) => {

       function getDaysAgo(pastDate: Date) {
    const today = new Date();
    // Set both dates to midnight to ensure accurate day difference without time component affecting it
    today.setHours(0, 0, 0, 0);
    pastDate.setHours(0, 0, 0, 0);

    const diffTime = Math.abs(today.getTime() - pastDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)); // Convert milliseconds to days

    if (diffDays === 0) {
      return "today";
    } else if (today.getTime() > pastDate.getTime()) {
      return `${diffDays} day${diffDays === 1 ? '' : 's'} ago`;
    } else {
      // If the pastDate is actually in the future relative to today
      return `in ${diffDays} day${diffDays === 1 ? '' : 's'}`;
    }
  }

  // Demo banner gradient if no banner is provided
  const demoBanner = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
  const bannerStyle = jobs.banner 
    ? { backgroundImage: `url(${jobs.banner})`, backgroundSize: 'cover', backgroundPosition: 'center' }
    : { background: demoBanner };


  return (
    <div className="">
      {/* Main Card */}
      <div className="relative bg-white dark:bg-gray-800 rounded-3xl border border-gray-200 dark:border-gray-700 shadow-lg hover:shadow-2xl transition-all duration-500 hover:border-gray-300 dark:hover:border-gray-600 hover:-translate-y-1">
        
        {/* Animated Background Gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 via-transparent to-purple-50/50 dark:from-blue-950/20 dark:via-transparent dark:to-purple-950/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>
        
        {/* Banner Header - Responsive height */}
        <div className="relative h-40 sm:h-48 md:h-52 rounded-t-3xl overflow-hidden" style={bannerStyle}>
          {/* Banner Overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent"></div>
          
         
         <div className='flex justify-between'>
           {/* Company Logo - Centered */}
            <div className="flex justify-start p-5 ">
              <div className="w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 bg-gradient-to-br from-gray-800 to-gray-900 dark:from-gray-100 dark:to-gray-200 rounded-lg sm:rounded-xl flex items-center justify-center shadow-lg ring-2 sm:ring-4 ring-white/30 backdrop-blur-sm">
                <span className="text-white dark:text-gray-900 text-base sm:text-lg md:text-xl font-bold uppercase">
                  {jobs.company.name[0]}
                </span>
              </div>
            </div>
             {/* Top Actions on Banner */}
          <div className="absolute top-3 right-3 sm:top-4 sm:right-4 flex items-center space-x-2">
            <span className="inline-flex items-center px-2 py-1 sm:px-3 sm:py-1 rounded-full text-xs font-medium bg-white/20 backdrop-blur-sm text-white border border-white/30">
              {jobs.openings} opening{jobs.openings !== 1 ? 's' : ''}
            </span>
            <button className="w-7 h-7 sm:w-8 sm:h-8 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white/30 transition-colors duration-200 border border-white/30">
              <Bookmark className="w-3.5 h-3.5 sm:w-4 sm:h-4 text-white" />
            </button>
          </div>
         </div>

          {/* Company Info in Banner - Stacked Layout */}
          <div className="absolute bottom-3 left-3 right-3 sm:bottom-4 sm:left-4 sm:right-4">
            

            {/* Company Name and Posted Date - Side by side */}
            <div className="flex items-center justify-between">
              {/* Company Name */}
              <p className="text-white text-sm sm:text-base md:text-lg font-semibold drop-shadow-lg truncate flex-1 mr-3">
                {jobs.company.name}
              </p>
              
              {/* Posted Date */}
              <span className="px-2 py-1 sm:px-3 sm:py-1.5 bg-white/20 backdrop-blur-sm text-white text-xs sm:text-sm font-medium rounded-full border border-white/30 flex-shrink-0">
                {jobs.postedAt && getDaysAgo(new Date(jobs.postedAt))}
              </span>
            </div>
          </div>
        </div>

        

        {/* Content */}
        <div className="relative p-4 sm:p-6">
          {/* Job Title */}
          <h2 className="text-lg sm:text-xl md:text-2xl font-bold text-gray-900 dark:text-white leading-snug mb-3 sm:mb-4 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">
            {jobs.title}
          </h2>

          {/* Tags */}
          <div className="flex gap-1.5 sm:gap-2 flex-wrap mb-4 sm:mb-6">
            <span className="px-2 py-1 sm:px-3 sm:py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 text-xs font-medium rounded-full">
              {jobs.category}
            </span>
            <span className="px-2 py-1 sm:px-3 sm:py-1 bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300 text-xs font-medium rounded-full">
              {jobs.mode}
            </span>
            <span className="px-2 py-1 sm:px-3 sm:py-1 bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-300 text-xs font-medium rounded-full">
              {jobs.workPlaceType}
            </span>
          </div>

          {/* Job Details */}
          <div className="mb-4 sm:mb-6">
            <div className="flex flex-col sm:flex-row sm:flex-wrap sm:items-center gap-2 sm:gap-4 text-sm">
              {/* Location */}
              <div className="flex items-center space-x-2">
                <MapPin className="w-4 h-4 text-gray-500 dark:text-gray-400 flex-shrink-0" />
                <span className="text-gray-700 dark:text-gray-300 font-medium truncate">
                  {jobs.location.city}, {jobs.location.state}
                </span>
              </div>
              
              {/* Work Hours */}
              {jobs.workingHours && (
                <div className="flex items-center space-x-2">
                  <Calendar className="w-4 h-4 text-gray-500 dark:text-gray-400 flex-shrink-0" />
                  <span className="text-gray-700 dark:text-gray-300 font-medium">
                    {jobs.workingHours}
                  </span>
                </div>
              )}
              
              {/* Salary Range */}
              {jobs.salaryRange && (
                <div className="flex items-center space-x-2">
                  <Building2 className="w-4 h-4 text-gray-500 dark:text-gray-400 flex-shrink-0" />
                  <span className="text-gray-700 dark:text-gray-300 font-medium">
                    {jobs.salaryRange}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Skills */}
          {jobs.skills && jobs.skills.length > 0 && (
            <div className="mb-4 sm:mb-6">
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">Required Skills:</p>
              <p className="text-sm font-semibold text-gray-900 dark:text-white">
                {jobs.skills.slice(0, 3).map((skill : any) => skill.skill).join(', ')}
                {jobs.skills.length > 3 && ` +${jobs.skills.length - 3} more`}
              </p>
            </div>
          )}

          {/* Footer Action */}
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3 sm:gap-0 pt-4 border-t border-gray-100 dark:border-gray-800">
            <div className="text-sm text-gray-500 dark:text-gray-400">
              Deadline: {new Date(jobs.applicationDeadline).toLocaleDateString()}
            </div>
            <Link href={`/dashboard/opportunities/jobs/${jobs.slug}`}>
              <button className="group/btn flex items-center justify-center sm:justify-start space-x-1.5 text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium text-sm transition-colors duration-200 w-full sm:w-auto py-2 sm:py-0">
                <span>Apply Now</span>
                <ArrowRight className="w-4 h-4 group-hover/btn:translate-x-0.5 transition-transform duration-200" />
              </button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  
  );
};

export default JobCard;