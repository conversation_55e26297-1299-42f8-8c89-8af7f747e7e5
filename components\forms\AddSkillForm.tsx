'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '../ui/form';
import { Input } from '../ui/input';
import { skillSchema } from '@/schemas/userSchema';
import { useCreateSkillMutation } from '@/store/features/userApi';
import { Button } from '../ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select';
const AddSkillForm: React.FC = () => {
  const [addSkill, { isSuccess, isError, error, isLoading }] =
    useCreateSkillMutation();
  const form = useForm<z.infer<typeof skillSchema>>({
    resolver: zodResolver(skillSchema),
    defaultValues: {
      name: '',
      level: '',
      monthsOfExperience: 0,
    },
  });

  const onSubmit = async (data: z.infer<typeof skillSchema>) => {
    const response = await addSkill(data).unwrap();
  };

  useEffect(() => {
    if (isSuccess) {
      toast.success('Skill updated successfully');
    }
    if (isError && error && 'data' in error) {
      toast.error(
        (error.data as any)?.message || 'Failed to Update Skill Details'
      );
    }
  }, [isSuccess, isError, error]);

  return (
    <>
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="flex w-full flex-col items-center space-y-5"
        >
          <div className="grid w-full grid-cols-1 gap-5">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter Name" {...field} />
                  </FormControl>

                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="level"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Marital Status</FormLabel>
                  <FormControl>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <SelectTrigger className="w-full flex-1">
                        <SelectValue placeholder="Select Status" />
                      </SelectTrigger>
                      <SelectContent position="popper">
                        <SelectItem value="beginner">Beginner</SelectItem>
                        <SelectItem value="intermediate">
                          Intermediate
                        </SelectItem>
                        <SelectItem value="advanced">Advanced</SelectItem>
                        <SelectItem value="expert">Expert</SelectItem>
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>{' '}
          <FormField
            control={form.control}
            name="monthsOfExperience"
            render={({ field }) => (
              <FormItem className="w-full flex-1">
                <FormLabel>Months of Experience</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    inputMode="numeric"
                    placeholder="Enter Months of Experience"
                    value={field.value} // Ensure the value is set correctly
                    onChange={e =>
                      field.onChange(
                        e.target.value ? parseFloat(e.target.value) : null
                      )
                    }
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <Button type="submit" disabled={isLoading} className="w-fit">
            {isLoading ? (
              <div className="flex items-center justify-center gap-2">
                <div className="h-3 w-3 animate-spin rounded-full border-2 border-white/30 border-t-white"></div>
                <span>Updating Skill</span>
              </div>
            ) : (
              <span>Update Skill</span>
            )}
          </Button>
        </form>
      </Form>
    </>
  );
};

export default AddSkillForm;
