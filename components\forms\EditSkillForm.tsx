'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '../ui/form';
import { Input } from '../ui/input';
import { skillSchema } from '@/schemas/userSchema';
import { useUpdateSkillMutation } from '@/store/features/userApi';
import { Button } from '../ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select';

import { ISkill } from '@/types/IUser';

const EditSkillForm: React.FC<{ skill: ISkill }> = ({ skill }) => {
  const [updateSkill, { isSuccess, isError, error, isLoading }] =
    useUpdateSkillMutation();
  const form = useForm<z.infer<typeof skillSchema>>({
    resolver: zodResolver(skillSchema),
    defaultValues: {
      name: skill?.name || '',
      level: skill?.level || '',
      monthsOfExperience: skill?.monthsOfExperience || 0,
    },
  });

  const onSubmit = async (data: z.infer<typeof skillSchema>) => {
    if (!skill?._id) return;
    const updatedSkill: ISkill = {
      ...skill,
      name: data.name,
      level: data.level,
      monthsOfExperience: data.monthsOfExperience,
    };

    const response = await updateSkill(updatedSkill).unwrap();
  };

  useEffect(() => {
    if (isSuccess) {
      toast.success('Skill updated successfully');
    }
    if (isError && error && 'data' in error) {
      toast.error(
        (error.data as any)?.message || 'Failed to Update Skill Details'
      );
    }
  }, [isSuccess, isError, error]);

  return (
    <>
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="flex w-full flex-col items-center space-y-5"
        >
          <div className="grid w-full grid-cols-1 gap-5">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter Name" {...field} />
                  </FormControl>

                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter Name" {...field} />
                  </FormControl>

                  <FormMessage />
                </FormItem>
              )}
            />
          </div>{' '}
          <FormField
            control={form.control}
            name="monthsOfExperience"
            render={({ field }) => (
              <FormItem className="w-full flex-1">
                <FormLabel>Months of Experience</FormLabel>
                <FormControl>
                  <Input placeholder="Enter Months of Experience" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <Button type="submit" disabled={isLoading} className="w-fit">
            {isLoading ? (
              <span>Updating Skill</span>
            ) : (
              <span>Update Skill</span>
            )}
          </Button>
        </form>
      </Form>
    </>
  );
};

export default EditSkillForm;
