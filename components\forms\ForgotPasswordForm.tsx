'use client';

import { forgetPasswordSchema } from '@/schemas/authSchema';
import { useForgetPasswordMutation } from '@/store/features/authApi';
import { zodResolver } from '@hookform/resolvers/zod';
import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '../ui/form';
import { Input } from '../ui/input';

const ForgotPasswordForm: React.FC = () => {
  const [isEmailSent, setEmailSet] = useState(false);
  const [forgetPassword, { isSuccess, isError, error, isLoading }] =
    useForgetPasswordMutation();
  const form = useForm<z.infer<typeof forgetPasswordSchema>>({
    resolver: zodResolver(forgetPasswordSchema),
    defaultValues: {
      email: '',
    },
  });

  const onSubmit = async (data: z.infer<typeof forgetPasswordSchema>) => {
    const response = await forgetPassword(data);
    if (response.data?.success) {
      setEmailSet(true);
    }
  };

  useEffect(() => {
    if (isSuccess) {
      toast.success('Reset Password Email Successfully Sent');
    }
    if (isError && error && 'data' in error) {
      toast.error((error.data as any)?.message || 'Failed to sent mail');
    }
  }, [isSuccess, isError, error]);

  return (
    <>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Enter your email address
  "
                    {...field}
                  />
                </FormControl>

                <FormMessage />
              </FormItem>
            )}
          />

          <button
            type="submit"
            disabled={isLoading || isEmailSent}
            className="relative w-full cursor-pointer rounded-sm bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 px-4 py-2.5 text-sm font-semibold text-white transition-all duration-300 hover:scale-[1.01] hover:from-blue-600 hover:via-purple-600 hover:to-pink-600 hover:shadow-lg hover:shadow-blue-500/25 disabled:cursor-not-allowed disabled:opacity-50 dark:hover:shadow-blue-500/10"
          >
            {isEmailSent ? (
              <div className="flex items-center justify-center gap-2">
                <span>Email Sent</span>
              </div>
            ) : isLoading ? (
              <span>Sending email...</span>
            ) : (
              <span>Send email</span>
            )}
          </button>
        </form>
      </Form>
    </>
  );
};

export default ForgotPasswordForm;
