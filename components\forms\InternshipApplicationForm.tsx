'use client';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '../ui/form';
import { Input } from '../ui/input';
import { Textarea } from '../ui/textarea';
import { Button } from '../ui/button';
import { toast } from 'sonner';
import { useCreateInternshipApplicationMutation } from '@/store/internshipApplication-feature/applicationApi';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Checkbox } from '../ui/checkbox';
import { RadioGroup, RadioGroupItem } from '../ui/radio-group';
import React from 'react';
import { useAppSelector } from '@/store';

interface InternshipQuestion {
  questionId: number;
  question: string;
  questionType: 'text' | 'textarea' | 'dropdown' | 'radio' | 'checkbox';
  options?: string[];
  isRequired?: boolean;
  hint?: string;
}

interface InternshipApplicationFormProps {
  internshipId: string;
  questions?: InternshipQuestion[];
  onSuccess: () => void;
}

const generateSchemaFromQuestions = (questions: InternshipQuestion[]) => {
  const shape: Record<string, z.ZodTypeAny> = {};

  // Add cover letter field (always required)
  shape.coverLetter = z.string().min(50, 'Cover letter must be at least 50 characters');

  // Add dynamic questions
  questions.forEach(q => {
    const key = `q_${q.questionId}`;
    const base = q.isRequired ? z.any().refine(val => val !== undefined && val !== '', { message: 'Required' }) : z.any();

    switch (q.questionType) {
      case 'text':
      case 'textarea':
      case 'dropdown':
      case 'radio':
        shape[key] = base.pipe(z.string());
        break;
      case 'checkbox':
        shape[key] = base.pipe(z.array(z.string()));
        break;
    }
  });

  return z.object(shape);
};

const InternshipApplicationForm: React.FC<InternshipApplicationFormProps> = ({ 
  internshipId, 
  questions = [], 
  onSuccess 
}) => {
  const user = useAppSelector(state => state.auth.user);
  const [createApplication, { isLoading }] = useCreateInternshipApplicationMutation();
  
  const schema = generateSchemaFromQuestions(questions);
  const form = useForm({
    resolver: zodResolver(schema),
    defaultValues: {
      coverLetter: '',
      ...Object.fromEntries(questions.map(q => [`q_${q.questionId}`, '']))
    },
  });

  const onSubmit = async (values: Record<string, any>) => {
    if (!user?._id) {
      toast.error('Please log in to apply for internships');
      return;
    }

    // Prepare answers array
    const answers = questions.map(q => ({
      questionId: q.questionId,
      answer: values[`q_${q.questionId}`],
    }));

    try {
      await createApplication({
        internshipId,
        applicantId: user._id,
        coverLetter: values.coverLetter,
        answers,
        status: 'applied' as const
      }).unwrap();
      
      toast.success('Application submitted successfully!');
      onSuccess();
    } catch (error: any) {
      toast.error(error?.data?.message || 'Application submission failed');
    }
  };

  // Show login prompt if user is not authenticated
  if (!user?._id) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-600 mb-4">Please log in to apply for this internship.</p>
        <Button
          onClick={() => window.location.href = '/auth/login'}
          className="bg-blue-600 hover:bg-blue-700"
        >
          Log In
        </Button>
      </div>
    );
  }

  return (
    <div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Cover Letter */}
          <FormField
            control={form.control}
            name="coverLetter"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Cover Letter <span className="text-red-500">*</span></FormLabel>
                <FormControl>
                  <Textarea 
                    {...field} 
                    rows={6}
                    placeholder="Tell us why you're interested in this internship and what makes you a great candidate..."
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Dynamic Questions */}
          {questions.map(q => (
            <FormField
              key={q.questionId}
              control={form.control}
              name={`q_${q.questionId}`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {q.question}
                    {q.isRequired && <span className="text-red-500 ml-1">*</span>}
                  </FormLabel>
                  {q.hint && (
                    <p className="text-sm text-gray-500 mb-2">{q.hint}</p>
                  )}
                  <FormControl>
                    <div>
                      {q.questionType === 'text' && <Input {...field} />}
                      {q.questionType === 'textarea' && <Textarea {...field} />}
                      {q.questionType === 'dropdown' && (
                        <Select value={field.value} onValueChange={field.onChange}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select an option" />
                          </SelectTrigger>
                          <SelectContent>
                            {q.options?.map(option => (
                              <SelectItem key={option} value={option}>
                                {option}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      )}
                      {q.questionType === 'radio' && (
                        <RadioGroup value={field.value} onValueChange={field.onChange}>
                          {q.options?.map(option => (
                            <div key={option} className="flex items-center space-x-2">
                              <RadioGroupItem value={option} id={option} />
                              <label htmlFor={option}>{option}</label>
                            </div>
                          ))}
                        </RadioGroup>
                      )}
                      {q.questionType === 'checkbox' && (
                        <div className="space-y-2">
                          {q.options?.map(option => (
                            <div key={option} className="flex items-center space-x-2">
                              <Checkbox
                                checked={field.value?.includes(option)}
                                onCheckedChange={(checked) => {
                                  const currentValue = field.value || [];
                                  if (checked) {
                                    field.onChange([...currentValue, option]);
                                  } else {
                                    field.onChange(currentValue.filter((v: string) => v !== option));
                                  }
                                }}
                              />
                              <label>{option}</label>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          ))}

          <div className="flex justify-end space-x-4 pt-4">
            <Button
              type="submit"
              disabled={isLoading}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isLoading ? 'Submitting...' : 'Submit Application'}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default InternshipApplicationForm;
