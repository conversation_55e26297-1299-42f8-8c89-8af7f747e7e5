'use client';

import { resetPasswordSchema } from '@/schemas/authSchema';
import { useResetPasswordMutation } from '@/store/features/authApi';
import { zodResolver } from '@hookform/resolvers/zod';
import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';
import { useRouter, useSearchParams } from 'next/navigation';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '../ui/form';
import { Input } from '../ui/input';

const ResetPasswordForm: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const token = searchParams.get('token');

  const [resetPassword, { isSuccess, isError, error, isLoading }] =
    useResetPasswordMutation();

  useEffect(() => {
    if (token == null) {
      router.push('/forgot-password');
    }
  }, [token]);

  const form = useForm<z.infer<typeof resetPasswordSchema>>({
    resolver: zodResolver(resetPasswordSchema),
    defaultValues: {
      token: token || '',
      password: '',
      confirmPassword: '',
    },
  });

  const onSubmit = async (data: z.infer<typeof resetPasswordSchema>) => {
    const response = await resetPassword(data);
    if (response.data?.success) {
      router.push('/sign-in');
    }
  };

  useEffect(() => {
    if (isSuccess) {
      toast.success('Password Successfull Reset');
    }
    if (isError && error && 'data' in error) {
      toast.error((error.data as any)?.message || 'Failed to reset password');
    }
  }, [isSuccess, isError, error]);

  return (
    <>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel>New Password</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Enter Password
  "
                    {...field}
                  />
                </FormControl>

                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="confirmPassword"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Confrim Password</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Enter Password
  "
                    {...field}
                  />
                </FormControl>

                <FormMessage />
              </FormItem>
            )}
          />
          <button
            type="submit"
            disabled={isLoading}
            className="relative w-full cursor-pointer rounded-sm bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 px-4 py-2.5 text-sm font-semibold text-white transition-all duration-300 hover:scale-[1.01] hover:from-blue-600 hover:via-purple-600 hover:to-pink-600 hover:shadow-lg hover:shadow-blue-500/25 disabled:cursor-not-allowed disabled:opacity-50 dark:hover:shadow-blue-500/10"
          >
            {isLoading ? (
              <div className="flex items-center justify-center gap-2">
                <div className="h-3 w-3 animate-spin rounded-full border-2 border-white/30 border-t-white"></div>
                <span>Reseting your password...</span>
              </div>
            ) : (
              <span>Reset Password</span>
            )}
          </button>
        </form>
      </Form>
    </>
  );
};

export default ResetPasswordForm;
