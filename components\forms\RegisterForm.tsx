'use client';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '../ui/form';
import { Input } from '../ui/input';
import { Textarea } from '../ui/textarea';
import { Button } from '../ui/button';
import { toast } from 'sonner';
import { useCreateEventRegistrationMutation } from '@/store/features/registrations/registrationApi';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Checkbox } from '../ui/checkbox';
import { RadioGroup, RadioGroupItem } from '../ui/radio-group';
import React from 'react';
import { IEventQuestion } from "@/types/IEvents";

interface EventRegistrationFormProps {
  eventId: string;
  questions: IEventQuestion[];
  setIsRegistered: (value: boolean) => void;
}

const generateSchemaFromQuestions = (questions: IEventQuestion[]) => {
  const shape: Record<string, z.ZodTypeAny> = {};

  questions.forEach(q => {
    const key = `q_${q.questionId}`;
    const base = q.isRequired ? z.any().refine(val => val !== undefined && val !== '', { message: 'Required' }) : z.any();

    switch (q.questionType) {
      case 'text':
      case 'textarea':
      case 'dropdown':
      case 'radio':
        shape[key] = base.pipe(z.string());
        break;
      case 'checkbox':
        shape[key] = base.pipe(z.array(z.string()));
        break;
    }
  });

  return z.object(shape);
};

const EventRegistrationForm: React.FC<EventRegistrationFormProps> = ({ eventId, questions, setIsRegistered }) => {
  const schema = generateSchemaFromQuestions(questions);
  const form = useForm({
    resolver: zodResolver(schema),
    defaultValues: {},
  });

  const [createRegistration, { isLoading, error, data }] = useCreateEventRegistrationMutation();

  const onSubmit = async (values: Record<string, any>) => {
    const answers = questions.map(q => ({
      questionId: q.questionId,
      answer: values[`q_${q.questionId}`],
    }));

    try {
      await createRegistration({
        event: eventId,
        paymentStatus: 'Pending',
        answers: answers,
      }).unwrap();
      toast.success('Registered successfully!');
      setIsRegistered(true);
    } catch (error: any) {
      toast.error(error?.data?.message || 'Registration failed');
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {questions.map(q => (
          <FormField
            key={q.questionId}
            control={form.control}
            name={`q_${q.questionId}`}
            render={({ field }) => (
              <FormItem>
                <FormLabel>{q.question}</FormLabel>
                <FormControl>
                  <div>
                    {q.questionType === 'text' && <Input {...field} />}
                    {q.questionType === 'textarea' && <Textarea {...field} />}
                    {q.questionType === 'dropdown' && (
                      <Select value={field.value} onValueChange={field.onChange}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select an option" />
                        </SelectTrigger>
                        <SelectContent>
                          {q.options?.map(option => (
                            <SelectItem key={option} value={option}>
                              {option}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    )}
                    {q.questionType === 'radio' && (
                      <RadioGroup onValueChange={field.onChange} value={field.value}>
                        {q.options?.map(option => (
                          <div key={option} className="flex items-center space-x-2">
                            <RadioGroupItem value={option} id={option} />
                            <label htmlFor={option}>{option}</label>
                          </div>
                        ))}
                      </RadioGroup>
                    )}
                    {q.questionType === 'checkbox' && (
                      <div className="flex flex-col gap-2">
                        {q.options?.map(option => (
                          <div key={option} className="flex items-center space-x-2">
                            <Checkbox
                              checked={field.value?.includes(option)}
                              onCheckedChange={checked =>
                                checked
                                  ? field.onChange([...(field.value || []), option])
                                  : field.onChange(field.value?.filter((val: string) => val !== option))
                              }
                            />
                            <label>{option}</label>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        ))}
        <Button type="submit" disabled={isLoading}>
          {isLoading ? 'Registering...' : 'Register'}
        </Button>
      </form>
    </Form>
  );
};

export default EventRegistrationForm;
