'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { toast } from 'sonner';

import { loginSchema } from '@/schemas/authSchema';
import { useLoginMutation } from '@/store/features/authApi';
import { useAppSelector } from '@/store';

import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '../ui/form';
import { Input } from '../ui/input';
import Link from 'next/link';

const SignInForm: React.FC = () => {
  const router = useRouter();
  const isLogin = useAppSelector(state => state.auth.isLogin);

  const [loginUser, { isSuccess, isError, error, isLoading }] =
    useLoginMutation();

  const form = useForm<z.infer<typeof loginSchema>>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  const onSubmit = async (data: z.infer<typeof loginSchema>) => {
    const response = await loginUser(data);
    if (response.data?.success) {
      router.push('/dashboard');
    }
  };

  useEffect(() => {
    if (isLogin) {
      router.push('/dashboard');
    }
  }, [isLogin, router]);

  useEffect(() => {
    if (isSuccess) {
      toast.success('Successfully logged in');
    }
    if (isError && error && 'data' in error) {
      toast.error((error.data as any)?.message || 'Login failed');
    }
  }, [isSuccess, isError, error]);

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input
                  placeholder="Enter your email address
"
                  {...field}
                />
              </FormControl>

              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Password</FormLabel>
              <FormControl>
                <Input
                  placeholder="Enter your password"
                  type="password"
                  {...field}
                />
              </FormControl>
              <FormDescription className="flex justify-end">
                {' '}
                <Link
                  href="/forgot-password"
                  className="text-blue-600 transition-colors hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300"
                >
                  {' '}
                  Forgot Password
                </Link>
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        <button
          type="submit"
          disabled={isLoading}
          className="relative w-full cursor-pointer rounded-sm bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 px-4 py-2.5 text-sm font-semibold text-white transition-all duration-300 hover:scale-[1.01] hover:from-blue-600 hover:via-purple-600 hover:to-pink-600 hover:shadow-lg hover:shadow-blue-500/25 disabled:cursor-not-allowed disabled:opacity-50 dark:hover:shadow-blue-500/10"
        >
          {isLoading ? (
            <div className="flex items-center justify-center gap-2">
              <div className="h-3 w-3 animate-spin rounded-full border-2 border-white/30 border-t-white"></div>
              <span>Signing in...</span>
            </div>
          ) : (
            <span>Sign In</span>
          )}
        </button>
      </form>
    </Form>
  );
};

export default SignInForm;
