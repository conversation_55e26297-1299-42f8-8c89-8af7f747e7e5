'use client';

import React, { useState } from 'react';
import { ArrowRight, CheckCircle, Briefcase } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import JobApplicationForm from '@/components/forms/JobApplicationForm';

interface JobQuestion {
  questionId: number;
  question: string;
  questionType: 'text' | 'textarea' | 'dropdown' | 'radio' | 'checkbox';
  options?: string[];
  isRequired?: boolean;
  hint?: string;
}

interface JobApplyButtonProps {
  jobId: string;
  jobTitle: string;
  questions?: JobQuestion[];
  className?: string;
}

const JobApplyButton: React.FC<JobApplyButtonProps> = ({ 
  jobId, 
  jobTitle, 
  questions = [], 
  className = "" 
}) => {
  const [isApplicationSubmitted, setIsApplicationSubmitted] = useState(false);

  if (isApplicationSubmitted) {
    return (
      <button 
        className={`w-full bg-green-600 text-white font-medium py-4 px-6 rounded-xl transition-all duration-200 flex items-center justify-center space-x-2 ${className}`}
        disabled
      >
        <CheckCircle className="w-4 h-4" />
        <span>Application Submitted</span>
      </button>
    );
  }

  return (
    <Dialog>
      <DialogTrigger asChild>
        <button className={`w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-4 px-6 rounded-xl transition-all duration-200 flex items-center justify-center space-x-2 group ${className}`}>
          <Briefcase className="w-4 h-4" />
          <span>Apply Now</span>
          <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
        </button>
      </DialogTrigger>

      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto overflow-x-hidden p-0">
        <div className="p-6">
          <DialogHeader>
            <DialogTitle>Apply for {jobTitle}</DialogTitle>
          </DialogHeader>
          <div className="mt-6">
            <JobApplicationForm
              jobId={jobId}
              questions={questions}
              onSuccess={() => setIsApplicationSubmitted(true)}
            />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default JobApplyButton;
