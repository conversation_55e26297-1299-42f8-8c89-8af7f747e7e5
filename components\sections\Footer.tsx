'use client';

import Link from 'next/link';
import React from 'react';
import {
  LinkedInIcon,
  InstagramIcon,
  BoltIcon,
} from '../../public/icons/socialMediaIcons';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import {
  aboutMenu,
  companyCategoryMenu,
  eventSubMenu,
  legalPageMenu,
  letConnectMenu,
  opportunitiesMenu,
  resourcesMenu,
  skillCertifications,
} from '@/constant/footerItems';

type Item = {
  label: string;
  href: string;
};

interface ItemLinksProps {
  items: Item[];
  className?: string; 
}

const ItemsLink = ({ items, className }: ItemLinksProps) => {
  const pathname = usePathname();

  return (
    <ul className={cn('font-secondary space-y-1 text-xs', className)}>
      {items.map(({ label, href }) => {
        const isActive = pathname === href;

        return (
          <li key={href}>
            <Link
              href={href}
              className={cn(
                'font-primary block text-sm text-gray-700 transition-all duration-200 hover:translate-x-1 hover:text-indigo-600 dark:text-gray-400 dark:hover:text-orange-600',
                isActive
                  ? 'font-medium text-indigo-600 dark:text-orange-600'
                  : ''
              )}
            >
              <span>{label}</span>
            </Link>
          </li>
        );
      })}
    </ul>
  );
};

const Footer = () => {
  return (
    <footer className="w-full border-t bg-blue-50 dark:bg-[#0a0e17]">
      <div className="mx-auto max-w-7xl px-4 pt-16 pb-8 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 gap-8 md:grid-cols-12 lg:gap-12">
          <div className="space-y-6 md:col-span-5">
            <div className="space-y-4">
              <div className="space-y-3">
                <h3 className="font-primary text-xl font-bold text-indigo-700 dark:text-orange-600">
                  Career Portal
                </h3>
                <p className="font-secondary max-w-sm leading-relaxed text-gray-700 dark:text-gray-300">
                  Empowering talent. Creating opportunities.
                  <br />
                  Shaping the future.
                </p>
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="font-primary text-sm font-semibold tracking-wide text-indigo-600 uppercase dark:text-gray-100">
                Connect With Us
              </h4>
              <p className="font-primary max-w-sm text-base leading-relaxed text-gray-700 dark:text-gray-300">
                Follow our journey on social media for the latest updates and
                opportunities.
              </p>

              <div className="flex flex-wrap items-center gap-4 pt-2">
                <Link
                  href="/contact"
                  className="font-primary inline-flex transform items-center rounded-lg bg-gradient-to-r from-blue-600 to-indigo-600 px-6 py-2.5 text-sm font-medium text-white shadow-md transition-all duration-200 hover:-translate-y-0.5 hover:from-blue-700 hover:to-indigo-700 hover:shadow-lg"
                >
                  Contact Us
                </Link>
                <div className="flex items-center space-x-3">
                  <Link
                    href="https://www.linkedin.com/company/sudha-software-solutions-private-limited/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="group flex h-10 w-10 items-center justify-center rounded-full border border-gray-200 bg-white shadow-sm transition-all duration-200 hover:-translate-y-0.5 hover:border-blue-300 hover:shadow-md"
                    title="LinkedIn"
                  >
                    <LinkedInIcon className="h-4 w-4 text-[#0077b5] transition-transform duration-200 group-hover:scale-110" />
                  </Link>

                  <Link
                    href="https://www.instagram.com/sudha_software_solutions/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="group flex h-10 w-10 items-center justify-center rounded-full border border-gray-200 bg-white shadow-sm transition-all duration-200 hover:-translate-y-0.5 hover:border-pink-300 hover:shadow-md"
                    title="Instagram"
                  >
                    <InstagramIcon className="h-4 w-4 text-[#ff34a7] transition-transform duration-200 group-hover:scale-110" />
                  </Link>
                </div>
              </div>
            </div>
          </div>

          <div className="md:col-span-7">
            <div className="grid grid-cols-2 gap-8 sm:grid-cols-4">
              <div className="flex flex-col space-y-4">
                <Link
                  href="/opportunities"
                  className={cn(
                    'block border-b border-blue-50 pb-2 text-sm font-semibold tracking-wide text-gray-900 uppercase transition-all duration-200 hover:translate-x-1 hover:text-indigo-600 dark:border-gray-300 dark:text-gray-300 dark:hover:text-orange-600'
                  )}
                >
                  Opportunities
                </Link>
                <ItemsLink className="space-y-3" items={opportunitiesMenu} />
              </div>

              {/* <div className="flex flex-col space-y-4">
                <Link
                  href="/professional-certifications"
                  className={cn(
                    'block border-b border-blue-50 pb-2 text-sm font-semibold tracking-wide text-gray-900 uppercase transition-all duration-200 hover:translate-x-1 hover:text-indigo-600 dark:border-gray-300 dark:text-gray-300 dark:hover:text-orange-600'
                  )}
                >
                  Certifications
                </Link>
                <ItemsLink className="space-y-3" items={skillCertifications} />
              </div> */}

              <div className="flex flex-col space-y-4">
                <Link
                  href="/events-and-webinars"
                  className={cn(
                    'block border-b border-blue-50 pb-2 text-sm font-semibold tracking-wide text-gray-900 uppercase transition-all duration-200 hover:translate-x-1 hover:text-indigo-600 dark:border-gray-300 dark:text-gray-300 dark:hover:text-orange-600'
                  )}
                >
                  Events
                </Link>
                <ItemsLink className="space-y-3" items={eventSubMenu} />
              </div>
              <div className="flex flex-col space-y-4">
                <h4 className="border-b border-blue-50 pb-2 text-sm font-semibold tracking-wide text-gray-900 uppercase dark:border-gray-300 dark:text-gray-300">
                  Lets Connect
                </h4>
                <ItemsLink className="space-y-3" items={letConnectMenu} />
              </div>
              <div className="flex flex-col space-y-4">
                <Link
                  href="/companies"
                  className={cn(
                    'block border-b border-blue-50 pb-2 text-sm font-semibold tracking-wide text-gray-900 uppercase transition-all duration-200 hover:translate-x-1 hover:text-indigo-600 dark:border-gray-300 dark:text-gray-300 dark:hover:text-orange-600'
                  )}
                >
                  Companies
                </Link>
                <ItemsLink className="space-y-3" items={companyCategoryMenu} />
              </div>
              <div className="flex flex-col space-y-4">
                <Link
                  href="/about"
                  className={cn(
                    'block border-b border-blue-50 pb-2 text-sm font-semibold tracking-wide text-gray-900 uppercase transition-all duration-200 hover:translate-x-1 hover:text-indigo-600 dark:border-gray-300 dark:text-gray-300 dark:hover:text-orange-600'
                  )}
                >
                  About Us
                </Link>
                <ItemsLink className="space-y-3" items={aboutMenu} />
              </div>
              <div className="flex flex-col space-y-4">
                <Link
                  href="/resources"
                  className={cn(
                    'block border-b border-blue-50 pb-2 text-sm font-semibold tracking-wide text-gray-900 uppercase transition-all duration-200 hover:translate-x-1 hover:text-indigo-600 dark:border-gray-300 dark:text-gray-300 dark:hover:text-orange-600'
                  )}
                >
                  Resources
                </Link>
                <ItemsLink className="space-y-3" items={resourcesMenu} />
              </div>

              <div className="space-y-4">
                <h4 className="border-b border-blue-50 pb-2 text-sm font-semibold tracking-wide text-gray-900 uppercase dark:border-gray-300 dark:text-gray-300">
                  Legal
                </h4>
                <ItemsLink className="space-y-3" items={legalPageMenu} />
              </div>
            </div>
          </div>
        </div>

        <div className="mt-12 border-t border-blue-50 pt-8 dark:border-gray-300/70">
          <div className="flex flex-col items-center justify-between space-y-4 md:flex-row md:space-y-0">
            <div className="flex items-center space-x-4">
              <div className="flex min-h-8 min-w-8 items-center justify-center rounded-lg bg-gradient-to-br from-blue-500 to-indigo-600">
                <BoltIcon className="h-4 w-4 text-white" />
              </div>
              <span className="text-sm text-gray-600">
                Copyright © 2025{' '}
                <span className="font-semibold text-gray-900 dark:text-gray-400">
                  Career Portal by Sudha Software Solutions Private Limited
                </span>
                . All rights reserved.
              </span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
