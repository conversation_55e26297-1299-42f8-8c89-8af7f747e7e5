import { Phone, Mail } from 'lucide-react';
import React from 'react';
import {
  LinkedInIcon,
  InstagramIcon,
} from '../../public/icons/socialMediaIcons';
import Link from 'next/link';

const Header = () => {
  const emailAddress = '<EMAIL>';

  return (
    <div className="border-b bg-gray-50 dark:bg-slate-900">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="hidden h-12 items-center justify-between lg:flex">
          <div className="flex items-center space-x-6">
            <Link
              href={`mailto:${emailAddress}`}
              className="group flex items-center space-x-2 rounded-lg px-4 py-2 text-sm font-medium text-gray-700 transition-all duration-200 ease-out hover:bg-indigo-50/80 hover:text-indigo-700 dark:text-gray-400 dark:hover:bg-transparent dark:hover:text-indigo-300"
            >
              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-indigo-100 transition-colors duration-200 group-hover:bg-indigo-200">
                <Mail className="h-4 w-4 text-indigo-600" />
              </div>
              <span className="font-secondary text-sm">{emailAddress}</span>
            </Link>

            <Link
              href="tel:+916204351245"
              className="group flex items-center space-x-2 rounded-lg px-4 py-2 text-sm font-medium text-gray-700 transition-all duration-200 ease-out hover:bg-indigo-50/80 hover:text-indigo-700 dark:text-gray-400 dark:hover:bg-transparent dark:hover:text-indigo-300"
            >
              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-green-100 transition-colors duration-200 group-hover:bg-indigo-200 dark:group-hover:text-indigo-200">
                <Phone className="h-4 w-4 text-indigo-600" />
              </div>
              <span className="font-primary text-sm">+91 6204351245</span>
            </Link>
          </div>

          <div className="flex items-center space-x-2">
            <span className="font-secondary mr-4 text-sm font-medium text-gray-700 dark:text-gray-400">
              Follow Us:
            </span>

            <Link
              href="https://www.linkedin.com/company/sudha-software-solutions-private-limited/"
              target="_blank"
              rel="noopener noreferrer"
              className="group flex h-8 w-8 items-center justify-center rounded-full border border-gray-200 bg-white shadow-sm transition-all duration-200 hover:-translate-y-0.5 hover:border-indigo-300 hover:shadow-md"
              title="LinkedIn"
            >
              <LinkedInIcon className="h-3.5 w-4 text-[#0077b5] transition-transform duration-200 group-hover:scale-110" />
            </Link>

            <Link
              href="https://www.instagram.com/sudha_software_solutions/"
              target="_blank"
              rel="noopener noreferrer"
              className="group flex h-8 w-8 items-center justify-center rounded-full border border-gray-200 bg-white shadow-sm transition-all duration-200 hover:-translate-y-0.5 hover:border-pink-300 hover:shadow-md"
              title="Instagram"
            >
              <InstagramIcon className="h-3.5 w-4 text-[#ff34a7] transition-transform duration-200 group-hover:scale-110" />
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Header;
