import React from 'react';
import { cn } from '@/lib/utils';

type HeadingProps = {
  children: React.ReactNode;
  className?: string;
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6'; // Specify allowed HTML heading tags
  level?: 1 | 2;
};

export const Heading: React.FC<HeadingProps> = ({
  children,
  className,
  as: Tag = 'h1',
  level = 1,
}) => {
  const levelStyles = {
    1: 'text-4xl md:text-5xl font-extrabold text-gray-900 dark:text-white',
    2: 'text-2xl md:text-3xl font-bold text-indigo-700 dark:text-indigo-300',
  };

  return (
    <Tag
      className={cn(
        'font-sans leading-tight tracking-tight drop-shadow-sm',
        levelStyles[level],
        className
      )}
    >
      {children}
    </Tag>
  );
};

export default Heading;