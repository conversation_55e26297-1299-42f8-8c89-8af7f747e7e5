'use client';
import Image from 'next/image';
import { ReactNode } from 'react';

interface HeroComponentProps {
  iconSrc?: string;
  iconAlt?: string;
  iconWidth?: number;
  iconHeight?: number;
  heading: ReactNode;
  description: ReactNode;
  heroImageSrc: string;
  heroImageAlt?: string;
  heroImageClassName?: string;
  heroImageWidth?: number;
  heroImageHeight?: number;
}

export const HeroComponent: React.FC<HeroComponentProps> = ({
  iconSrc = '/images/archery.png',
  iconAlt = 'Icon',
  iconWidth = 80,
  iconHeight = 80,
  heading,
  description,
  heroImageSrc,
  heroImageAlt = 'Hero image',
  heroImageWidth = 600,
  heroImageHeight = 500,
  heroImageClassName = '',
}) => {
  return (
    <section
      className={`relative w-full overflow-hidden bg-gray-50 py-16 md:py-24 lg:py-32 dark:bg-[#0a0e17]`}
    >
      <div className="pointer-events-none absolute top-0 right-0 z-0 hidden h-[400px] w-[400px] dark:block">
        <div
          className="h-full w-full"
          style={{
            background:
              'radial-gradient(circle at 80% 20%, rgba(37,99,235,0.35) 0%, transparent 70%)',
            filter: 'blur(8px)',
          }}
        />
      </div>
      <div className="dark:hidden absolute inset-0 opacity-30">
        <div className="absolute top-20 left-10 w-32 h-32 bg-blue-400 rounded-full blur-xl"></div>
        <div className="absolute bottom-20 right-10 w-24 h-24 bg-purple-400 rounded-full blur-xl"></div>
      </div>
      <div className="relative z-10 mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="grid min-h-[500px] grid-cols-1 items-center gap-8 lg:grid-cols-12 lg:gap-12">
          <div className="space-y-8 text-left lg:col-span-7">
            <div className="flex items-center">
              <div className="rounded-2xl border border-gray-100 bg-white p-3 shadow-lg">
                <Image
                  src={iconSrc}
                  width={iconWidth}
                  height={iconHeight}
                  className="h-8 w-8 md:h-12 md:w-12"
                  alt={iconAlt}
                />
              </div>
            </div>

            <div className="space-y-4">{heading}</div>

            <div className="space-y-6">{description}</div>
          </div>
          <div className="flex items-center justify-center lg:col-span-5 lg:justify-end">
            <div className="group relative">
              <div className="relative transform overflow-hidden rounded-3xl border border-gray-100 bg-white p-4 shadow-2xl transition-transform duration-300 ease-out group-hover:scale-105">
                <Image
                  src={heroImageSrc}
                  width={heroImageWidth}
                  height={heroImageHeight}
                  alt={heroImageAlt}
                  className={`h-auto w-full rounded-2xl object-cover ${heroImageClassName}`}
                  priority
                />
                <div className="pointer-events-none absolute inset-0 rounded-2xl bg-gradient-to-tr from-transparent via-transparent to-blue-500/5"></div>
              </div>
              <div
                className="absolute -top-4 -right-4 h-8 w-8 animate-bounce rounded-full bg-gradient-to-br from-yellow-400 to-orange-500 shadow-lg"
                style={{ animationDelay: '0.5s' }}
              ></div>
              <div className="absolute -bottom-2 -left-2 h-6 w-6 animate-pulse rounded-full bg-gradient-to-br from-green-400 to-blue-500 shadow-lg"></div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroComponent;
