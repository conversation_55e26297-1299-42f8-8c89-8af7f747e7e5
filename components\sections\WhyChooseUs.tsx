import Image from 'next/image';
import { ReactNode } from 'react';

export type FeatureItemProps = {
  icon: ReactNode;
  title: string;
  description: string;
  highlight?: boolean;
};

type WhyChooseUsProps = {
  heading: ReactNode;
  imageSrc: string;
  iconSrc: string;
  features: FeatureItemProps[];
  imagePosition?: 'left' | 'right';
};

const FeatureItem = ({
  icon,
  title,
  description,
  highlight = false,
}: FeatureItemProps) => {
  return (
    <div
      className={`group relative rounded-2xl p-6 transition-all duration-300 hover:scale-105 ${
        highlight
          ? 'border-2 border-blue-200 bg-gradient-to-br from-blue-50 to-indigo-50 shadow-lg'
          : 'border border-gray-200 dark:border-gray-700 bg-gray-100 dark:bg-gray-900 shadow-md hover:border-gray-300 hover:bg-gray-50 hover:shadow-lg'
      }`}
    >
      <div
        className={`mb-4 inline-flex h-12 w-12 items-center justify-center rounded-xl transition-transform duration-300 group-hover:scale-110 ${
          highlight
            ? 'bg-gradient-to-br from-blue-500 to-indigo-600 text-white shadow-lg'
            : 'bg-gradient-to-br from-gray-100 to-gray-200 text-gray-600 group-hover:from-blue-100 group-hover:to-indigo-100 group-hover:text-blue-600'
        }`}
      >
        {icon}
      </div>

      <div className="space-y-2">
        <h3
          className={`text-lg leading-tight font-bold ${
            highlight
              ? 'text-blue-900'
              : 'text-gray-900 dark:text-gray-200 group-hover:text-blue-900 dark:group-hover:text-gray-100'
          }`}
        >
          {title}
        </h3>
        <p
          className={`text-sm leading-relaxed ${
            highlight
              ? 'text-blue-700'
              : 'text-gray-600 dark:text-gray-400 group-hover:text-gray-700 dark:group-hover:text-gray-300'
          }`}
        >
          {description}
        </p>
      </div>

      {highlight && (
        <div className="absolute top-4 right-4">
          <div className="h-3 w-3 animate-pulse rounded-full bg-gradient-to-br from-yellow-400 to-orange-500"></div>
        </div>
      )}
    </div>
  );
};

const WhyChooseUs = ({
  heading,
  imageSrc,
  iconSrc,
  features,
  imagePosition = 'right',
}: WhyChooseUsProps) => {
  const isImageRight = imagePosition === 'right';

  return (
    <section
      className={`w-full bg-gray-100 dark:bg-[#0a0e17] relative overflow-hidden py-16 md:py-24`}
    >
      <div className="pointer-events-none absolute bottom-0 left-0 z-0 hidden h-[400px] w-[400px] dark:block">
        <div
          className="h-full w-full"
          style={{
            background:
              'radial-gradient(circle at 20% 80%, rgba(37,99,235,0.35) 0%, transparent 70%)',
            filter: 'blur(8px)',
          }}
        />
      </div>
      <div className="dark:hidden absolute inset-0 opacity-20">
        <div className="absolute top-10 right-20 h-40 w-40 rounded-full bg-blue-400 blur-3xl"></div>
        <div className="absolute bottom-20 left-10 h-32 w-32 rounded-full bg-purple-400 blur-2xl"></div>
        <div className="absolute top-1/3 left-1/3 h-20 w-20 rounded-full bg-pink-400 blur-xl"></div>
      </div>

      <div className="relative z-10 mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div
          className={`grid grid-cols-1 items-center gap-12 lg:grid-cols-12 lg:gap-16 ${
            isImageRight ? '' : 'lg:grid-flow-col-dense'
          }`}
        >
          <div
            className={`space-y-8 lg:col-span-7 ${
              isImageRight ? 'lg:pr-8' : 'lg:col-start-6 lg:pl-8'
            }`}
          >
            <div className="space-y-6">
              <div className="inline-flex items-center">
                <div className="rounded-2xl border border-gray-100 bg-white p-4 shadow-lg">
                  <Image
                    src={iconSrc}
                    width={70}
                    height={70}
                    className="h-10 w-10 md:h-14 md:w-14"
                    alt="icon"
                  />
                </div>
              </div>

              <div className="space-y-3">
                {heading}
              </div>
            </div>

            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              {features.map((feature, index) => (
                <FeatureItem
                  key={index}
                  icon={feature.icon}
                  title={feature.title}
                  description={feature.description}
                  highlight={feature.highlight}
                />
              ))}
            </div>
          </div>

          <div
            className={`flex items-center justify-center lg:col-span-5 ${
              isImageRight ? '' : 'lg:col-start-1 lg:row-start-1'
            }`}
          >
            <div className="group relative">
              <div className="relative transform overflow-hidden rounded-3xl border border-gray-100 bg-white p-6 shadow-2xl transition-transform duration-500 ease-out group-hover:scale-105">
                <Image
                  src={imageSrc}
                  width={500}
                  height={500}
                  className="h-auto w-full rounded-2xl object-cover"
                  alt="Why choose us illustration"
                  priority
                />

                <div className="pointer-events-none absolute inset-0 rounded-2xl bg-gradient-to-tr from-transparent via-transparent to-blue-500/10"></div>
              </div>

              <div className="absolute -top-6 -right-6 animate-bounce rounded-2xl border border-gray-100 bg-white p-4 shadow-xl">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">100%</div>
                  <div className="text-xs font-medium text-gray-500">
                    Satisfaction
                  </div>
                </div>
              </div>

              <div className="absolute -bottom-4 -left-4 animate-pulse rounded-2xl bg-gradient-to-r from-blue-500 to-[#000080] p-4 text-white shadow-xl">
                <div className="flex items-center gap-2">
                  <svg
                    className="h-5 w-5"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clipRule="evenodd"
                    />
                  </svg>
                  <span className="text-sm font-semibold">Trusted</span>
                </div>
              </div>

              <div className="absolute top-8 right-8 -z-10 h-24 w-24 rounded-full bg-blue-200 opacity-50 blur-xl"></div>
              <div className="absolute bottom-8 left-8 -z-10 h-32 w-32 rounded-full bg-purple-200 opacity-30 blur-2xl"></div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default WhyChooseUs;
