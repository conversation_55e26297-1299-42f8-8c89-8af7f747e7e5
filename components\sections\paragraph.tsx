import React from 'react';
import clsx from 'clsx';

type ParagraphProps = {
  children: React.ReactNode;
  className?: string;
};

export const Paragraph: React.FC<ParagraphProps> = ({
  children,
  className,
}) => (
  <p
    className={clsx(
      'max-w-2xl text-base md:text-lg text-gray-700 dark:text-gray-200 leading-relaxed md:leading-8',
      className
    )}
  >
    {children}
  </p>
);

export default Paragraph;