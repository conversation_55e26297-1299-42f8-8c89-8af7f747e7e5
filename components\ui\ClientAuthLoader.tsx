'use client';

import { useEffect } from 'react';

import { useAppDispatch } from '@/store';
import { userLoggedIn, userLoggedOut } from '@/store/slices/authSlice';
import {
  useLoadUserQuery,
  useRefreshTokenQuery,
} from '@/store/features/authApi';

export default function ClientAuthLoader() {
  const dispatch = useAppDispatch();

  // Step 1: Refresh token
  const { data: refreshData, isSuccess: isRefreshSuccess } =
    useRefreshTokenQuery({});

  // Step 2: Load user if refresh succeeded
  const {
    data: userData,
    isSuccess: isUserLoadSuccess,
    isError: isUserLoadError,
  } = useLoadUserQuery(undefined, {
    skip: !isRefreshSuccess,
  });

  useEffect(() => {
    if (isRefreshSuccess && isUserLoadSuccess && userData && refreshData) {
      dispatch(
        userLoggedIn({
          user: userData.data,
          accessToken: refreshData.data.accessToken,
          refreshToken: refreshData.data.refreshToken,
        })
      );
    }

    if (isUserLoadError) {
      dispatch(userLoggedOut());
    }
  }, [
    isRefreshSuccess,
    isUserLoadSuccess,
    userData,
    refreshData,
    dispatch,
    isUserLoadError,
  ]);

  return null; // invisible loader
}
