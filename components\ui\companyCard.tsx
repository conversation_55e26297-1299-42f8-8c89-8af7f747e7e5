import React from 'react';
import { Building2, MapPin, Users, Globe, ArrowRight, Calendar } from 'lucide-react';
import Link from 'next/link';
import { ICompany } from '@/types/ICompany';

interface CompanyCardProps {
  company: ICompany;
}

const CompanyCard: React.FC<CompanyCardProps> = ({ company }) => {
  // Get the default website or the first one if no default is set
  const primaryWebsite = company.websites?.find(w => w.isDefault) || company.websites?.[0];
  const websiteUrl = primaryWebsite?.website || '';
  
  // Format website URL for display (remove http/https)
  const displayWebsite = websiteUrl.replace(/^https?:\/\//, '');
  
  return (
    <div className="">
      {/* Main Card */}
      <div className="relative min-h-[360px] bg-white dark:bg-gray-900 rounded-3xl border border-gray-200 dark:border-gray-800 shadow-lg hover:shadow-2xl transition-all duration-500 hover:border-gray-300 dark:hover:border-gray-700 hover:-translate-y-1">
        
        {/* Animated Background Gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 via-transparent to-purple-50/50 dark:from-blue-950/20 dark:via-transparent dark:to-purple-950/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>
        
        {/* Content */}
        <div className="relative p-8">
          {/* Header Section */}
          <div className="flex items-start justify-between mb-6">
            <div className="flex items-center space-x-4">
              {/* Company Icon */}
              <div className="relative">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-xl">
                  <Building2 className="w-8 h-8 text-white" />
                </div>
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white dark:border-gray-900"></div>
              </div>
              
              {/* Company Info */}
              <div className="flex-1">
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-1 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">
                  {company.name}
                </h3>
                <div className="flex items-center space-x-2">
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300">
                    {company.industry}
                  </span>
                  {company.sector && (
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300">
                      {company.sector}
                    </span>
                  )}
                </div>
              </div>
            </div>
          </div>
          
          {/* Description */}
          {company.description && (
            <div className="mb-6">
              <p className="text-gray-600 dark:text-gray-400 text-sm leading-relaxed line-clamp-2">
                {company.description}
              </p>
            </div>
          )}
          
          {/* Details Section */}
          <div className="mb-6">
            <div className="flex flex-wrap items-center gap-6 text-sm">
              {/* Location */}
              <div className="flex items-center space-x-2">
                <MapPin className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                <span className="text-gray-700 dark:text-gray-300 font-medium">
                  {company.country || 'Location not specified'}
                </span>
              </div>
              
              {/* Company Size */}
              <div className="flex items-center space-x-2">
                <Users className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                <span className="text-gray-700 dark:text-gray-300 font-medium">
                  {company.companySize || 'Size not specified'}
                </span>
              </div>
              
              {/* Founded */}
              {company.founded && (
                <div className="flex items-center space-x-2">
                  <Calendar className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                  <span className="text-gray-700 dark:text-gray-300 font-medium">
                    Founded {new Date(company.founded).getFullYear()}
                  </span>
                </div>
              )}
              
              {/* Website */}
              {websiteUrl && (
                <div className="flex items-center space-x-2">
                  <Globe className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                  <a 
                    href={websiteUrl.startsWith('http') ? websiteUrl : `https://${websiteUrl}`} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors duration-200 font-medium"
                  >
                    {displayWebsite}
                  </a>
                </div>
              )}
            </div>
          </div>
          
          {/* Action Button */}
          <div className="flex justify-end">
            <Link href={`/companies/${company.slug}`}>
              <button className="group/btn flex items-center space-x-1.5 text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium text-sm transition-colors duration-200">
                <span>View Details</span>
                <ArrowRight className="w-4 h-4 group-hover/btn:translate-x-0.5 transition-transform duration-200" />
              </button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CompanyCard;