import { cn } from '@/lib/utils';

interface InfoFieldProps {
  title: string;
  value?: React.ReactNode;
  className?: string;
}

export const InfoField: React.FC<InfoFieldProps> = ({
  title,
  value,
  className,
}) => {
  return (
    <p className="font-nunito text-sm">
      <strong className="pr-3">{title}: </strong>
      <span className={cn('', className)}>{value || 'N/A'}</span>
    </p>
  );
};
