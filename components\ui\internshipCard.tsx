import React from 'react'
import { MapPin, Calendar, DollarSign, Building2, ArrowR<PERSON>, Bookmark, Clock, Users } from 'lucide-react';
import Link from 'next/link';

interface Company {
  name: string;
  organization: string;
  _id: string;
}

interface CustomFields {
  project_budget: number;
  team_size: number;
}

interface Duration {
  frequency: string;
  max: number;
  min: number;
}

interface InterviewRound {
  roundTitle: string;
  description: string;
  sortOrder: number;
}

interface Location {
  address: string;
  city: string;
  country: string;
  label: string;
  state: string;
}

interface Perk {
  title: string;
  sortOrder: number;
}

interface QuestionOption {
  label: string;
  value: string;
}

interface Question {
  question: string;
  isRequired: boolean;
  questionType: 'textarea' | 'radio' | 'checkbox' | 'text';
  options: QuestionOption[];
  questionId: number;
  hint?: string;
}

interface Responsibility {
  title: string;
  sortOrder: number;
}

interface SelectionProcessStage {
  stage: string;
  description: string;
  sortOrder: number;
}

interface Skill {
  skill: string;
  level: 'beginner' | 'intermediate' | 'advanced';
  sortOrder: number;
}

interface Internship {
  applicationDeadline: string;
  banner: string;
  category: string;
  company: Company;
  createdAt: string;
  createdBy: string;
  customFields: CustomFields;
  description: string;
  duration: Duration;
  eligibilityCriteria: string[];
  interviews: InterviewRound[];
  isDeleted: boolean;
  location: Location;
  mode: string;
  openings: number;
  perks: Perk[];
  postedAt: string;
  preferredLanguages: string[];
  qualification: string[];
  questions: Question[];
  responsibilities: Responsibility[];
  selectionProcess: SelectionProcessStage[];
  skills: Skill[];
  slug: string;
  status: string;
  stipend: string;
  tags: string[];
  title: string;
  updatedAt: string;
  workPlaceType: string;
  workingDays: string[];
  workingHours: string;
  _id: string;
}

interface InternshipCardProps {
  internship: Internship;
}

const InternshipCard: React.FC<InternshipCardProps> = ({ internship }) => {
  
  function getDaysAgo(pastDate: Date) {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    pastDate.setHours(0, 0, 0, 0);

    const diffTime = Math.abs(today.getTime() - pastDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      return "today";
    } else if (today.getTime() > pastDate.getTime()) {
      return `${diffDays} day${diffDays === 1 ? '' : 's'} ago`;
    } else {
      return `in ${diffDays} day${diffDays === 1 ? '' : 's'}`;
    }
  }

  // Internship-specific gradient colors
  const demoBanner = 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)';
  const bannerStyle = internship.banner 
    ? { backgroundImage: `url(${internship.banner})`, backgroundSize: 'cover', backgroundPosition: 'center' }
    : { background: demoBanner };

  return (
    <div className="">
      {/* Main Card */}
      <div className="relative bg-white dark:bg-gray-800 rounded-3xl border border-gray-200 dark:border-gray-700 shadow-lg hover:shadow-2xl transition-all duration-500 hover:border-gray-300 dark:hover:border-gray-600 hover:-translate-y-1">
        
        {/* Animated Background Gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-pink-50/50 via-transparent to-orange-50/50 dark:from-pink-950/20 dark:via-transparent dark:to-orange-950/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>
        
        {/* Banner Header - Responsive height */}
        <div className="relative h-40 sm:h-48 md:h-52 rounded-t-3xl overflow-hidden" style={bannerStyle}>
          {/* Banner Overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent"></div>
          
          <div className='flex justify-between'>
            {/* Company Logo - Centered */}
            <div className="flex justify-start p-5">
              <div className="w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 bg-gradient-to-br from-pink-500 to-orange-500 rounded-lg sm:rounded-xl flex items-center justify-center shadow-lg ring-2 sm:ring-4 ring-white/30 backdrop-blur-sm">
                <span className="text-white text-base sm:text-lg md:text-xl font-bold uppercase">
                  {internship.company.name[0]}
                </span>
              </div>
            </div>
            
            {/* Top Actions on Banner */}
            <div className="absolute top-3 right-3 sm:top-4 sm:right-4 flex items-center space-x-2">
              <span className="inline-flex items-center px-2 py-1 sm:px-3 sm:py-1 rounded-full text-xs font-medium bg-blue-950/10 dark:bg-white/20 backdrop-blur-sm dark:text-white border border-white/30">
                {internship.openings} spot{internship.openings !== 1 ? 's' : ''}
              </span>
              <button className="w-7 h-7 sm:w-8 sm:h-8 dark:bg-white/20 bg-blue-950/10  backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white/30 transition-colors duration-200 border border-white/30">
                <Bookmark className="w-3.5 h-3.5 sm:w-4 sm:h-4 dark:text-white" />
              </button>
            </div>
          </div>

          {/* Company Info in Banner - Stacked Layout */}
          <div className="absolute bottom-3 left-3 right-3 sm:bottom-4 sm:left-4 sm:right-4">
            {/* Company Name and Posted Date - Side by side */}
            <div className="flex items-center justify-between">
              {/* Company Name */}
              <p className="text-white text-sm sm:text-base md:text-lg font-semibold drop-shadow-lg truncate flex-1 mr-3">
                {internship.company.name}
              </p>
              
              {/* Posted Date */}
              <span className="px-2 py-1 sm:px-3 sm:py-1.5 bg-white/20 backdrop-blur-sm text-white text-xs sm:text-sm font-medium rounded-full border border-white/30 flex-shrink-0">
                {getDaysAgo(new Date(internship.postedAt))}
              </span>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="relative p-4 sm:p-6">
          {/* Internship Title */}
          <h2 className="text-lg sm:text-xl md:text-2xl font-bold text-gray-900 dark:text-white leading-snug mb-3 sm:mb-4 group-hover:text-pink-600 dark:group-hover:text-pink-400 transition-colors duration-300">
            {internship.title}
          </h2>

          {/* Tags */}
          <div className="flex gap-1.5 sm:gap-2 flex-wrap mb-4 sm:mb-6">
            <span className="px-2 py-1 sm:px-3 sm:py-1 bg-pink-100 dark:bg-pink-900/30 text-pink-800 dark:text-pink-300 text-xs font-medium rounded-full">
              {internship.category}
            </span>
            <span className="px-2 py-1 sm:px-3 sm:py-1 bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-300 text-xs font-medium rounded-full">
              {internship.mode}
            </span>
            <span className="px-2 py-1 sm:px-3 sm:py-1 bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300 text-xs font-medium rounded-full">
              {internship.workPlaceType}
            </span>
          </div>

          {/* Internship Details */}
          <div className="mb-4 sm:mb-6">
            <div className="flex flex-col sm:flex-row sm:flex-wrap sm:items-center gap-2 sm:gap-4 text-sm">
              {/* Location */}
              <div className="flex items-center space-x-2">
                <MapPin className="w-4 h-4 text-gray-500 dark:text-gray-400 flex-shrink-0" />
                <span className="text-gray-700 dark:text-gray-300 font-medium truncate">
                  {internship.location.city}, {internship.location.state}
                </span>
              </div>
              
              {/* Stipend */}
              <div className="flex items-center space-x-2">
                <DollarSign className="w-4 h-4 text-gray-500 dark:text-gray-400 flex-shrink-0" />
                <span className="text-gray-700 dark:text-gray-300 font-medium">
                  {internship.stipend}
                </span>
              </div>
              
              {/* Duration */}
              <div className="flex items-center space-x-2">
                <Calendar className="w-4 h-4 text-gray-500 dark:text-gray-400 flex-shrink-0" />
                <span className="text-gray-700 dark:text-gray-300 font-medium">
                  {internship.duration.min} - {internship.duration.max} {internship.duration.frequency}
                </span>
              </div>

              {/* Team Size */}
              {internship.customFields?.team_size && (
                <div className="flex items-center space-x-2">
                  <Users className="w-4 h-4 text-gray-500 dark:text-gray-400 flex-shrink-0" />
                  <span className="text-gray-700 dark:text-gray-300 font-medium">
                    Team of {internship.customFields.team_size}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Skills */}
          {internship.skills && internship.skills.length > 0 && (
            <div className="mb-4 sm:mb-6">
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">Required Skills:</p>
              <p className="text-sm font-semibold text-gray-900 dark:text-white">
                {internship.skills.slice(0, 3).map((skill) => skill.skill).join(', ')}
                {internship.skills.length > 3 && ` +${internship.skills.length - 3} more`}
              </p>
            </div>
          )}

          {/* Footer Action */}
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3 sm:gap-0 pt-4 border-t border-gray-100 dark:border-gray-800">
            <div className="text-sm text-gray-500 dark:text-gray-400">
              Deadline: {new Date(internship.applicationDeadline).toLocaleDateString()}
            </div>
            <Link href={`/opportunities/internships/${internship.slug}`}>
              <button className="group/btn flex items-center justify-center sm:justify-start space-x-1.5 bg-gradient-to-r from-pink-500 to-orange-500 hover:from-pink-600 hover:to-orange-600 text-white px-4 py-2 rounded-lg font-medium text-sm transition-all duration-200 shadow-md hover:shadow-lg w-full sm:w-auto">
                <span>Apply Now</span>
                <ArrowRight className="w-4 h-4 group-hover/btn:translate-x-0.5 transition-transform duration-200" />
              </button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InternshipCard;