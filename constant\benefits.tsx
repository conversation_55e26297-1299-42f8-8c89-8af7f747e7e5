import React from 'react';

export const certificationsBenefitsData = {
  hero: {
    title: "Why Professional Certifications Are Your Career Game-Changer",
    subtitle: "Stand Out, <PERSON><PERSON><PERSON>, Advance Faster",
    description: "In today's competitive job market, certifications are no longer optional—they're essential. Discover how the right certifications can transform your career prospects and open doors to opportunities you never thought possible.",
    backgroundGradient: "bg-gradient-to-br from-indigo-600 via-purple-600 to-pink-600",
    icon: (
      <svg className="w-16 h-16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
      </svg>
    )
  },
  statistics: [
    {
      value: "87%",
      label: "Higher Interview Success",
      description: "Certified professionals get more interview opportunities"
    },
    {
      value: "$15K+",
      label: "Average Salary Increase",
      description: "Annual salary boost with relevant certifications"
    },
    {
      value: "3.2x",
      label: "Faster Promotions",
      description: "Certified employees advance their careers quicker"
    }
  ],
  benefits: [
    {
      icon: (
        <svg className="w-8 h-8 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
        </svg>
      ),
      title: "Accelerated Career Growth",
      description: "Certifications fast-track your career advancement by demonstrating expertise and commitment to professional development.",
      stats: "65% faster promotion rate"
    },
    {
      icon: (
        <svg className="w-8 h-8 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
        </svg>
      ),
      title: "Higher Salary Potential",
      description: "Certified professionals command significantly higher salaries and have stronger negotiating power during job offers.",
      stats: "15-25% salary increase"
    },
    {
      icon: (
        <svg className="w-8 h-8 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2h8zM12 7h.01" />
        </svg>
      ),
      title: "Industry Recognition",
      description: "Stand out from the competition with globally recognized credentials that validate your skills and knowledge.",
      stats: "3x more recruiter views"
    },
    {
      icon: (
        <svg className="w-8 h-8 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
        </svg>
      ),
      title: "Skill Validation",
      description: "Prove your expertise with industry-standard certifications that employers trust and value.",
      stats: "90% employer confidence"
    },
    {
      icon: (
        <svg className="w-8 h-8 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
        </svg>
      ),
      title: "Professional Network",
      description: "Join exclusive communities of certified professionals and expand your network with like-minded experts.",
      stats: "50K+ certified professionals"
    },
    {
      icon: (
        <svg className="w-8 h-8 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
        </svg>
      ),
      title: "Continuous Learning",
      description: "Stay current with industry trends and maintain your competitive edge through ongoing education and skill development.",
      stats: "98% skill relevance"
    }
  ],
  resumeComparison: {
    title: "The Resume That Gets You Hired",
    subtitle: "See the dramatic difference certifications make on your resume and career prospects",
    without: {
      name: "Alex Johnson",
      title: "Software Developer",
      email: "<EMAIL>",
      sections: [
        {
          title: "Experience",
          items: [
            "Junior Software Developer at TechCorp (2022-2024) - Worked on basic web applications",
            "Intern at StartupXYZ (2021-2022) - Assisted with coding tasks and bug fixes",
            "Freelance Web Developer (2020-2021) - Built simple websites for small businesses"
          ]
        },
        {
          title: "Skills",
          items: [
            "JavaScript, HTML, CSS",
            "Basic React knowledge",
            "Git version control",
            "Problem-solving"
          ]
        },
        {
          title: "Education",
          items: [
            "Bachelor's in Computer Science - State University (2020)",
            "Relevant coursework: Data Structures, Algorithms, Web Development"
          ]
        },
        {
          title: "Projects",
          items: [
            "Personal portfolio website",
            "To-do list application",
            "Basic e-commerce site"
          ]
        }
      ]
    },
    with: {
      name: "Alex Johnson",
      title: "Certified Full Stack Developer & Cloud Solutions Architect",
      email: "<EMAIL>",
      sections: [
        {
          title: "Professional Certifications",
          items: [
            "AWS Certified Solutions Architect - Professional (2024)",
            "Google Cloud Professional Developer (2024)",
            "Microsoft Azure Developer Associate (2023)",
            "Certified Kubernetes Administrator (CKA) (2023)",
            "Meta React Developer Professional Certificate (2023)"
          ]
        },
        {
          title: "Experience",
          items: [
            "Senior Full Stack Developer at TechCorp (2023-2024) - Led development of cloud-native applications serving 100K+ users",
            "Cloud Solutions Developer at TechCorp (2022-2023) - Architected and deployed scalable microservices on AWS",
            "Software Developer at StartupXYZ (2021-2022) - Developed RESTful APIs and implemented CI/CD pipelines"
          ]
        },
        {
          title: "Technical Expertise",
          items: [
            "Cloud Platforms: AWS, Google Cloud, Microsoft Azure",
            "Frontend: React, Next.js, TypeScript, Tailwind CSS",
            "Backend: Node.js, Python, Java, Microservices Architecture",
            "DevOps: Docker, Kubernetes, Jenkins, Terraform",
            "Databases: PostgreSQL, MongoDB, Redis",
            "Security: OAuth, JWT, SSL/TLS, Security Best Practices"
          ]
        },
        {
          title: "Key Achievements",
          items: [
            "Reduced application deployment time by 75% through automated CI/CD implementation",
            "Architected cloud infrastructure saving company $50K annually in hosting costs",
            "Led team of 5 developers in successful migration to microservices architecture",
            "Achieved 99.9% uptime for critical production applications"
          ]
        },
        {
          title: "Education & Training",
          items: [
            "Bachelor's in Computer Science - State University (2020)",
            "AWS Advanced Architecting on AWS (40 hours)",
            "Google Cloud Professional Development (120 hours)",
            "Certified Scrum Master (CSM) Training"
          ]
        }
      ]
    }
  },
  cta: {
    title: "Ready to Transform Your Career?",
    description: "Join thousands of professionals who have accelerated their careers with industry-recognized certifications. Your future self will thank you.",
    buttonText: "Explore Certification Programs",
    buttonLink: "/professional-certifications"
  }
};