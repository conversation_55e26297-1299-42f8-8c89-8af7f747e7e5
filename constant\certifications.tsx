import React from 'react';

interface CertificationProvider {
  id: string;
  name: string;
  logo: string;
  color: string;
  bgColor: string;
}

interface Certification {
  id: string;
  title: string;
  description: string;
  provider: CertificationProvider;
  category: string;
  level: 'Beginner' | 'Intermediate' | 'Advanced' | 'Professional';
  duration: string;
  format: 'Online' | 'Proctored' | 'Hybrid';
  prerequisites: string[];
  skills: string[];
  image: string;
  rating: number;
  totalReviews: number;
  price: string;
  originalPrice?: string;
  validityPeriod: string;
  examDetails: {
    questions: number;
    timeLimit: string;
    passingScore: string;
  };
  link: string;
  featured?: boolean;
  trending?: boolean;
}

interface FilterOption {
  id: string;
  label: string;
}

export interface CertificationsPageData {
  header: {
    title: string;
    subtitle: string;
    description: string;
    backgroundGradient: string;
    icon: React.ReactNode;
  };
  stats: {
    totalCertifications: string;
    partneredCompanies: string;
    averageRating: string;
    successRate: string;
  };
  filters: {
    providers: FilterOption[];
    categories: FilterOption[];
    levels: FilterOption[];
    formats: FilterOption[];
    durations: FilterOption[];
  };
  certifications: Certification[];
  cta: {
    title: string;
    description: string;
    buttonText: string;
    buttonLink: string;
  };
}

export const webDevelopmentCertificationsData = {
  header: {
    title: 'Web Development Certifications',
    subtitle:
      'Master Modern Web Technologies with Industry-Leading Certifications',
    description:
      'Advance your career with globally recognized certifications from top tech companies. Build expertise in frontend, backend, and full-stack development.',
    backgroundGradient:
      'bg-gradient-to-br from-blue-600 via-indigo-700 to-purple-800',
    icon: (
      <svg
        className="h-12 w-12"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"
        />
      </svg>
    ),
  },
  stats: {
    totalCertifications: '45+',
    partneredCompanies: '12',
    averageRating: '4.8',
    successRate: '89%',
  },
  filters: {
    providers: [
      { id: 'google', label: 'Google' },
      { id: 'ibm', label: 'IBM' },
      { id: 'microsoft', label: 'Microsoft' },
      { id: 'amazon', label: 'Amazon' },
    ],
    categories: [
      { id: 'frontend', label: 'Frontend Development' },
      { id: 'backend', label: 'Backend Development' },
      { id: 'fullstack', label: 'Full-Stack Development' },
      { id: 'mobile', label: 'Mobile Development' },
      { id: 'devops', label: 'DevOps' },
    ],
    levels: [
      { id: 'beginner', label: 'Beginner' },
      { id: 'intermediate', label: 'Intermediate' },
      { id: 'advanced', label: 'Advanced' },
      { id: 'professional', label: 'Professional' },
    ],
    formats: [
      { id: 'online', label: 'Online' },
      { id: 'proctored', label: 'Proctored' },
      { id: 'hybrid', label: 'Hybrid' },
    ],
    durations: [
      { id: 'short', label: '1-3 months' },
      { id: 'medium', label: '3-6 months' },
      { id: 'long', label: '6+ months' },
    ],
  },
  certifications: [
    {
      id: 'google-ux-design',
      title: 'Google UX Design Professional Certificate',
      description:
        'Learn the fundamentals of UX design, including user research, wireframing, prototyping, and testing. Create a professional portfolio.',
      provider: {
        id: 'google',
        name: 'Google',
        logo: 'https://upload.wikimedia.org/wikipedia/commons/2/2f/Google_2015_logo.svg',
        color: 'text-blue-600',
        bgColor: 'bg-blue-50',
      },
      category: 'frontend',
      level: 'Beginner' as 'Beginner',
      duration: '3-6 months',
      format: 'Online' as 'Online',
      prerequisites: ['Basic computer skills', 'Interest in design'],
      skills: [
        'User Research',
        'Wireframing',
        'Prototyping',
        'Figma',
        'Adobe XD',
        'Usability Testing',
      ],
      image:
        'https://images.unsplash.com/photo-**********-2526d30994b5?w=400&h=300&fit=crop',
      rating: 4.8,
      totalReviews: 12450,
      price: '$49/month',
      originalPrice: '$79/month',
      validityPeriod: 'Lifetime',
      examDetails: {
        questions: 60,
        timeLimit: '90 min',
        passingScore: '80%',
      },
      link: '/professional-certifications/web-development/google-ux-design',
      featured: true,
      trending: true,
    },
    {
      id: 'ibm-fullstack-developer',
      title: 'IBM Full Stack Software Developer Certificate',
      description:
        'Comprehensive full-stack development program covering frontend, backend, databases, and cloud deployment.',
      provider: {
        id: 'ibm',
        name: 'IBM',
        logo: 'https://upload.wikimedia.org/wikipedia/commons/5/51/IBM_logo.svg',
        color: 'text-blue-700',
        bgColor: 'bg-blue-50',
      },
      category: 'fullstack',
      level: 'Intermediate' as 'Intermediate',
      duration: '6-8 months',
      format: 'Online' as 'Online',
      prerequisites: ['Programming fundamentals', 'Database basics'],
      skills: [
        'Node.js',
        'Express',
        'MongoDB',
        'React',
        'Docker',
        'Kubernetes',
      ],
      image:
        'https://images.unsplash.com/photo-1627398242454-45a1465c2479?w=400&h=300&fit=crop',
      rating: 4.6,
      totalReviews: 5670,
      price: '$59/month',
      originalPrice: '$89/month',
      validityPeriod: '2 years',
      examDetails: {
        questions: 90,
        timeLimit: '150 min',
        passingScore: '70%',
      },
      link: '/professional-certifications/web-development/ibm-fullstack-developer',
      trending: true,
    },
  ],
  cta: {
    title: 'Ready to Advance Your Web Development Career?',
    description:
      'Join thousands of developers who have transformed their careers with industry-recognized certifications. Start your journey today and unlock new opportunities.',
    buttonText: 'Browse All Certifications',
    buttonLink: '/professional-certifications/web-development',
  },
};

export const graphicDesignCertificationsData = {
  header: {
    title: 'Graphic Design Certifications',
    subtitle:
      'Master Visual Communication with Industry-Leading Design Certifications',
    description:
      'Elevate your creative career with globally recognized certifications from top design platforms. Build expertise in visual design, branding, and digital media.',
    backgroundGradient:
      'bg-gradient-to-br from-pink-600 via-purple-700 to-indigo-800',
    icon: (
      <svg
        className="h-12 w-12"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5H9a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V7a2 2 0 00-2-2z"
        />
      </svg>
    ),
  },
  stats: {
    totalCertifications: '35+',
    partneredCompanies: '8',
    averageRating: '4.7',
    successRate: '92%',
  },
  filters: {
    providers: [
      { id: 'adobe', label: 'Adobe' },
      { id: 'canva', label: 'Canva' },
      { id: 'skillshare', label: 'Skillshare' },
      { id: 'coursera', label: 'Coursera' },
    ],
    categories: [
      { id: 'visual-design', label: 'Visual Design' },
      { id: 'branding', label: 'Branding & Identity' },
      { id: 'digital-media', label: 'Digital Media' },
      { id: 'print-design', label: 'Print Design' },
      { id: 'ui-design', label: 'UI Design' },
    ],
    levels: [
      { id: 'beginner', label: 'Beginner' },
      { id: 'intermediate', label: 'Intermediate' },
      { id: 'advanced', label: 'Advanced' },
      { id: 'professional', label: 'Professional' },
    ],
    formats: [
      { id: 'online', label: 'Online' },
      { id: 'proctored', label: 'Proctored' },
      { id: 'portfolio', label: 'Portfolio-Based' },
    ],
    durations: [
      { id: 'short', label: '1-3 months' },
      { id: 'medium', label: '3-6 months' },
      { id: 'long', label: '6+ months' },
    ],
  },
  certifications: [
    {
      id: 'adobe-certified-expert',
      title: 'Adobe Certified Expert (ACE) - Photoshop',
      description:
        'Demonstrate mastery of Adobe Photoshop with advanced image editing, compositing, and design techniques for professional workflows.',
      provider: {
        id: 'adobe',
        name: 'Adobe',
        logo: 'https://upload.wikimedia.org/wikipedia/commons/8/8d/Adobe_Corporate_Logo.svg',
        color: 'text-red-600',
        bgColor: 'bg-red-50',
      },
      category: 'visual-design',
      level: 'Advanced' as 'Advanced',
      duration: '2-3 months',
      format: 'Proctored' as 'Proctored',
      prerequisites: ['1+ years Photoshop experience', 'Portfolio of work'],
      skills: [
        'Photoshop',
        'Image Editing',
        'Compositing',
        'Color Theory',
        'Digital Painting',
        'Retouching',
      ],
      image:
        'https://images.unsplash.com/photo-1626785774573-4b799315345d?w=400&h=300&fit=crop',
      rating: 4.8,
      totalReviews: 8420,
      price: '$150',
      validityPeriod: '2 years',
      examDetails: {
        questions: 50,
        timeLimit: '50 min',
        passingScore: '70%',
      },
      link: '/professional-certifications/graphic-design/adobe-photoshop-ace',
      featured: true,
      trending: true,
    },
    {
      id: 'canva-design-school',
      title: 'Canva Design School Professional Certificate',
      description:
        "Master modern design principles and create stunning visuals for digital and print media using Canva's comprehensive design platform.",
      provider: {
        id: 'canva',
        name: 'Canva',
        logo: 'https://upload.wikimedia.org/wikipedia/commons/0/08/Canva_icon_2021.svg',
        color: 'text-purple-600',
        bgColor: 'bg-purple-50',
      },
      category: 'digital-media',
      level: 'Beginner' as 'Beginner',
      duration: '1-2 months',
      format: 'Online' as 'Online',
      prerequisites: ['Basic computer skills', 'Creative interest'],
      skills: [
        'Canva Pro',
        'Brand Design',
        'Social Media Graphics',
        'Typography',
        'Layout Design',
        'Content Creation',
      ],
      image:
        'https://images.unsplash.com/photo-1609921212029-bb5a28e60960?w=400&h=300&fit=crop',
      rating: 4.6,
      totalReviews: 15670,
      price: '$39/month',
      originalPrice: '$59/month',
      validityPeriod: 'Lifetime',
      examDetails: {
        questions: 40,
        timeLimit: '60 min',
        passingScore: '75%',
      },
      link: '/professional-certifications/graphic-design/canva-design-school',
      trending: true,
    },
  ],
  cta: {
    title: 'Ready to Launch Your Design Career?',
    description:
      'Join thousands of designers who have elevated their creative careers with industry-recognized certifications. Start your design journey today and unlock endless creative possibilities.',
    buttonText: 'Browse All Design Certifications',
    buttonLink: '/professional-certifications/graphic-design',
  },
};

export const dataScienceCertificationsData = {
  header: {
    title: 'Data Science Certifications',
    subtitle:
      'Master Analytics and Machine Learning with Industry-Leading Certifications',
    description:
      'Transform your career with globally recognized data science certifications. Build expertise in machine learning, analytics, and big data technologies.',
    backgroundGradient:
      'bg-gradient-to-br from-emerald-600 via-teal-700 to-cyan-800',
    icon: (
      <svg
        className="h-12 w-12"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
        />
      </svg>
    ),
  },
  stats: {
    totalCertifications: '50+',
    partneredCompanies: '15',
    averageRating: '4.9',
    successRate: '87%',
  },
  filters: {
    providers: [
      { id: 'google', label: 'Google' },
      { id: 'ibm', label: 'IBM' },
      { id: 'microsoft', label: 'Microsoft' },
      { id: 'amazon', label: 'Amazon' },
      { id: 'coursera', label: 'Coursera' },
    ],
    categories: [
      { id: 'machine-learning', label: 'Machine Learning' },
      { id: 'data-analytics', label: 'Data Analytics' },
      { id: 'big-data', label: 'Big Data' },
      { id: 'ai', label: 'Artificial Intelligence' },
      { id: 'business-intelligence', label: 'Business Intelligence' },
    ],
    levels: [
      { id: 'beginner', label: 'Beginner' },
      { id: 'intermediate', label: 'Intermediate' },
      { id: 'advanced', label: 'Advanced' },
      { id: 'expert', label: 'Expert' },
    ],
    formats: [
      { id: 'online', label: 'Online' },
      { id: 'proctored', label: 'Proctored' },
      { id: 'hands-on', label: 'Hands-on Projects' },
    ],
    durations: [
      { id: 'short', label: '1-3 months' },
      { id: 'medium', label: '3-6 months' },
      { id: 'long', label: '6+ months' },
    ],
  },
  certifications: [
    {
      id: 'google-data-analytics',
      title: 'Google Data Analytics Professional Certificate',
      description:
        'Learn essential data analytics skills including data cleaning, analysis, and visualization using industry-standard tools like SQL, R, and Tableau.',
      provider: {
        id: 'google',
        name: 'Google',
        logo: 'https://upload.wikimedia.org/wikipedia/commons/2/2f/Google_2015_logo.svg',
        color: 'text-blue-600',
        bgColor: 'bg-blue-50',
      },
      category: 'data-analytics',
      level: 'Beginner' as 'Beginner',
      duration: '3-6 months',
      format: 'Online' as 'Online',
      prerequisites: ['Basic computer skills', 'High school math'],
      skills: [
        'SQL',
        'R Programming',
        'Tableau',
        'Data Cleaning',
        'Data Visualization',
        'Statistical Analysis',
      ],
      image:
        'https://images.unsplash.com/photo-**********-bebda4e38f71?w=400&h=300&fit=crop',
      rating: 4.8,
      totalReviews: 18750,
      price: '$49/month',
      originalPrice: '$79/month',
      validityPeriod: 'Lifetime',
      examDetails: {
        questions: 8,
        timeLimit: 'Self-paced',
        passingScore: 'Project Completion',
      },
      link: '/professional-certifications/data-science/google-data-analytics',
      featured: true,
      trending: true,
    },
    {
      id: 'ibm-data-science',
      title: 'IBM Data Science Professional Certificate',
      description:
        'Comprehensive program covering Python, machine learning, data visualization, and statistical analysis with hands-on projects using real datasets.',
      provider: {
        id: 'ibm',
        name: 'IBM',
        logo: 'https://upload.wikimedia.org/wikipedia/commons/5/51/IBM_logo.svg',
        color: 'text-blue-700',
        bgColor: 'bg-blue-50',
      },
      category: 'machine-learning',
      level: 'Intermediate' as 'Intermediate',
      duration: '6-8 months',
      format: 'Online' as 'Online',
      prerequisites: ['Programming basics', 'Statistics fundamentals'],
      skills: [
        'Python',
        'Machine Learning',
        'Data Mining',
        'Jupyter Notebooks',
        'Pandas',
        'Scikit-learn',
      ],
      image:
        'https://images.unsplash.com/photo-1518186285589-2f7649de83e0?w=400&h=300&fit=crop',
      rating: 4.7,
      totalReviews: 12890,
      price: '$59/month',
      validityPeriod: 'Lifetime',
      examDetails: {
        questions: 12,
        timeLimit: 'Self-paced',
        passingScore: 'Project Approval',
      },
      link: '/professional-certifications/data-science/ibm-data-science',
      trending: true,
    },
  ],
  cta: {
    title: 'Ready to Lead the Data Revolution?',
    description:
      'Join thousands of data professionals who have transformed their careers with industry-recognized certifications. Start your data science journey today and unlock high-demand opportunities.',
    buttonText: 'Browse All Data Science Certifications',
    buttonLink: '/professional-certifications/data-science',
  },
};

export const digitalMarketingCertificationsData = {
  header: {
    title: 'Digital Marketing Certifications',
    subtitle: 'Master Online Marketing with Industry-Leading Certifications',
    description:
      'Accelerate your marketing career with globally recognized certifications from top platforms. Build expertise in SEO, social media, PPC, and content marketing.',
    backgroundGradient:
      'bg-gradient-to-br from-orange-600 via-red-700 to-pink-800',
    icon: (
      <svg
        className="h-12 w-12"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z"
        />
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z"
        />
      </svg>
    ),
  },
  stats: {
    totalCertifications: '38+',
    partneredCompanies: '10',
    averageRating: '4.6',
    successRate: '91%',
  },
  filters: {
    providers: [
      { id: 'google', label: 'Google' },
      { id: 'facebook', label: 'Meta' },
      { id: 'hubspot', label: 'HubSpot' },
      { id: 'semrush', label: 'SEMrush' },
      { id: 'hootsuite', label: 'Hootsuite' },
    ],
    categories: [
      { id: 'search-marketing', label: 'Search Marketing' },
      { id: 'social-media', label: 'Social Media Marketing' },
      { id: 'content-marketing', label: 'Content Marketing' },
      { id: 'email-marketing', label: 'Email Marketing' },
      { id: 'analytics', label: 'Marketing Analytics' },
    ],
    levels: [
      { id: 'beginner', label: 'Beginner' },
      { id: 'intermediate', label: 'Intermediate' },
      { id: 'advanced', label: 'Advanced' },
      { id: 'expert', label: 'Expert' },
    ],
    formats: [
      { id: 'online', label: 'Online' },
      { id: 'proctored', label: 'Proctored' },
      { id: 'practical', label: 'Practical Assessment' },
    ],
    durations: [
      { id: 'short', label: '1-3 months' },
      { id: 'medium', label: '3-6 months' },
      { id: 'long', label: '6+ months' },
    ],
  },
  certifications: [
    {
      id: 'google-ads-certification',
      title: 'Google Ads Certification',
      description:
        'Master Google Ads platform with comprehensive training in search, display, video, shopping, and app advertising campaigns.',
      provider: {
        id: 'google',
        name: 'Google',
        logo: 'https://upload.wikimedia.org/wikipedia/commons/2/2f/Google_2015_logo.svg',
        color: 'text-blue-600',
        bgColor: 'bg-blue-50',
      },
      category: 'search-marketing',
      level: 'Intermediate' as 'Intermediate',
      duration: '2-3 months',
      format: 'Online' as 'Online',
      prerequisites: ['Basic marketing knowledge', 'Google Ads account'],
      skills: [
        'Google Ads',
        'PPC',
        'Keyword Research',
        'Campaign Management',
        'Ad Copywriting',
        'Conversion Tracking',
      ],
      image:
        'https://images.unsplash.com/photo-*************-afdab827c52f?w=400&h=300&fit=crop',
      rating: 4.7,
      totalReviews: 22340,
      price: 'Free',
      validityPeriod: '1 year',
      examDetails: {
        questions: 50,
        timeLimit: '75 min',
        passingScore: '80%',
      },
      link: '/professional-certifications/digital-marketing/google-ads',
      featured: true,
      trending: true,
    },
    {
      id: 'meta-social-media',
      title: 'Meta Social Media Marketing Professional Certificate',
      description:
        'Learn to create effective social media campaigns across Facebook, Instagram, and other platforms with advanced targeting and analytics.',
      provider: {
        id: 'facebook',
        name: 'Meta',
        logo: 'https://upload.wikimedia.org/wikipedia/commons/7/7b/Meta_Platforms_Inc._logo.svg',
        color: 'text-blue-700',
        bgColor: 'bg-blue-50',
      },
      category: 'social-media',
      level: 'Beginner' as 'Beginner',
      duration: '4-5 months',
      format: 'Online' as 'Online',
      prerequisites: ['Basic computer skills', 'Social media familiarity'],
      skills: [
        'Facebook Ads',
        'Instagram Marketing',
        'Social Media Strategy',
        'Content Creation',
        'Analytics',
        'Community Management',
      ],
      image:
        'https://images.unsplash.com/photo-1611926653458-09294b3142bf?w=400&h=300&fit=crop',
      rating: 4.5,
      totalReviews: 15890,
      price: '$49/month',
      originalPrice: '$79/month',
      validityPeriod: 'Lifetime',
      examDetails: {
        questions: 60,
        timeLimit: '90 min',
        passingScore: '75%',
      },
      link: '/professional-certifications/digital-marketing/meta-social-media',
      trending: true,
    },
  ],
  cta: {
    title: 'Ready to Dominate Digital Marketing?',
    description:
      'Join thousands of marketers who have accelerated their careers with industry-recognized certifications. Start your digital marketing journey today and drive measurable results.',
    buttonText: 'Browse All Marketing Certifications',
    buttonLink: '/professional-certifications/digital-marketing',
  },
};

export const mobileAppDevelopmentCertificationsData = {
  header: {
    title: 'Mobile App Development Certifications',
    subtitle: 'Master Mobile Technologies with Industry-Leading Certifications',
    description:
      'Build your mobile development career with globally recognized certifications. Master iOS, Android, and cross-platform development technologies.',
    backgroundGradient:
      'bg-gradient-to-br from-violet-600 via-purple-700 to-indigo-800',
    icon: (
      <svg
        className="h-12 w-12"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"
        />
      </svg>
    ),
  },
  stats: {
    totalCertifications: '28+',
    partneredCompanies: '6',
    averageRating: '4.8',
    successRate: '85%',
  },
  filters: {
    providers: [
      { id: 'google', label: 'Google' },
      { id: 'apple', label: 'Apple' },
      { id: 'meta', label: 'Meta' },
      { id: 'microsoft', label: 'Microsoft' },
      { id: 'unity', label: 'Unity' },
    ],
    categories: [
      { id: 'android', label: 'Android Development' },
      { id: 'ios', label: 'iOS Development' },
      { id: 'cross-platform', label: 'Cross-Platform' },
      { id: 'game-development', label: 'Game Development' },
      { id: 'ar-vr', label: 'AR/VR Development' },
    ],
    levels: [
      { id: 'beginner', label: 'Beginner' },
      { id: 'intermediate', label: 'Intermediate' },
      { id: 'advanced', label: 'Advanced' },
      { id: 'expert', label: 'Expert' },
    ],
    formats: [
      { id: 'online', label: 'Online' },
      { id: 'proctored', label: 'Proctored' },
      { id: 'project-based', label: 'Project-Based' },
    ],
    durations: [
      { id: 'short', label: '1-3 months' },
      { id: 'medium', label: '3-6 months' },
      { id: 'long', label: '6+ months' },
    ],
  },
  certifications: [
    {
      id: 'google-android-developer',
      title: 'Google Associate Android Developer',
      description:
        'Demonstrate proficiency in Android development using Kotlin, including UI design, data storage, and API integration.',
      provider: {
        id: 'google',
        name: 'Google',
        logo: 'https://upload.wikimedia.org/wikipedia/commons/2/2f/Google_2015_logo.svg',
        color: 'text-green-600',
        bgColor: 'bg-green-50',
      },
      category: 'android',
      level: 'Intermediate' as 'Intermediate',
      duration: '4-6 months',
      format: 'Hybrid' as 'Hybrid',
      prerequisites: [
        'Kotlin programming',
        'Android Studio familiarity',
        'Java basics',
      ],
      skills: [
        'Kotlin',
        'Android SDK',
        'Android Studio',
        'Material Design',
        'REST APIs',
        'SQLite',
      ],
      image:
        'https://images.unsplash.com/photo-1607252650355-f7fd0460ccdb?w=400&h=300&fit=crop',
      rating: 4.7,
      totalReviews: 7890,
      price: '$149',
      validityPeriod: '2 years',
      examDetails: {
        questions: 25,
        timeLimit: '48 hours',
        passingScore: 'Project Approval',
      },
      link: '/professional-certifications/mobile-app-development/google-android-developer',
      featured: true,
      trending: true,
    },
    {
      id: 'apple-ios-developer',
      title: 'Apple iOS App Development with Swift',
      description:
        'Master iOS app development using Swift programming language, UIKit, and SwiftUI for creating native iPhone and iPad applications.',
      provider: {
        id: 'apple',
        name: 'Apple',
        logo: 'https://upload.wikimedia.org/wikipedia/commons/f/fa/Apple_logo_black.svg',
        color: 'text-gray-700',
        bgColor: 'bg-gray-50',
      },
      category: 'ios',
      level: 'Intermediate' as 'Intermediate',
      duration: '3-5 months',
      format: 'Online' as 'Online',
      prerequisites: [
        'Swift programming',
        'Xcode familiarity',
        'Programming fundamentals',
      ],
      skills: [
        'Swift',
        'SwiftUI',
        'UIKit',
        'Core Data',
        'Xcode',
        'App Store Guidelines',
      ],
      image:
        'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=400&h=300&fit=crop',
      rating: 4.8,
      totalReviews: 12450,
      price: '$99/month',
      originalPrice: '$149/month',
      validityPeriod: 'Lifetime',
      examDetails: {
        questions: 12,
        timeLimit: 'Self-paced',
        passingScore: 'App Store Submission',
      },
      link: '/professional-certifications/mobile-app-development/apple-ios-developer',
      trending: true,
    },
  ],
  cta: {
    title: 'Ready to Build the Next Big App?',
    description:
      'Join thousands of developers who have launched successful mobile careers with industry-recognized certifications. Start your mobile development journey today and create apps that millions will use.',
    buttonText: 'Browse All Mobile Development Certifications',
    buttonLink: '/professional-certifications/mobile-app-development',
  },
};
