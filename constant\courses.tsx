import React from 'react';
import {
  HiOutlineCode,
  HiOutlineCheckCircle,
  HiOutlineUsers,
  HiOutlineClock,
  HiOutlineDesktopComputer,
  HiOutlineBookOpen,
  HiOutlineAcademicCap,
  HiOutlineBadgeCheck,
  HiOutlineArrowLeft,
} from 'react-icons/hi';

interface CourseCard {
  id: string;
  title: string;
  description: string;
  duration: string;
  level: 'Beginner' | 'Intermediate' | 'Advanced';
  price: string;
  originalPrice?: string;
  rating: number;
  students: string;
  image: string;
  tags: string[];
  instructor: {
    name: string;
    avatar: string;
  };
  features: string[];
  link: string;
}

export interface FilterOption {
  id: string;
  label: string;
  count?: number;
}

export interface CertificationsPageData {
  header: {
    backLabel: string;
    backIcon: React.ReactNode;
    backLink: string;
    title: string;
    subtitle: string;
    description: string;
    backgroundGradient: string;
    icon: React.ReactNode;
  };
  stats: {
    totalCourses: string;
    totalStudents: string;
    averageRating: string;
    completionRate: string;
  };
  about: {
    title: string;
    description: string;
    highlights: string[];
  };
  outcomes: {
    title: string;
    description: string;
    skills: string[];
    careerStats: {
      label: string;
      value: string;
    }[];
    jobRoles: string[];
  };
  courses: CourseCard[];
  cta: {
    title: string;
    description: string;
    buttonText: string;
    buttonLink: string;
  };
}

export const certificationsData = {
  'google-ux-design': {
    header: {
      backLabel: 'Back to Certifications',
      backIcon: <HiOutlineArrowLeft className="h-5 w-5" />,
      backLink: '/professional-certifications/web-development',
      title: 'Google UX Design Certification',
      subtitle: 'Build Apps for Android & iOS',
      description:
        'Learn to create native and cross-platform mobile apps using React Native, Flutter, and more. Launch your career as a mobile developer.',
      backgroundGradient:
        'bg-gradient-to-br from-purple-600 via-blue-600 to-green-600',
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          x="0px"
          y="0px"
          width="100"
          height="100"
          viewBox="0 0 48 48"
        >
          <path
            fill="#fbc02d"
            d="M43.611,20.083H42V20H24v8h11.303c-1.649,4.657-6.08,8-11.303,8c-6.627,0-12-5.373-12-12	s5.373-12,12-12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C12.955,4,4,12.955,4,24s8.955,20,20,20	s20-8.955,20-20C44,22.659,43.862,21.35,43.611,20.083z"
          ></path>
          <path
            fill="#e53935"
            d="M6.306,14.691l6.571,4.819C14.655,15.108,18.961,12,24,12c3.059,0,5.842,1.154,7.961,3.039	l5.657-5.657C34.046,6.053,29.268,4,24,4C16.318,4,9.656,8.337,6.306,14.691z"
          ></path>
          <path
            fill="#4caf50"
            d="M24,44c5.166,0,9.86-1.977,13.409-5.192l-6.19-5.238C29.211,35.091,26.715,36,24,36	c-5.202,0-9.619-3.317-11.283-7.946l-6.522,5.025C9.505,39.556,16.227,44,24,44z"
          ></path>
          <path
            fill="#1565c0"
            d="M43.611,20.083L43.595,20L42,20H24v8h11.303c-0.792,2.237-2.231,4.166-4.087,5.571	c0.001-0.001,0.002-0.001,0.003-0.002l6.19,5.238C36.971,39.205,44,34,44,24C44,22.659,43.862,21.35,43.611,20.083z"
          ></path>
        </svg>
      ),
    },
    stats: {
      totalCourses: '10+',
      totalStudents: '5.3K+',
      averageRating: '4.8',
      completionRate: '90%',
    },
    about: {
      title: 'About Google UX Design Certification',
      description:
        "The Google UX Design Certificate is a comprehensive program designed to prepare you for a career in user experience design. This certificate program, developed by Google, provides hands-on experience with industry-standard tools and real-world projects. You'll learn the complete UX design process from user research and wireframing to prototyping and testing. The program covers design thinking, accessibility principles, and how to create user-centered designs that solve real problems. Upon completion, you'll have a professional portfolio showcasing your skills and be prepared for entry-level UX design roles at top companies worldwide.",
      highlights: [
        'Industry-recognized certification from Google',
        'Hands-on projects with real-world applications',
        'Learn industry-standard tools like Figma and Adobe XD',
        'Build a professional portfolio',
        'No prior experience required',
        'Average completion time: 3-6 months',
      ],
    },

    outcomes: {
      title: 'Learning Outcomes & Career Impact',
      description:
        "Upon completing the Google UX Design Certification, you'll be equipped with the essential skills and knowledge needed to launch a successful career in UX design. Our graduates have gone on to work at leading companies and have seen significant career advancement.",
      skills: [
        'User Research & Analysis',
        'Information Architecture',
        'Wireframing & Prototyping',
        'Visual Design Principles',
        'Usability Testing',
        'Accessibility Design',
      ],
      careerStats: [
        { label: 'Average Starting Salary', value: '$75,000' },
        { label: 'Job Placement Rate', value: '87%' },
        { label: 'Career Advancement', value: '92%' },
        { label: 'Employer Satisfaction', value: '4.8/5' },
      ],
      jobRoles: [
        'UX Designer',
        'UI/UX Designer',
        'User Researcher',
        'Product Designer',
        'Interaction Designer',
        'Visual Designer',
      ],
    },
    courses: [
      {
        id: 'react-native-basics',
        title: 'React Native Basics: Cross-Platform Apps',
        description:
          'Build mobile apps for Android and iOS using React Native. Learn components, navigation, and APIs.',
        duration: '9 hours',
        level: 'Beginner' as const,
        price: '$69',
        originalPrice: '$109',
        rating: 4.8,
        students: '1,210',
        image:
          'https://images.unsplash.com/photo-*************-675f0ddb6308?auto=format&fit=crop&w=1000&q=80',
        tags: ['React Native', 'Mobile', 'Cross-Platform', 'JavaScript'],
        instructor: {
          name: 'Kevin Tran',
          avatar:
            'https://images.unsplash.com/photo-1511367461989-f85a21fda167?auto=format&fit=crop&w=100&q=80',
        },
        features: [
          'Cross-platform setup',
          'Navigation patterns',
          'API integration',
          'Device testing',
        ],
        link: '/professional-certifications/web-development/google-ux-design/react-native-basics',
      },
      {
        id: 'flutter-complete',
        title: 'Flutter Complete Guide',
        description:
          'Master Flutter and Dart to build beautiful, high-performance mobile apps for Android and iOS.',
        duration: '14 hours',
        level: 'Intermediate' as const,
        price: '$89',
        originalPrice: '$139',
        rating: 4.7,
        students: '1,034',
        image:
          'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=1000&q=80',
        tags: ['Flutter', 'Dart', 'Mobile', 'UI'],
        instructor: {
          name: 'Aisha Patel',
          avatar:
            'https://images.unsplash.com/photo-1517841905240-472988babdf9?auto=format&fit=crop&w=100&q=80',
        },
        features: [
          'Flutter widgets',
          'State management',
          'Animations',
          'App deployment',
        ],
        link: '/professional-certifications/web-development/google-ux-design/flutter-complete',
      },
      {
        id: 'android-native',
        title: 'Android Native Development',
        description:
          'Learn to build native Android apps using Java and Kotlin. Understand layouts, activities, and app architecture.',
        duration: '12 hours',
        level: 'Intermediate' as const,
        price: '$79',
        originalPrice: '$129',
        rating: 4.7,
        students: '1,008',
        image:
          'https://images.unsplash.com/photo-1516116216624-53e697fedbea?auto=format&fit=crop&w=1000&q=80',
        tags: ['Android', 'Java', 'Kotlin', 'Native'],
        instructor: {
          name: 'Rajesh Kumar',
          avatar:
            'https://images.unsplash.com/photo-1519340333755-c190485c1df4?auto=format&fit=crop&w=100&q=80',
        },
        features: [
          'Android Studio setup',
          'UI layouts',
          'App lifecycle',
          'Publishing to Play Store',
        ],
        link: '/professional-certifications/web-development/google-ux-design/android-native',
      },
      {
        id: 'ios-development',
        title: 'iOS Development with Swift',
        description:
          'Build native iOS apps using Swift and Xcode. Learn UIKit, SwiftUI, and app deployment.',
        duration: '11 hours',
        level: 'Advanced' as const,
        price: '$89',
        originalPrice: '$139',
        rating: 4.8,
        students: '1,054',
        image:
          'https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=1000&q=80',
        tags: ['iOS', 'Swift', 'Xcode', 'Native'],
        instructor: {
          name: 'Emily Wong',
          avatar:
            'https://images.unsplash.com/photo-1519340333755-c190485c1df4?auto=format&fit=crop&w=100&q=80',
        },
        features: [
          'Swift basics',
          'UIKit & SwiftUI',
          'App deployment',
          'App Store publishing',
        ],
        link: '/professional-certifications/web-development/google-ux-design/ios-development',
      },
    ],
    cta: {
      title: 'Start Building Mobile Apps Today',
      description:
        'Join thousands of developers and learn to build high-quality mobile apps for Android and iOS.',
      buttonText: 'Explore All Mobile Courses',
      buttonLink: '/professional-certifications/web-development',
    },
  },
};

export interface CourseData {
  header: {
    backLabel: string;
    icon: React.ReactNode;
    backLink: string;
  };
  hero: {
    icon: React.ReactNode;
    badge: string;
    title: string;
    description: string;
    stats: {
      icon: React.ReactNode;
      label: string;
      value: string;
    }[];
    actions: {
      label: string;
      className: string;
      icon: React.ReactNode | null;
    }[];
    codeBlock: {
      filename: string;
      code: string;
      badges: {
        label: string;
        className: string;
      }[];
    };
  };
  overview: {
    why: {
      icon: React.ReactNode;
      title: string;
      description: string;
    }[];
    skills: {
      name: string;
      level: number;
      color: string;
    }[];
    who: {
      icon: React.ReactNode;
      title: string;
      description: string;
      bg: string;
    }[];
  };
  curriculum: {
    module: string;
    title: string;
    duration: string;
    topics: string[];
    icon: React.ReactNode;
  }[];
  projects: {
    title: string;
    description: string;
    image: string;
    tech: string[];
  }[];
  certification: {
    items: {
      icon: React.ReactNode;
      title: string;
      description: string;
      color: string;
    }[];
    illustration: {
      icon: React.ReactNode;
      title: string;
      description: string;
    };
  };
}

export const coursesData = {
  'react-native-basics': {
    header: {
      backLabel: 'Back to Certification',
      icon: <HiOutlineArrowLeft className="h-5 w-5" />,
      backLink: '/professional-certifications/web-development/google-ux-design',
    },
    hero: {
      icon: <HiOutlineCode className="h-12 w-12 text-white" />,
      badge: 'Professional Certification',
      title: 'React Native Basics',
      description:
        'Build mobile apps for Android and iOS using React Native. Learn components, navigation, and APIs.',
      stats: [
        {
          icon: <HiOutlineClock className="h-6 w-6" />,
          label: 'Duration',
          value: '9 hours',
        },
        {
          icon: <HiOutlineUsers className="h-6 w-6" />,
          label: 'Students',
          value: '1,210',
        },
        {
          icon: <HiOutlineBookOpen className="h-6 w-6" />,
          label: 'Projects',
          value: '4+ Real',
        },
        {
          icon: <HiOutlineBadgeCheck className="h-6 w-6" />,
          label: 'Certificate',
          value: 'Industry',
        },
      ],
      actions: [
        {
          label: 'Enroll Now - $69',
          className:
            'bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-blue-50 transition-colors',
          icon: null,
        },
      ],
      codeBlock: {
        filename: 'App.js',
        code: `import React from 'react';
import { View, Text } from 'react-native';

const App = () => {
  return (
    <View>
      <Text>Hello, world!</Text>
    </View>
  );
};

export default App;`,
        badges: [
          {
            label: 'React Native',
            className:
              'absolute -top-4 -right-4 bg-yellow-400 text-slate-800 px-4 py-2 rounded-full text-sm font-semibold shadow-lg',
          },
          {
            label: 'JavaScript',
            className:
              'absolute -bottom-4 -left-4 bg-green-500 text-white px-4 py-2 rounded-full text-sm font-semibold shadow-lg',
          },
        ],
      },
    },
    overview: {
      why: [
        {
          icon: (
            <HiOutlineCheckCircle className="mt-1 h-6 w-6 flex-shrink-0 text-green-600" />
          ),
          title: 'Build Cross-Platform Apps',
          description:
            'Learn to build mobile apps for Android and iOS using React Native.',
        },
        {
          icon: (
            <HiOutlineCheckCircle className="mt-1 h-6 w-6 flex-shrink-0 text-green-600" />
          ),
          title: 'Master React Native Components',
          description:
            'Learn to build reusable UI components and manage state in React Native.',
        },
        {
          icon: (
            <HiOutlineCheckCircle className="mt-1 h-6 w-6 flex-shrink-0 text-green-600" />
          ),
          title: 'API Integration and Navigation',
          description:
            'Learn to integrate APIs and manage navigation in React Native apps.',
        },
      ],
      skills: [
        { name: 'React Native', level: 90, color: 'bg-yellow-500' },
        { name: 'JavaScript', level: 85, color: 'bg-green-500' },
        { name: 'Components', level: 80, color: 'bg-blue-500' },
        { name: 'Navigation', level: 75, color: 'bg-purple-500' },
      ],
      who: [
        {
          icon: <HiOutlineAcademicCap className="h-8 w-8 text-blue-600" />,
          title: 'Students',
          description:
            'Computer science students or recent graduates looking to build practical skills.',
          bg: 'bg-blue-100',
        },
        {
          icon: <HiOutlineUsers className="h-8 w-8 text-green-600" />,
          title: 'Career Changers',
          description:
            'Professionals from other fields wanting to transition into tech.',
          bg: 'bg-green-100',
        },
        {
          icon: (
            <HiOutlineDesktopComputer className="h-8 w-8 text-purple-600" />
          ),
          title: 'Working Professionals',
          description:
            'Developers and IT professionals aiming to upskill and advance their careers.',
          bg: 'bg-purple-100',
        },
      ],
    },
    curriculum: [
      {
        module: 'Module 1',
        title: 'React Native Fundamentals',
        duration: '3 weeks',
        topics: [
          'React Native Basics',
          'Components and Props',
          'State Management',
          'API Integration',
        ],
        icon: <HiOutlineCode className="h-6 w-6" />,
      },
      {
        module: 'Module 2',
        title: 'Advanced React Native',
        duration: '4 weeks',
        topics: [
          'Navigation and Routing',
          'Async Programming',
          'State Management',
          'API Integration',
        ],
        icon: <HiOutlineCode className="h-6 w-6" />,
      },
      {
        module: 'Module 3',
        title: 'Advanced React Native',
        duration: '4 weeks',
        topics: [
          'Navigation and Routing',
          'Async Programming',
          'State Management',
          'API Integration',
        ],
        icon: <HiOutlineCode className="h-6 w-6" />,
      },
    ],
    projects: [
      {
        title: 'React Native App',
        description: 'A simple React Native app for iOS and Android.',
        image:
          'https://images.unsplash.com/photo-*************-675f0ddb6308?auto=format&fit=crop&w=1000&q=80',
        tech: ['React', 'Node.js', 'MongoDB'],
      },
    ],
    certification: {
      items: [
        {
          icon: <HiOutlineBadgeCheck className="h-8 w-8 text-green-600" />,
          title: 'Industry Recognized Certificate',
          description:
            'Receive a certificate upon successful completion, recognized by top employers.',
          color: 'text-green-700',
        },
        {
          icon: <HiOutlineUsers className="h-8 w-8 text-blue-600" />,
          title: 'Career Guidance',
          description:
            'Get resume reviews, interview prep, and job referrals from our expert mentors.',
          color: 'text-blue-700',
        },
        {
          icon: <HiOutlineAcademicCap className="h-8 w-8 text-purple-600" />,
          title: 'Lifelong Learning',
          description:
            'Access our library of online courses, tutorials, and workshops for lifelong learning.',
          color: 'text-purple-700',
        },
      ],
      illustration: {
        icon: <HiOutlineBadgeCheck className="h-20 w-20 text-green-500" />,
        title: 'Showcase Your Achievement',
        description:
          'Share your certificate on LinkedIn and with employers to boost your career prospects.',
      },
    },
  },
};
