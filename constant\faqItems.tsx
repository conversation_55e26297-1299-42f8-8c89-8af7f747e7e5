import { FAQItem } from '@/components/custom/FAQ';

export const faqs: FAQItem[] = [
  {
    category: 'General',
    faqs: [
      {
        question: 'What is this career portal?',
        answer: 'It is a platform to help you find jobs, internships, and training programs.',
      },
      {
        question: 'Is the portal free to use?',
        answer: 'Yes, accessing and browsing jobs on the portal is completely free.',
      },
      {
        question: 'Who can use this portal?',
        answer: 'Anyone looking for jobs, internships, or training opportunities can use the portal.',
      },
      {
        question: 'How do I create an account?',
        answer: 'Click on the Sign Up button and fill in your details to create a free account.',
      },
      {
        question: 'Can I use the portal on mobile devices?',
        answer: 'Yes, the portal is fully responsive and works on all devices.',
      },
    ],
  },
  {
    category: 'Legal',
    faqs: [
      {
        question: 'What are your terms of service?',
        answer:
          'Please read our <a href="/terms" className="text-blue-600 hover:text-blue-800">Terms of Service</a> for details.',
      },
      {
        question: 'How is my data protected?',
        answer: 'We use industry-standard security measures to protect your data and privacy.',
      },
      {
        question: 'Can I delete my account?',
        answer: 'Yes, you can delete your account at any time from your profile settings.',
      },
      {
        question: 'Do you share my information with employers?',
        answer: 'Your information is only shared with employers when you apply for a job.',
      },
      {
        question: 'Where can I find your privacy policy?',
        answer:
          'Our privacy policy is available <a href="/privacy" className="text-blue-600 hover:text-blue-800">here</a>.',
      },
    ],
  },
  {
    category: 'Application',
    faqs: [
      {
        question: 'How do I apply for a job?',
        answer: 'Browse jobs, click apply, and follow the instructions to submit your application.',
      },
      {
        question: 'Can I apply for multiple jobs?',
        answer: 'Yes, you can apply to as many jobs as you like.',
      },
      {
        question: 'How do I track my applications?',
        answer: 'Go to your dashboard to see the status of all your applications.',
      },
      {
        question: 'Can I edit my application after submitting?',
        answer: 'No, once submitted, applications cannot be edited. Please review carefully before submitting.',
      },
      {
        question: 'Will I get notified about my application status?',
        answer: 'Yes, you will receive email notifications for any updates on your application.',
      },
    ],
  },
  {
    category: 'Interview',
    faqs: [
      {
        question: 'How can I prepare for interviews?',
        answer: (
          <ul className="list-disc pl-5">
            <li>Research the company.</li>
            <li>Practice common interview questions.</li>
            <li>Dress professionally.</li>
          </ul>
        ),
      },
      {
        question: 'What should I bring to an interview?',
        answer: (
          <ul className="list-disc pl-5">
            <li>Copies of your resume.</li>
            <li>A notepad and pen.</li>
            <li>Any required documents mentioned by the employer.</li>
          </ul>
        ),
      },
      {
        question: 'How early should I arrive for an interview?',
        answer: 'Arrive at least 10-15 minutes before your scheduled interview time.',
      },
      {
        question: 'What if I cannot attend the interview?',
        answer: 'Contact the employer as soon as possible to reschedule or inform them of your absence.',
      },
      {
        question: 'How do I follow up after an interview?',
        answer: 'Send a thank-you email to the interviewer within 24 hours of your interview.',
      },
    ],
  },
];