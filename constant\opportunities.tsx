import { HiOutlineCode, HiOutlinePencilAlt, HiOutlineChartBar, HiOutlinePhotograph, HiOutlineFilm, HiOutlineDocumentText, HiOutlineBriefcase, HiOutlineAcademicCap, HiOutlineUserGroup } from 'react-icons/hi';
export const benefits = [
  {
    icon: <HiOutlineBriefcase className="h-8 w-8 text-indigo-600" />,
    title: 'Diverse Career Paths',
    description:
      'Explore a wide range of opportunities including full-time jobs, internships, and freelance projects—all in one place.',
  },
  {
    icon: <HiOutlineAcademicCap className="h-8 w-8 text-indigo-600" />,
    title: 'Skill Development',
    description:
      'Grow your skills with real-world experience, mentorship, and access to innovative projects across multiple domains.',
  },
  {
    icon: <HiOutlineUserGroup className="h-8 w-8 text-indigo-600" />,
    title: 'Community & Networking',
    description:
      'Connect with a vibrant community of professionals, mentors, and organizations to expand your network and career prospects.',
  },
];

export const cards = [
  {
    title: 'Jobs',
    description: 'Find your next full-time or part-time role at Sudha Software Solutions and its group companies.',
    href: '/opportunities/jobs',
    icon: <HiOutlineBriefcase className="h-10 w-10 text-white" />,
    gradient: 'from-indigo-500 to-purple-600',
  },
  {
    title: 'Internships',
    description: 'Kickstart your career with hands-on internships designed for learning and growth.',
    href: '/opportunities/internships',
    icon: <HiOutlineAcademicCap className="h-10 w-10 text-white" />,
    gradient: 'from-blue-500 to-cyan-600',
  },
  {
    title: 'Freelance',
    description: 'Work flexibly on freelance projects and connect with clients seeking your expertise.',
    href: '/opportunities/freelance',
    icon: <HiOutlineUserGroup className="h-10 w-10 text-white" />,
    gradient: 'from-emerald-500 to-teal-600',
  },
];

export const jobsFeature = [
  {
    icon: '✅',
    title: 'Our Journey of Excellence',
    description:
      'From humble beginnings to becoming a trailblazer in digital innovation, Sudha Software Solutions has consistently pushed boundaries to deliver unparalleled tech solutions. Our legacy is built on passion, perseverance, and a relentless pursuit of excellence.',
  },
  {
    icon: '✅',
    title: 'A Collaborative Culture',
    description:
      'At our core, we believe in the power of collaboration. Our teams are united by a shared vision to innovate and excel, ensuring every idea is nurtured and every challenge transformed into an opportunity.',
  },
  {
    icon: '✅',
    title: 'Pioneering the Future',
    description:
      'Embracing change with open arms, we continuously reinvent ourselves to stay ahead of the curve. Join us, and be a part of a future where innovation drives successand every challenge becomes a stepping stone to greatness.',
  },
];

export const internshipsFeature = [
  {
    icon: '✅',
    title: 'Hands-on Experience',
    description: 'Gain real-world experience working on innovative projects.',
  },
  {
    icon: '✅',
    title: 'Mentorship and Learning',
    description:
      'Work closely with industry experts and receive personalized mentorship.',
  },
  {
    icon: '✅',
    title: 'Networking Opportunities',
    description:
      'Build valuable connections in the tech industry that can propel your career forward.',
  },
];

export const popularServices = [
  {
    title: 'Web Development',
    description:
      'Full-stack development, frontend, backend, and e-commerce solutions',
    tags: ['React', 'Node.js', 'Python', 'PHP', 'WordPress'],
    icon: <HiOutlineCode />,
    gradient: 'from-indigo-500 to-purple-600',
  },
  {
    title: 'UI/UX Design',
    description:
      'User interface design, user experience optimization, and prototyping',
    tags: ['Figma', 'Adobe XD', 'Sketch', 'Prototyping', 'User Research'],
    icon: <HiOutlinePencilAlt />,
    gradient: 'from-emerald-500 to-teal-600',
  },
  {
    title: 'Digital Marketing',
    description:
      'SEO, social media marketing, content strategy, and PPC campaigns',
    tags: [
      'SEO',
      'Social Media',
      'Content Marketing',
      'Google Ads',
      'Analytics',
    ],
    icon: <HiOutlineChartBar />,
    gradient: 'from-amber-500 to-orange-600',
  },
  {
    title: 'Graphic Design',
    description:
      'Logo design, branding, print design, and visual identity creation',
    tags: ['Adobe Creative Suite', 'Branding', 'Print Design', 'Illustration'],
    icon: <HiOutlinePhotograph />,
    gradient: 'from-pink-500 to-rose-600',
  },
  {
    title: 'Video Production',
    description:
      'Video editing, animation, explainer videos, and motion graphics',
    tags: ['After Effects', 'Premiere Pro', 'Motion Graphics', '3D Animation'],
    icon: <HiOutlineFilm />,
    gradient: 'from-blue-500 to-cyan-600',
  },
  {
    title: 'Content Writing',
    description:
      'Blog writing, copywriting, technical documentation, and SEO content',
    tags: ['SEO Writing', 'Technical Writing', 'Copywriting', 'Blog Posts'],
    icon: <HiOutlineDocumentText />,
    gradient: 'from-violet-500 to-indigo-600',
  },
];