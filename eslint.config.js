// eslint.config.js
import js from '@eslint/js';
import next from 'eslint-config-next';
import prettier from 'eslint-config-prettier';
import tseslint from 'typescript-eslint';
import pluginPrettier from 'eslint-plugin-prettier';
import pluginReact from 'eslint-plugin-react';
import pluginNext from '@next/eslint-plugin-next';

/** @type {import("eslint").Linter.FlatConfig[]} */
export default [
  {
    ignores: [
      '**/node_modules/**',
      '.next/**',
      'dist/**',
      'public/**',
      '**/*.config.js',
    ],
  },
  js.configs.recommended,
  ...tseslint.configs.recommended,
  next(),
  prettier,
  {
    plugins: {
      prettier: pluginPrettier,
      react: pluginReact,
      '@next/next': pluginNext,
    },
    rules: {
      'prettier/prettier': 'warn',
      'react/jsx-key': 'warn',
      'react/react-in-jsx-scope': 'off',
      '@typescript-eslint/no-unused-vars': [
        'warn',
        { argsIgnorePattern: '^_' },
      ],
      '@next/next/no-img-element': 'off',
    },
  },
];
