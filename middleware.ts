// middleware.ts
import { NextRequest, NextResponse } from 'next/server';

export function middleware(request: NextRequest) {
  const accessToken = request.cookies.get('accessToken')?.value;
  const path = request.nextUrl.pathname;

  const isAuthPage = path === '/sign-in' || path === '/sign-up';
  const isDashboardPage = path.startsWith('/dashboard');

  // ✅ 1. If user has accessToken and tries to access sign-in or sign-up, redirect to dashboard
  if (accessToken && isAuthPage) {
    return NextResponse.redirect(new URL('/dashboard', request.url));
  }

  // ✅ 2. If user tries to access /dashboard but has no accessToken, redirect to sign-in
  if (!accessToken && isDashboardPage) {
    return NextResponse.redirect(new URL('/sign-in', request.url));
  }

  // ✅ Allow all other routes to pass
  return NextResponse.next();
}

export const config = {
  matcher: ['/dashboard/:path*', '/sign-in', '/sign-up'],
};
