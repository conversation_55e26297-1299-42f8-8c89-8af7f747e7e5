{"name": "careers-portal", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "eslint .", "format": "prettier --write ."}, "dependencies": {"@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@react-three/drei": "^10.2.0", "@react-three/fiber": "^9.1.2", "@reduxjs/toolkit": "^2.8.2", "@tabler/icons-react": "^3.34.0", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.17.0", "jose": "^6.0.11", "lottie-react": "^2.4.1", "lucide-react": "^0.511.0", "motion": "^12.16.0", "next": "15.3.3", "next-themes": "^0.4.6", "ogl": "^1.0.11", "postcss": "^8.5.4", "postprocessing": "^6.37.4", "react": "^19.0.0", "react-day-picker": "^9.8.0", "react-dom": "^19.0.0", "react-hook-form": "^7.58.1", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "sonner": "^2.0.5", "tailwind-merge": "^3.3.0", "three": "^0.167.1", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3", "@next/eslint-plugin-next": "^15.3.3", "@rushstack/eslint-patch": "^1.11.0", "@tailwindcss/postcss": "^4.1.8", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^8.33.0", "@typescript-eslint/parser": "^8.33.0", "eslint": "^8.57.1", "eslint-config-next": "^15.3.3", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.1", "eslint-plugin-react": "^7.37.5", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.12", "tailwindcss": "^4.1.8", "tw-animate-css": "^1.3.4", "typescript": "^5"}}