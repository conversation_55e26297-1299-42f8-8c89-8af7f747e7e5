import { z } from 'zod';

export const personalDetailSchema = z.object({
  firstName: z
    .string()
    .nonempty('First name is required')
    .min(2, 'First name must be at least 2 characters long')
    .max(50, 'First name must be at most 50 characters long'),
  lastName: z
    .string()
    .nonempty('Last name is required')
    .min(2, 'Last name must be at least 2 characters long')
    .max(50, 'Last name must be at most 50 characters long'),
  phone: z.string().nonempty('Phone number is required'),
  email: z
    .string()
    .nonempty('Email is required')
    .email('Must be a valid email address'),
  gender: z.string(),
  dob: z.date().optional(),
  // .refine(value => !value || !isNaN(Date.parse(value)), {
  //   message: 'Date of birth must be a valid date',
  // }),
  fatherName: z.string(),
  motherName: z.string(),
  maritalStatus: z.string(),
});

export const skillSchema = z.object({
  name: z
    .string()
    .nonempty(' Name is required')
    .min(2, ' Name must be at least 2 characters long')
    .max(50, ' Name must be at most 50 characters long'),
  level: z
    .string()
    .nonempty('level name is required')
    .min(2, 'level name must be at least 2 characters long')
    .max(50, 'level name must be at most 50 characters long'),
  monthsOfExperience: z.number(),
});
