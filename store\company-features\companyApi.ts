import { ICompany } from '@/types/ICompany';
import { baseApi } from '../api/baseApi';
import { ApiResponse } from '@/types/IGobal';



export const companyApi = baseApi
  .enhanceEndpoints({ addTagTypes: ['company'] })
  .injectEndpoints({
    endpoints: builder => ({
      getAllCompanies: builder.query<ApiResponse<{companies: ICompany[]}>, void>({
        query: () => '/companies',
        providesTags: ['company'],
      }),
      getCompanyBySlug: builder.query<ApiResponse<ICompany>, string>({
        query:  (slug)=> `/companies/slug/${slug}`,
        providesTags: ['company'],
      }),
      createCompany: builder.mutation<ApiResponse<ICompany>, Partial<ICompany>>({
        query: body => ({
          url: '/companies',
          method: 'POST',
          body,
        }),
        invalidatesTags: ['company'],
      }),
      updateCompany: builder.mutation<ApiResponse<ICompany>, Partial<ICompany>>({
        query: body => ({
          url: '/companies',
          method: 'PATCH',
          body,
        }),
        invalidatesTags: ['company'],
      }),
      deleteCompany: builder.mutation<{ success: boolean }, string>({
        query: id => ({
          url: `/companies/${id}`,
          method: 'DELETE',
        }),
        invalidatesTags: ['company'],
      }),
    }),

    overrideExisting: false,
  });

export const {
  useGetAllCompaniesQuery,
  useGetCompanyBySlugQuery,
  useCreateCompanyMutation,
  useUpdateCompanyMutation,
  useDeleteCompanyMutation,
} = companyApi;
