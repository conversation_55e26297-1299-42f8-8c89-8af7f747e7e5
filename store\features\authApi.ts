import { IUser } from '@/types/IUser';
import { baseApi } from '../api/baseApi';

import { userLoggedIn, userLoggedOut } from '../slices/authSlice';
import { ApiResponse } from '@/types/IGobal';

export const authApi = baseApi.injectEndpoints({
  endpoints: builder => ({
    login: builder.mutation<
      ApiResponse<{ user: IUser; accessToken: string; refreshToken: string }>,
      { email: string; password: string }
    >({
      query: body => ({
        url: '/auth/login',
        method: 'POST',
        body,
      }),
      async onQueryStarted(_, { queryFulfilled, dispatch }) {
        try {
          const { data } = await queryFulfilled;
          dispatch(userLoggedIn(data.data));
        } catch (err) {
          console.error('Login failed:', err);
        }
      },
    }),
    register: builder.mutation<
      ApiResponse<{ user: IUser; accessToken: string; refreshToken: string }>,
      { email: string; password: string }
    >({
      query: body => ({
        url: '/auth/signup',
        method: 'POST',
        body,
      }),
      async onQueryStarted(_, { queryFulfilled, dispatch }) {
        try {
          const { data } = await queryFulfilled;
          dispatch(userLoggedIn(data.data));
        } catch (err) {
          console.error('Signup failed:', err);
        }
      },
    }),
    forgetPassword: builder.mutation<ApiResponse<any>, { email: string }>({
      query: body => ({
        url: '/auth/forgot-password',
        method: 'POST',
        body,
      }),
    }),
    resetPassword: builder.mutation<
      ApiResponse<any>,
      { token: string; password: string; confirmPassword: string }
    >({
      query: body => ({
        url: '/auth/reset-password',
        method: 'POST',
        body,
      }),
    }),
    refreshToken: builder.query({
      query: () => ({ url: '/auth/refresh-token', method: 'POST' }),
    }),
    loadUser: builder.query<{ data: IUser }, void>({
      query: () => ({ url: '/users/me' }),
    }),
    logout: builder.mutation<void, void>({
      query: () => ({ url: '/auth/logout', method: 'POST' }),
      async onQueryStarted(_, { dispatch }) {
        dispatch(userLoggedOut());
      },
    }),
  }),
  overrideExisting: false,
});

export const {
  useLoginMutation,
  useRegisterMutation,
  useRefreshTokenQuery,
  useForgetPasswordMutation,
  useResetPasswordMutation,
  useLoadUserQuery,
  useLogoutMutation,
} = authApi;
