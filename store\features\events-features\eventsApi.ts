import { IEvent } from '@/types/IEvents';
import { baseApi } from '../../api/baseApi';
import { ApiResponse } from '@/types/IGobal';

export const eventsApi = baseApi
  .enhanceEndpoints({ addTagTypes: ['events'] })
  .injectEndpoints({
    endpoints: builder => ({
      getAllEvents: builder.query<
        ApiResponse<{ events: IEvent[]; meta: any }>,
        Record<string, any>
      >({
        query: params => ({
          url: '/events',
          params,
        }),
        providesTags: ['events'],
      }),
      // 🗓️ Get all registered events
      getRegisteredEvents: builder.query<
        ApiResponse<{ events: IEvent[]; meta: any }>,
        Record<string, any>
      >({
        query: params => ({
          url: '/events/registered',
          params,
        }),
        providesTags: ['events'],
      }),

      getEventBySlug: builder.query<ApiResponse<IEvent>, string>({
        query: slug => `/events/slug/${slug}`,
        providesTags: ['events'],
      }),
    }),
    overrideExisting: false,
  });

export const {
  useGetRegisteredEventsQuery,
  useGetEventBySlugQuery,
  useGetAllEventsQuery,
} = eventsApi;
