import axios from "axios"

export const baseUrl = process.env.NEXT_PUBLIC_API_URL;

const api = axios.create({
    baseURL: baseUrl,  
})

export const getAllInternships = () => {
    if(!baseUrl){
        throw new Error('NEXT_PUBLIC_API_URL is not defined in environment variables.');
    }
    return api.get("/internships")
}
export const getInternshipBySlug = (slug:string) => {
    if(!baseUrl){
        throw new Error('NEXT_PUBLIC_API_URL is not defined in environment variables.');
    }
    return api.get(`/internships/slug/${slug}`)
}
