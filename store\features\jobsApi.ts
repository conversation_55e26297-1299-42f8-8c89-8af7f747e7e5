import axios from "axios"

export const baseUrl = process.env.NEXT_PUBLIC_API_URL;

const api = axios.create({
    baseURL: baseUrl,  
})

export const getAllJobs = () => {
    if(!baseUrl){
        throw new Error('NEXT_PUBLIC_API_URL is not defined in environment variables.');
    }
    return api.get("/jobs")
}
export const getJobBySlug = (slug:string) => {
    if(!baseUrl){
        throw new Error('NEXT_PUBLIC_API_URL is not defined in environment variables.');
    }
    return api.get(`/jobs/slug/${slug}`)
}
