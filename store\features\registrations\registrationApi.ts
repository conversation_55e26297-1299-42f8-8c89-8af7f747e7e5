import { IEventRegistrationPayload } from '@/types/IEventRegistration';
import { baseApi } from '../../api/baseApi';
import { ApiResponse } from '@/types/IGobal';

export const registrationApi = baseApi.injectEndpoints({
  endpoints: builder => ({
    createEventRegistration: builder.mutation<
      ApiResponse<IEventRegistrationPayload>,
      {
        event: string;
        paymentStatus?: 'Pending' | 'Paid' | 'Failed';
        registeredBy?: string;
        answers: Array<{
          questionId: number;
          answer: string | string[];
        }>;
      }
    >({
      query: body => ({
        url: '/eventRegistrations',
        method: 'POST',
        body,
      }),
    }),

  }),
  overrideExisting: false,
});

export const {
    useCreateEventRegistrationMutation
} = registrationApi