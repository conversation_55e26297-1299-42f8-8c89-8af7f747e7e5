import { IVideoResource, IArticleResource, ICourseResource, IDocumentResource } from '@/types/IResources';
import { baseApi } from '../../api/baseApi';
import { ApiResponse } from '@/types/IGobal';

export const resourcesApi = baseApi
  .enhanceEndpoints({ addTagTypes: ['documents', 'articles', 'courses', 'videos'] })
  .injectEndpoints({
    endpoints: builder => ({
      getAllDocuments: builder.query<ApiResponse<{resources: IDocumentResource[], meta: any}>, Record<string, any>>({
        query: (params) => ({
          url: '/documents',
          params,
        }),
        providesTags: ['documents'],
      }),
      getAllArticles: builder.query<ApiResponse<{resources: IArticleResource[], meta: any}>, Record<string, any>>({
        query: (params) => ({
          url: '/articleResources',
          params,
        }),
        providesTags: ['articles'],
      }),
      getAllCourses: builder.query<ApiResponse<{resources: ICourseResource[], meta: any}>, Record<string, any>>({
        query: (params) => ({
          url: '/courseResources',
          params,
        }),
        providesTags: ['courses'],
      }),
      getAllVideos: builder.query<ApiResponse<{resources: IVideoResource[], meta: any}>, Record<string, any>>({
        query: (params) => ({
          url: '/videoResources',
          params,
        }),
        providesTags: ['videos'],
      }),
    }),
    overrideExisting: false,
})

export const { useGetAllArticlesQuery, useGetAllCoursesQuery, useGetAllDocumentsQuery, useGetAllVideosQuery } = resourcesApi;