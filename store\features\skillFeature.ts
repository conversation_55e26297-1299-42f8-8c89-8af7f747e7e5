import { ISkill } from '@/types/IUser';
import { baseApi } from '../api/baseApi';
import { ApiResponse } from '@/types/IGobal';

export const userApi = baseApi
  .enhanceEndpoints({ addTagTypes: ['skills'] })
  .injectEndpoints({
    endpoints: builder => ({
      getMySkills: builder.query<ApiResponse<ISkill[]>, void>({
        query: () => '/users/me/skills',
        providesTags: ['skills'],
      }),
      createSkill: builder.mutation<ApiResponse<ISkill>, Partial<ISkill>>({
        query: body => ({
          url: '/users/skills',
          method: 'POST',
          body,
        }),
        invalidatesTags: ['skills'],
      }),
      updateSkill: builder.mutation<ISkill, Partial<ISkill>>({
        query: body => ({
          url: '/users/skills',
          method: 'PATCH',
          body,
        }),
        invalidatesTags: ['skills'],
      }),
      deleteSkill: builder.mutation<{ success: boolean }, string>({
        query: id => ({
          url: `/users/skills/${id}`,
          method: 'DELETE',
        }),
        invalidatesTags: ['skills'],
      }),
    }),

    overrideExisting: false,
  });

export const {
  useGetMySkillsQuery,
  useCreateSkillMutation,
  useUpdateSkillMutation,
  useDeleteSkillMutation,
} = userApi;
