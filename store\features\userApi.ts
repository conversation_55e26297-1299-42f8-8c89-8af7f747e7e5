import {
  ICertificate,
  IEmergencyContact,
  IExperience,
  IIdentityDocument,
  IImportantLink,
  IQualification,
  ISkill,
  IUser,
} from '@/types/IUser';
import { baseApi } from '../api/baseApi';
import { IUpdateUserRequest } from '@/types/IUserRequest';
import { ApiResponse } from '@/types/IGobal';

export const userApi = baseApi
  .enhanceEndpoints({ addTagTypes: ['users', 'skills'] })
  .injectEndpoints({
    endpoints: builder => ({
      // 👤 Get current user
      getCurrentUser: builder.query<IUser, void>({
        query: () => '/users/me',
        providesTags: ['users'],
      }),

      // 🧑 Get user by ID
      getUserById: builder.query<ApiResponse<IUser>, string>({
        query: id => `/users/${id}`,
        providesTags: ['users'],
      }),

      // 📝 Update user
      updateUser: builder.mutation<
        ApiResponse<IUser>,
        { id: string; body: IUpdateUserRequest }
      >({
        query: ({ id, body }) => ({
          url: `/users/${id}`,
          method: 'PATCH',
          body,
        }),
        invalidatesTags: ['users'],
      }),

      // 🖼️ Update profile image
      updateProfileImage: builder.mutation<IUser, FormData>({
        query: formData => ({
          url: `/users/me/profile-image`,
          method: 'PATCH',
          body: formData,
        }),
        invalidatesTags: ['users'],
      }),

      // =============================
      // 🔗 Important Links
      // =============================

      getMyLinks: builder.query<IImportantLink[], void>({
        query: () => '/users/me/links',
      }),
      createLink: builder.mutation<IImportantLink, Partial<IImportantLink>>({
        query: body => ({
          url: '/users/links',
          method: 'POST',
          body,
        }),
      }),
      updateLink: builder.mutation<IImportantLink, Partial<IImportantLink>>({
        query: body => ({
          url: '/users/links',
          method: 'PATCH',
          body,
        }),
      }),
      deleteLink: builder.mutation<{ success: boolean }, string>({
        query: id => ({
          url: `/users/links/${id}`,
          method: 'DELETE',
        }),
      }),

      // =============================
      // 🛠️ Skills
      // =============================

      getMySkills: builder.query<ApiResponse<ISkill[]>, void>({
        query: () => '/users/me/skills',
        providesTags: ['skills'],
      }),
      createSkill: builder.mutation<ApiResponse<ISkill>, Partial<ISkill>>({
        query: body => ({
          url: '/users/skills',
          method: 'POST',
          body,
        }),
        invalidatesTags: ['skills'],
      }),
      updateSkill: builder.mutation<ISkill, Partial<ISkill>>({
        query: body => ({
          url: '/users/skills',
          method: 'PATCH',
          body,
        }),
        invalidatesTags: ['skills'],
      }),
      deleteSkill: builder.mutation<{ success: boolean }, string>({
        query: id => ({
          url: `/users/skills/${id}`,
          method: 'DELETE',
        }),
      }),

      // =============================
      // ☎️ Emergency Contact
      // =============================

      getMyEmergencyContact: builder.query<IEmergencyContact[], void>({
        query: () => '/users/me/emergency-contact',
      }),
      createEmergencyContact: builder.mutation<
        IEmergencyContact,
        Partial<IEmergencyContact>
      >({
        query: body => ({
          url: '/users/emergency-contact',
          method: 'POST',
          body,
        }),
      }),
      updateEmergencyContact: builder.mutation<
        IEmergencyContact,
        { id: string; body: Partial<IEmergencyContact> }
      >({
        query: ({ id, body }) => ({
          url: `/users/emergency-contact/${id}`,
          method: 'PATCH',
          body,
        }),
      }),
      deleteEmergencyContact: builder.mutation<{ success: boolean }, string>({
        query: id => ({
          url: `/users/emergency-contact/${id}`,
          method: 'DELETE',
        }),
      }),

      // =============================
      // 🎓 Qualification
      // =============================

      getMyQualifications: builder.query<IQualification[], void>({
        query: () => '/users/me/qualifications',
      }),
      createQualification: builder.mutation<
        IQualification,
        Partial<IQualification>
      >({
        query: body => ({
          url: '/users/qualifications',
          method: 'POST',
          body,
        }),
      }),
      updateQualification: builder.mutation<
        IQualification,
        Partial<IQualification>
      >({
        query: body => ({
          url: '/users/qualifications',
          method: 'PATCH',
          body,
        }),
      }),
      deleteQualification: builder.mutation<{ success: boolean }, string>({
        query: id => ({
          url: `/users/qualifications/${id}`,
          method: 'DELETE',
        }),
      }),

      // =============================
      // 📜 Certificates
      // =============================

      getMyCertificates: builder.query<ICertificate[], void>({
        query: () => '/users/me/certificates',
      }),
      createCertificate: builder.mutation<ICertificate, Partial<ICertificate>>({
        query: body => ({
          url: '/users/certificates',
          method: 'POST',
          body,
        }),
      }),
      updateCertificate: builder.mutation<ICertificate, Partial<ICertificate>>({
        query: body => ({
          url: '/users/certificates',
          method: 'PATCH',
          body,
        }),
      }),
      deleteCertificate: builder.mutation<{ success: boolean }, string>({
        query: id => ({
          url: `/users/certificates/${id}`,
          method: 'DELETE',
        }),
      }),

      // =============================
      // 🧑‍💻 Experience
      // =============================

      getMyExperience: builder.query<IExperience[], void>({
        query: () => '/users/me/experience',
      }),
      createExperience: builder.mutation<IExperience, Partial<IExperience>>({
        query: body => ({
          url: '/users/experience',
          method: 'POST',
          body,
        }),
      }),
      updateExperience: builder.mutation<
        IExperience,
        { id: string; body: Partial<IExperience> }
      >({
        query: ({ id, body }) => ({
          url: `/users/experience/${id}`,
          method: 'PATCH',
          body,
        }),
      }),
      deleteExperience: builder.mutation<{ success: boolean }, string>({
        query: id => ({
          url: `/users/experience/${id}`,
          method: 'DELETE',
        }),
      }),

      // =============================
      // 🪪 Identity Documents
      // =============================

      getMyDocuments: builder.query<IIdentityDocument[], void>({
        query: () => '/users/me/identity-documents',
      }),
      createDocument: builder.mutation<
        IIdentityDocument,
        Partial<IIdentityDocument>
      >({
        query: body => ({
          url: '/users/identity-documents',
          method: 'POST',
          body,
        }),
      }),
      updateDocument: builder.mutation<
        IIdentityDocument,
        Partial<IIdentityDocument>
      >({
        query: body => ({
          url: '/users/identity-documents',
          method: 'PATCH',
          body,
        }),
      }),
      deleteDocument: builder.mutation<{ success: boolean }, string>({
        query: id => ({
          url: `/users/identity-documents/${id}`,
          method: 'DELETE',
        }),
      }),
    }),

    overrideExisting: false,
  });

export const {
  useGetCurrentUserQuery,
  useGetUserByIdQuery,
  useUpdateUserMutation,
  useUpdateProfileImageMutation,

  useGetMySkillsQuery,
  useCreateSkillMutation,
  useUpdateSkillMutation,
  useDeleteSkillMutation,

  useGetMyLinksQuery,
  useCreateLinkMutation,
  useUpdateLinkMutation,
  useDeleteLinkMutation,

  useGetMyCertificatesQuery,
  useCreateCertificateMutation,
  useUpdateCertificateMutation,
  useDeleteCertificateMutation,

  useGetMyExperienceQuery,
  useCreateExperienceMutation,
  useUpdateExperienceMutation,
  useDeleteExperienceMutation,

  useGetMyQualificationsQuery,
  useCreateQualificationMutation,
  useUpdateQualificationMutation,
  useDeleteQualificationMutation,

  useGetMyEmergencyContactQuery,
  useCreateEmergencyContactMutation,
  useUpdateEmergencyContactMutation,
  useDeleteEmergencyContactMutation,

  useGetMyDocumentsQuery,
  useCreateDocumentMutation,
  useUpdateDocumentMutation,
  useDeleteDocumentMutation,
} = userApi;
