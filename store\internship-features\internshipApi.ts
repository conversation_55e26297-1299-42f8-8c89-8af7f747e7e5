import { IInternship } from '@/types/IInternship';
import { baseApi } from '../api/baseApi';
import { ApiResponse } from '@/types/IGobal';



export const internshipApi = baseApi
  .enhanceEndpoints({ addTagTypes: ['internship'] })
  .injectEndpoints({
    endpoints: builder => ({
      getAllInternships: builder.query<ApiResponse<{internships: IInternship[]}>, void>({
        query: () => '/internships',
        providesTags: ['internship'],
      }),
      getInternshipBySlug: builder.query<ApiResponse<IInternship>, string>({
        query:  (slug)=> `/internships/slug/${slug}`,
        providesTags: ['internship'],
      }),
      getInternshipById: builder.query<ApiResponse<IInternship>, string>({
        query:  (id)=> `/internships/id/${id}`,
        providesTags: ['internship'],
      }),
      createInternship: builder.mutation<ApiResponse<IInternship>, Partial<IInternship>>({
        query: body => ({
          url: '/internships',
          method: 'POST',
          body,
        }),
        invalidatesTags: ['internship'],
      }),
      updateInternship: builder.mutation<ApiResponse<IInternship>, Partial<IInternship>>({
        query: body => ({
          url: '/internships',
          method: 'PATCH',
          body,
        }),
        invalidatesTags: ['internship'],
      }),
      deleteInternship: builder.mutation<{ success: boolean }, string>({
        query: id => ({
          url: `/internships/${id}`,
          method: 'DELETE',
        }),
        invalidatesTags: ['internship'],
      }),
    }),

    overrideExisting: false,
  });

export const {
  useGetAllInternshipsQuery,
  useGetInternshipBySlugQuery,
  useGetInternshipByIdQuery,
  useCreateInternshipMutation,
  useUpdateInternshipMutation,
  useDeleteInternshipMutation,
} = internshipApi;
