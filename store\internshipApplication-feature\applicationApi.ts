// src/services/api/internshipApplicationApi.ts
import { baseApi } from "../api/baseApi";
import { ApiResponse } from "@/types/IGobal"; 
import { IInternshipApplication } from "@/types/IInternshipApplication"; 

interface params {
  internshipId: string;
  applicantId: string;
  status?: string;
}

interface GetMyApplicationsParams {
  page?: number;
  limit?: number;
}


// Define the request body types
interface CreateApplicationBody {
  internshipId: string;
  applicantId: string;
  resume?: string;
  coverLetter?: string;
  answers?: Array<{
    questionId: number;
    answer: string | string[];
  }>;
  status?: "draft" | "applied";
}




export const applicationApi = baseApi
  .enhanceEndpoints({ addTagTypes: ['internshipApplication'] })
  .injectEndpoints({
    endpoints: (builder) => ({

 getInternshipApplication: builder.query<ApiResponse<IInternshipApplication>, params
      >({
        query: ({ internshipId, applicantId, status }) => ({
          url: "/internshipApplications",
          params: { internshipId, applicantId, status },
        }),
        providesTags: ["internshipApplication"],
      }),

      // Get my internship applications (as applicant or employer) with pagination and filters
      getMyInternshipApplications: builder.query<ApiResponse<{
        applications: IInternshipApplication[];
        totalApplications: number;
        page: number;
        limit: number;
        totalPages: number;
      }>, GetMyApplicationsParams>({
        query: ({ page = 1, limit = 10 }) => ({
          url: "/internshipApplications/me",
          params: { page, limit },
        }),
        providesTags: ["internshipApplication"],
      }),

      // Get internship application by ID
      getInternshipApplicationById: builder.query<ApiResponse<IInternshipApplication>, string>({
        query: (id) => ({
          url: `/internshipApplications/${id}`,
          method: 'GET',
        }),
        providesTags: ["internshipApplication"],
      }),

 
      // Mutation for creating a new application (initial draft or direct apply)
      createInternshipApplication: builder.mutation<ApiResponse<IInternshipApplication>, CreateApplicationBody>({
        query: (body) => ({
          url: '/internshipApplications/',
          method: 'POST',
          body,
        }),
       
      }),
    }),
    overrideExisting: false,
  });

export const {
  useGetInternshipApplicationQuery,
  useGetMyInternshipApplicationsQuery,
  useCreateInternshipApplicationMutation,
} = applicationApi;