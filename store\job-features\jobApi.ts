import { IJob } from '@/types/IJob';
import { baseApi } from '../api/baseApi';
import { ApiResponse } from '@/types/IGobal';



export const jobApi = baseApi
  .enhanceEndpoints({ addTagTypes: ['job'] })
  .injectEndpoints({
    endpoints: builder => ({
      getAllJobs: builder.query<ApiResponse<{jobs: IJob[]}>, void>({
        query: () => '/jobs',
        providesTags: ['job'],
      }),
      getJobBySlug: builder.query<ApiResponse<IJob>, string>({
        query:  (slug)=> `/jobs/slug/${slug}`,
        providesTags: ['job'],
      }),
      createJob: builder.mutation<ApiResponse<IJob>, Partial<IJob>>({
        query: body => ({
          url: '/jobs',
          method: 'POST',
          body,
        }),
        invalidatesTags: ['job'],
      }),
      updateJob: builder.mutation<ApiResponse<IJob>, Partial<IJob>>({
        query: body => ({
          url: '/jobs',
          method: 'PATCH',
          body,
        }),
        invalidatesTags: ['job'],
      }),
      deleteJob: builder.mutation<{ success: boolean }, string>({
        query: id => ({
          url: `/jobs/${id}`,
          method: 'DELETE',
        }),
        invalidatesTags: ['job'],
      }),
    }),

    overrideExisting: false,
  });

export const {
  useGetAllJobsQuery,
  useGetJobBySlugQuery,
  useCreateJobMutation,
  useUpdateJobMutation,
  useDeleteJobMutation,
} = jobApi;
