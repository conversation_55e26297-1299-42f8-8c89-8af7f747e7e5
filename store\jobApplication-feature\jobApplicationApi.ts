// src/services/api/jobApplicationApi.ts
import { baseApi } from "../api/baseApi";
import { ApiResponse } from "@/types/IGobal";
import { IJobApplication } from "@/types/IJobApplication";



interface GetAllJobApplicationsParams {
  page?: number;
  limit?: number;
  status?: string;
  job?: string;
  applicant?: string;
  sort?: string;
  includeDeleted?: boolean;
}

// Define the request body types
interface CreateJobApplicationBody {
  job: string;
  applicant: string;
  resume?: string;
  coverLetter?: string;
  answers?: Array<{
    questionId: number;
    answer: string | string[];
  }>;
  status?: "draft" | "applied";
}

export const jobApplicationApi = baseApi
  .enhanceEndpoints({ addTagTypes: ['jobApplication'] })
  .injectEndpoints({
    endpoints: (builder) => ({

      // Get my job applications (as applicant) - matches getMyApplications controller
      getMyJobApplications: builder.query<ApiResponse<{
        jobApplications: IJobApplication[];
      }>, void>({
        query: () => ({
          url: "/jobApplications/me",
          method: 'GET',
        }),
        providesTags: ["jobApplication"],
      }),

      // Get all job applications with pagination and filters - matches getAllApplications controller
      getAllJobApplications: builder.query<ApiResponse<{
        applications: IJobApplication[];
        pagination: {
          currentPage: number;
          totalPages: number;
          totalCount: number;
          limit: number;
          hasNextPage: boolean;
          hasPrevPage: boolean;
          nextPage: number | null;
          prevPage: number | null;
        };
      }>, GetAllJobApplicationsParams>({
        query: ({ page = 1, limit = 10, status, job, applicant, sort = "-createdAt", includeDeleted = false }) => ({
          url: "/jobApplications",
          params: { page, limit, status, job, applicant, sort, includeDeleted },
        }),
        providesTags: ["jobApplication"],
      }),

      // Get job application by ID
      getJobApplicationById: builder.query<ApiResponse<IJobApplication>, string>({
        query: (id) => ({
          url: `/jobApplications/${id}`,
          method: 'GET',
        }),
        providesTags: ["jobApplication"],
      }),

      // Create new job application - matches createApplication controller
      createJobApplication: builder.mutation<ApiResponse<IJobApplication>, CreateJobApplicationBody>({
        query: (body) => ({
          url: '/jobApplications',
          method: 'POST',
          body,
        }),
        invalidatesTags: ["jobApplication"],
      }),
    }),
    overrideExisting: false,
  });

export const {
  useGetMyJobApplicationsQuery,
  useGetAllJobApplicationsQuery,
  useGetJobApplicationByIdQuery,
  useCreateJobApplicationMutation,
} = jobApplicationApi;