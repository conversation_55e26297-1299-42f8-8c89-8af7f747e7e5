export interface ICompany {
  _id: string;
  name: string;
  organization: string;
  slug: string;
  description: string;
  phones: ICompanyPhone[];
  emails: ICompanyEmail[];
  websites: ICompanyWebsite[];
  socialMedia: ICompanySocialMedia[];
  category: string[];
  industry: string;
  sector?: string;
  companySize: string;
  founded: Date; 
  country: string;

  gst?: string;
  pan?: string;
  tan?: string;
  cin?: string;
  isStartupIndiaRegistered?: boolean;

  certificates: ICompanyCertificate[];
  documents: ICompanyDocument[];
  moreInformation: ICompanyMoreInformation[];

  logo?: string; 
  coverImage?: string; 

  folders: string[]; 
  defaultAssetFolder?: string; 

  addresses: ICompanyAddressEntry[];
  contactPersons: string[]; 

  isDeleted?: boolean;
  deletedBy?: string; 
  createdBy: string; 
  updatedBy: string; 
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
}


interface ICompanyPhone {
  phone: string;
  label?: string;
  purpose?: string;
  isVerified?: boolean;
  isDefault?: boolean;
  sortOrder?: number;
}



interface ICompanyEmail {
  email: string;
  label?: string;
  purpose?: string;
  isVerified?: boolean;
  isDefault?: boolean;
  sortOrder?: number;
}




interface ICompanyWebsite {
  website: string;
  label?: string;
  purpose?: string;
  isVerified?: boolean;
  isDefault?: boolean;
  sortOrder?: number;
}



interface ICompanyCertificate {
  name: string;
  certificateID: string;
  issuedBy: string;
  issueDate: Date; // Consider `string` if this comes as an ISO date string from the API
  expiryDate?: Date; // Consider `string` if this comes as an ISO date string from the API
  file: string; // Changed from Types.ObjectId to string
  sortOrder?: number;
}




interface ICompanyDocument {
  file: string; 
  documentName: string;
  isVerified?: boolean;
}

interface ICompanyAddressEntry {
  address: {
    city: string;
    state: string;
    country: string;
    street?: string;
  };
  isDefault?: boolean;
  sortOrder?: number;
}


interface ICompanySocialMedia {
  platform: string;
  value: string;
}



interface ICompanyMoreInformation {
  title: string;
  value: string;
}