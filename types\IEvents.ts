export interface IEventQuestion {
  question: string;
  hint?: string;
  isRequired?: boolean;
  questionType: "text" | "textarea" | "radio" | "checkbox" | "dropdown";
  options?: string[];
  questionId: number;
}

export interface IEvent {
  _id: string;
  title: string;
  slug: string;
  description: string;
  tags?: string[];
  hostedByUsers?: Array<{
    user: {
      _id: string;
      firstName: string;
      lastName: string;
      email: string;
    };
    sortOrder: number;
  }>;
  hostedByCompanies?: Array<{
    user: {
      _id: string;
      name: string;
    };
    sortOrder: number;
  }>;
  hostName?: string;
  hostDesignation?: string;
  hostImage?: {
    _id: string;
    url: string;
  };
  bannerImage?: {
    _id: string;
    url: string;
  };
  language?: string;
  category?: string;
  level?: 'Beginner' | 'Intermediate' | 'Advanced';
  mode: 'Online' | 'Offline';
  platform?: string;
  meetingLink?: string;
  location?: string;
  timezone?: string;
  startTime: string;
  endTime?: string;
  durationMinutes?: number;
  questions: IEventQuestion[];
  isPaid: boolean;
  price?: number;
  currency?: string;
  registrationDeadline?: string;
  capacity?: number;
  registeredUsers: Array<{
    _id: string;
    firstName?: string;
    lastName?: string;
    email?: string;
  }>;
  certificateOffered: boolean;
  resourcesProvided?: boolean;
  recordingAccess?: boolean;
  isActive: boolean;
  isApproved: boolean;
  isFeatured?: boolean;
  createdBy: {
    _id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  createdAt: string;
  updatedAt: string;
};