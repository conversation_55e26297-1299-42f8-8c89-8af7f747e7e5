interface Location {
  address: string;
  label: string;
  city: string;
  state: string;
  country: string;
}

interface Duration {
  min: number;
  max: number;
  frequency: string;
}

interface Company {
  _id: string;
  name: string;
  organization: string;
  companySize: string;
}

interface Perk {
  title: string;
  sortOrder: number;
}

interface Skill {
  skill: string;
  level: 'beginner' | 'intermediate' | 'advanced';
  sortOrder: number;
}

interface Responsibility {
  title: string;
  sortOrder: number;
}

interface SelectionStage {
  stage: string;
  description: string;
  sortOrder: number;
}

interface InterviewRound {
  roundTitle: string;
  description: string;
  sortOrder: number;
}

interface Question {
  questionId: number;
  question: string;
  isRequired: boolean;
  questionType: 'text' | 'textarea' | 'radio' | 'checkbox';
  hint?: string;
  options: string[];
}

interface CustomFields {
  project_budget: number;
  team_size: number;
}

export interface IInternship {
  _id: string;
  title: string;
  slug: string;
  description: string;
  category: string;
  location: Location;
  duration: Duration;
  company: Company;
  createdBy: string;
  workPlaceType: string;
  mode: string;
  stipend: string;
  tags: string[];
  banner: string;
  workingHours: string;
  workingDays: string[];
  preferredLanguages: string[];
  perks: Perk[];
  eligibilityCriteria: string[];
  qualification: string[];
  skills: Skill[];
  responsibilities: Responsibility[];
  selectionProcess: SelectionStage[];
  interviews: InterviewRound[];
  questions: Question[];
  openings: number;
  applicationDeadline: string;
  customFields: CustomFields;
  status: string;
  isDeleted: boolean;
  postedAt: string;
  createdAt: string;
  updatedAt: string;
}

