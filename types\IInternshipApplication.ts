export interface IInternshipApplication {
  _id: string;
  Internship: string | { _id: string; title: string; company: string; location: string; }; // Can be populated or just ID
  applicant: string | { _id: string; name: string; email: string; }; // Can be populated or just ID
  resume?: string | { _id: string; filename: string; url: string; }; // Can be populated or just ID
  coverLetter?: string;
  answers: Array<{ questionId: number; answer: string | string[]; }>;
  status: "draft" | "applied" | "under-review" | "shortlisted" | "interview" | "offered" | "hired" | "rejected" | "withdrawn";
  reviewLogs: Array<{
    reviewer: string;
    action: string;
    timestamp: Date;
    notes?: string;
  }>;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
}

