export interface IJob {
  _id:string;
  title: string;
  slug: string;
  description: string;
  category: string;
  company: {
    _id: string;
    name: string;
    organization: string;
  };
  createdBy: string;
  probation: {
    max: number;
    min: number;
    frequency: "days" | "weeks" | "months" | "years" | string;
  };
  workPlaceType: "full-time" | "part-time" | "contract";
  mode: "onsite" | "remote" | "hybrid";

  location: {
    address: string; 
    label?: string;
    city?: string;
    state?: string;
    country?: string;
  };

  salaryRange?: string;
  tags: string[];
  banner?: string; 
  workingHours?: string;
  preferredLanguages: string[];
  workingDays: string[];
  perks: IJobPerk[];
  eligibilityCriteria: string[];
  qualification: string[];
  skills: IJobSkill[];
  responsibilities: IResponsibility[];
  selectionProcess: ISelectionProcess[];
  interviews: IInterviewRound[];
  questions: IJobQuestion[];

  openings: number;
  applicationDeadline: Date; 
  customFields?: Record<string, any>;
  status:
    | "draft"
    | "live"
    | "on-hold"
    | "deadline-passed"
    | "position-filled"
    | "closed";

  isDeleted?: boolean;
  deletedBy?: string; 
  deletedAt?: Date; 
  deletionReason?: string;

  postedAt?: Date; 
  updatedBy?: string; 
}


interface IJobQuestion {
  question: string;
  hint?: string;
  isRequired?: boolean;
  questionType: "text" | "textarea" | "radio" | "checkbox" | "dropdown";
  options?: string[];
  questionId?: number;
}


interface IInterviewRound {
  roundTitle: string;
  description?: string;
  sortOrder?: number;
}



interface ISelectionProcess {
  stage: string;
  description?: string;
  sortOrder?: number;
}



interface IResponsibility {
  title: string;
  sortOrder?: number;
}


interface IJobSkill {
  skill: string;
  level?: "beginner" | "intermediate" | "advanced" | "expert";
  sortOrder?: number;
}


interface IJobPerk {
  title: string;
  sortOrder?: number;
}