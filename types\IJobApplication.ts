export interface IJobApplication {
  _id: string;
  job: string | { _id: string; title: string; company: string; location: string; }; // Can be populated or just ID
  applicant: string | { _id: string; firstName: string; lastName: string; email: string; }; // Can be populated or just ID

  resume: string | { _id: string; filename: string; url: string; }; // Can be populated or just ID
  coverLetter: string;

  answers: IJobAnswer[];

  status:
    | "draft"
    | "applied"
    | "under-review"
    | "shortlisted"
    | "interview"
    | "offered"
    | "hired"
    | "rejected"
    | "withdrawn";

  reviewLogs: IReviewLog[];
  rejectedBy: string;
  withdrawnAt: Date;
  interviews: string[];

  offerLetter: string;

  isDeleted: boolean;
  deletedAt: Date;
  deletedBy: string

  createdAt: string;
  updatedAt: string;
}


export interface IReviewLog {
  reviewerNotes: string;
  reviewedBy: string;
  reviewedAt: Date;
  visibility: "private" | "public";
}


export interface IJobAnswer {
  questionId: number;
  answer: string | string[];
}
