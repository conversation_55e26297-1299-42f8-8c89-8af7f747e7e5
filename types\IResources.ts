export interface IVideoResource {
  _id: string;
  title: string;
  description?: string;
  videoUrl: string;
  platform: "YouTube" | "Vimeo" | "Other";
  category: string;
  subcategory: string;
  tags?: string[];
  level: "Beginner" | "Intermediate" | "Advanced";
  recommendedBy: string[];
  isVerified?: boolean;
  isFeatured?: boolean;
  benefits?: string[];
  importanceLevel?: "Core" | "Reference" | "Bonus";
  moderationStatus: "pending" | "approved" | "rejected";
  visible: boolean;
  thumbnailUrl?: string;
  duration?: string;
  uploadedAt?: Date;
  createdBy?: string;
  updatedBy?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface IDocumentResource {
  _id: string;
  title: string;
  description?: string;
  fileUrl: string;
  type: "PDF" | "eBook" | "Handwritten Note";
  category: string;
  subcategory: string;
  tags?: string[];
  level: "Beginner" | "Intermediate" | "Advanced";
  benefits?: string[];
  recommendedBy: string[];
  isVerified?: boolean;
  isFeatured?: boolean;
  importanceLevel?: "Core" | "Reference" | "Bonus";
  moderationStatus: "pending" | "approved" | "rejected";
  visible: boolean;
  downloadCount: number;
  thumbnailUrl?: string;
  createdBy?: string;
  updatedBy?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ICourseResource {
  _id: string;
  title: string;
  description?: string;
  courseUrl: string;
  platform: "Udemy" | "Coursera" | "edX" | "YouTube" | "LinkedIn Learning" | "Other";
  isPaid: boolean;
  price?: number;
  category: string;
  subcategory: string;
  tags?: string[];
  level: "Beginner" | "Intermediate" | "Advanced";
  benefits?: string[];
  duration?: string;
  recommendedBy: string[];
  isVerified?: boolean;
  isFeatured?: boolean;
  importanceLevel?: "Core" | "Reference" | "Bonus";
  moderationStatus: "pending" | "approved" | "rejected";
  visible: boolean;
  thumbnailUrl?: string;
  createdBy?: string;
  updatedBy?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface IArticleResource {
  _id: string;
  title: string;
  description?: string;
  url: string;
  type: "Blog" | "Coding Article" | "GitHub Repository";
  platform?: string;
  category: string;
  subcategory: string;
  tags?: string[];
  level: "Beginner" | "Intermediate" | "Advanced";
  benefits?: string[];
  recommendedBy: string[];
  isVerified?: boolean;
  isFeatured?: boolean;
  importanceLevel?: "Core" | "Reference" | "Bonus";
  moderationStatus: "pending" | "approved" | "rejected";
  visible: boolean;
  thumbnailUrl?: string;
  createdBy?: string;
  updatedBy?: string;
  createdAt: Date;
  updatedAt: Date;
}