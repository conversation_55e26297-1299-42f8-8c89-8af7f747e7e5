export interface IUser {
  _id: string;
  firstName: string;
  lastName: string;
  phone: string;
  email: string;
  userType: 'student' | 'employer' | 'admin' | string;
  providers: { name: string; id: string; email?: string; photo?: string }[];

  isPhoneVerified: boolean;
  isEmailVerified: boolean;
  password?: string;
  refreshToken?: string;
  verifyToken?: string;
  verifyTokenExpires?: Date;
  resetPasswordRequestCount?: number;
  lastLogin?: Date;

  permanentAddress?: string;
  presentAddress?: string;
  address?: string;
  profileImage?: string;
  gender?: string;
  dob?: Date;
  fatherName?: string;
  motherName?: string;
  maritalStatus?: string;

  identityDocs?: string[];
  emergencyContacts?: string[];
  qualifications?: string[];
  certificates?: string[];
  experiences?: string[];
  skills?: string[];
  importantLinks?: string[];

  status: 'active' | 'block' | 'hold';
  assets?: string[];
  folders?: string[];
  defaultAssetFolder?: string;
  invites?: string[];

  isDeleted?: boolean;
  deletedAt?: Date;
  deletedBy?: string;
  createdBy: string;
  updatedBy: string;
  isActive?: boolean;
  roles?: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface ISkill {
  _id: string;
  user: string;
  name: string;
  level: 'beginner' | 'intermediate' | 'advanced' | 'expert' | string;
  monthsOfExperience: number;
  sortOrder: number;
  createdAt: string; // ISO timestamp
  updatedAt: string; // ISO timestamp
}

export interface IQualification {
  _id: string;
  user: string;
  instituteName: string;
  degree: string;
  grade: string; // now free text
  fieldOfStudy: string;
  startDate: Date;
  endDate?: Date;
  description: string;
  files: string[]; // Refers to Asset
  isDeleted?: boolean;
  deletedAt?: Date;
  deletedBy?: string;
  createdBy: string;
  updatedBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface IImportantLink {
  _id: string;
  user: string;
  title: string;
  url: string;
  type?: 'portfolio' | 'github' | 'linkedin' | 'project' | 'other' | string;
  isVerified?: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface IIdentityDocument {
  _id: string;
  user: string;
  type: string; // e.g. Aadhaar, PAN, Passport
  value: string;
  isVerified?: boolean;
  status?: string;
  files?: string[]; // Refers to Asset
  isDeleted?: boolean;
  deletedAt?: Date;
  deletedBy?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface IExperience {
  _id: string;
  user: string;
  designation: string;
  employmentType:
    | 'full-time'
    | 'part-time'
    | 'internship'
    | 'freelance'
    | 'contract'
    | 'remote'
    | 'other'
    | string;
  company: string;
  startDate: Date;
  endDate?: Date;
  location: string;
  locationType: 'on-site' | 'remote' | 'hybrid' | string;
  description: string;
  files: string[]; // Refers to Asset
  inHandSalary?: number;
  isDeleted?: boolean;
  deletedAt?: Date;
  deletedBy?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface IEmergencyContact {
  _id: string;
  user: string;
  name: string;
  relation: string;
  phone: string;
  alternatePhone?: string;
  email: string;
  isPrimary: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface ICertificate {
  _id: string;
  user: string;
  instituteName: string;
  certificateName: string;
  certificateType?: 'course' | 'award' | 'achievement' | 'other';
  issueDate: Date;
  expiryDate?: Date;
  description: string;
  verificationUrl?: string;
  files: string[];
  createdAt: Date;
  updatedAt: Date;
}
